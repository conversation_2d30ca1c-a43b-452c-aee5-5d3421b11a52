@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #0072b3;
  --color-primary-hover: #005a8f;
  --color-secondary: #ffffff;
  --color-secondary-hover: #f3f4f6;
}

@layer components {
  .btn-primary {
    @apply bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-hover transition-colors;
  }
  
  .link-primary {
    @apply text-gray-700 hover:text-primary transition-colors;
  }
}

/* Define the border shine animation */
@keyframes borderShine {
  0% {
    border-color: #05668D; /* Starting color */
    box-shadow: 0 0 5px rgba(5, 102, 141, 0.5); /* Subtle glow */
  }
  50% {
    border-color: #00A7B5; /* Midpoint color for shine effect */
    box-shadow: 0 0 10px rgba(0, 167, 181, 0.8); /* Brighter glow */
  }
  100% {
    border-color: #05668D; /* Back to start */
    box-shadow: 0 0 5px rgba(5, 102, 141, 0.5);
  }
}

.ai-border-shine {
  border: 2px solid #05668D; /* Base border */
  animation: borderShine 1.5s ease-in-out infinite; /* Smooth border-only animation */
}