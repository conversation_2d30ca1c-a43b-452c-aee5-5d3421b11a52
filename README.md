# OnlyC2C

![Deployment](https://github.com/Judyjose008/onlyc2c-candidate-frontend/actions/workflows/deploy.yml/badge.svg)

> __CRON status__
>
> ![Sitemap Submission](https://github.com/Judyjose008/onlyc2c-candidate-frontend/actions/workflows/sitemap.yml/badge.svg)  
> ![Close Old Jobs](https://github.com/Judyjose008/onlyc2c-candidate-frontend/actions/workflows/close-jobs.yml/badge.svg)

Platform to find only C2C jobs!.

***

## Running the project

1. Install npm dependencies with:

    ```bash
    npm ci
    ```

2. Start server with:

    ```bash
    npm run dev
    ```

***

## Deployment

This project uses GitHub Actions to automatically deploy to a Linode server when changes are pushed to the main branch.

### Required GitHub Secrets

The following secrets need to be set in the GitHub repository settings:

- `LINODE_SSH_KEY`: The private SSH key for connecting to the Linode server
- `LINODE_HOST`: The IP address or hostname of the Linode server
- `LINODE_USER`: The username for SSH access to the Linode server

### Manual Deployment

To manually deploy the application to the Linode server:

1. SSH into the server
2. Navigate to the project directory
3. Run the deployment script:

    ```bash
    ./scripts/deploy.sh
    ```

***

## Generating Keys for `jose`

1. Generate public and private keys with

    ```sh
    openssl genrsa -out privateKey.pem 2048

    openssl rsa -in privateKey.pem -pubout -out publicKey.pem
    ```

2. Generate JWKS for public and private key from [JWK Converter](https://pem2jwk.vercel.app/).

3. Add the generated jwks object as json encoded string into corresponding variables `PRIVATE_JWK`, `PUBLIC_JWK`.

> __NOTE:__
> Refer `./src/lib/jwt.ts` for jwt token genration and verfication implementation.
>
> [Reference Code](https://github.com/panva/jose/blob/main/docs/jwt/sign/classes/SignJWT.md)

***

## Search Indexes in Mongo DB

1. Search Index: __jobs-search-index__

    ```json
    {
        "mappings": {
            "dynamic": false,
            "fields": {
                "meta_keywords": [
                    { "type": "string" },
                    { "type": "autocomplete" },
                    { "type": "token" }
                ]
            }
        }
    }
    ```
