/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#0072b3',
          hover: '#005a8f',
        },
        secondary: {
          DEFAULT: '#ffffff',
          hover: '#f3f4f6',
        },
        customBlue: {
          DEFAULT: '#05668D',
          light: '#7480e7',
        },
      },
      animation: {
        'marquee-left': 'marquee-left 1000s linear infinite',
        'marquee-right': 'marquee-right 700s linear infinite',
        'loading-bar': 'loading 2s infinite',
        'marquee-diagonal-1': 'marquee-diagonal-1 80s linear infinite',
        'marquee-diagonal-2': 'marquee-diagonal-2 65s linear infinite',
        'marquee-diagonal-3': 'marquee-diagonal-3 70s linear infinite',
      },
      keyframes: {
        'marquee-left': {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' }
        },
        'marquee-right': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' }
        },
        loading: {
          '0%': {
            left: '-20%',
            width: '20%'
          },
          '50%': {
            left: '40%',
            width: '40%'
          },
          '100%': {
            left: '100%',
            width: '20%'
          }
        },
        'marquee-diagonal-1': {
          '0%': { transform: 'translateX(0%) translateY(0%)' },
          '100%': { transform: 'translateX(100%) translateY(-20%)' },
        },
        'marquee-diagonal-2': {
          '0%': { transform: 'translateX(0%) translateY(0%)' },
          '100%': { transform: 'translateX(-100%) translateY(25%)' },
        },
        'marquee-diagonal-3': {
          '0%': { transform: 'translateX(0%) translateY(0%)' },
          '100%': { transform: 'translateX(100%) translateY(-15%)' },
        },
      },
    },
  },
  plugins: [],
}; 