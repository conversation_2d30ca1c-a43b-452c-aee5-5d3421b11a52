import { NextRequest, NextResponse } from "next/server";
import { getTokenFromCookieOrRequest, verifyJwtToken } from "../server/lib/jwt";
import BaseResponse from '../server/core/BaseResponse';
import { USER_ID_HEADER, USER_NAME_HEADER, USER_PROFILE_ID_HEADER, USER_EMAIL_HEADER, USER_SCOPE_HEADER } from "../server/utils/requestUtils";

// Define public API routes that don't require authentication
export const publicApiRoutes = [
    // GET  /api/v1/jobs
    // GET  /api/v1/jobs/:id
    { path: /^\/api\/v1\/jobs(?:\/[a-f\d]+)?$/, methods: ['GET'] },

    // POST  /api/v1/jobs/search
    { path: /^\/api\/v1\/jobs\/search$/, methods: ['POST'] },

    // GET  /api/v1/old-jobs
    // GET  /api/v1/old-jobs/:id
    { path: /^\/api\/v1\/old-jobs(?:\/[a-zA-Z0-9-]+)?$/, methods: ['GET'] },

    // GET  /api/v1/locality
    // GET  /api/v1/locality/:id
    // POST /api/v1/locality
    { path: /^\/api\/v1\/locality(?:\/[a-f\d]+)?$/, methods: ['GET', 'POST'] },

    // GET  /api/v1/organizations
    // GET  /api/v1/organizations/:id
    { path: /^\/api\/v1\/organizations(?:\/[a-f\d]+)?$/, methods: ['GET'] },

    // GET  /api/v1/recruiters
    // GET  /api/v1/recruiters/:id
    { path: /^\/api\/v1\/recruiters(?:\/[a-f\d]+)?$/, methods: ['GET'] },

    // GET  /api/v1/skills
    // GET  /api/v1/skills/:id
    // POST /api/v1/skills
    // POST /api/v1/skills/batch
    { path: /^\/api\/v1\/skills(?:\/([a-f\d]|batch)+)?$/, methods: ['GET', 'POST'] },

    // GET  /api/v1/subscriptions
    { path: /^\/api\/v1\/subscriptions$/, methods: ['POST'] },

    // GET  /api/v1/jobs/external
    // GET  /api/v1/jobs/external/:id
    // POST /api/v1/jobs/external
    { path: /^\/api\/v1\/jobs\/external(?:\/[a-f\d]+)?$/, methods: ['GET', 'POST'] },
];

/**
 * Backend middleware to protect API routes
 * Returns unauthorized response for unauthenticated requests
 */
export async function backendMiddleware(request: NextRequest) {
    const url = request.nextUrl.pathname;
    const method = request.method;

    // Check if route is public
    if (publicApiRoutes.some(p => p.path.test(url) && p.methods.includes(method))) {
        return NextResponse.next({ request });
    }

    console.log(`API Middleware: ${url}`);

    // Check for authentication token
    const token = await getTokenFromCookieOrRequest(request);
    if (!token) {
        return BaseResponse.unauthorized();
    }

    try {
        // Verify token and set user headers
        const payload = await verifyJwtToken(token);
        if (!payload) throw new Error('Invalid token');

        request.headers.set(USER_ID_HEADER, payload.sub);
        request.headers.set(USER_NAME_HEADER, payload.name);
        request.headers.set(USER_PROFILE_ID_HEADER, payload.profile_id);
        request.headers.set(USER_EMAIL_HEADER, payload.email);
        request.headers.set(USER_SCOPE_HEADER, payload.scope);
    } catch (error) {
        console.log(error);
        return BaseResponse.unauthorized();
    }

    return NextResponse.next({ request });
}
