import { NextRequest, NextResponse } from "next/server";
import { getTokenFromCookieOrRequest, verifyJwtToken } from "../server/lib/jwt";
import { UserScope } from "../server/enums";

/**
 * Frontend middleware to protect dashboard routes
 * Redirects unauthenticated users to login page with callback URL
 */
export async function frontendMiddleware(request: NextRequest) {
    const url = request.nextUrl.pathname;
    console.log(`Frontend Middleware: ${url}`);

    // Check for authentication token
    const token = await getTokenFromCookieOrRequest(request);
    if (!token) {
        // Redirect to login with callback URL
        return redirectToLogin(request);
    }

    try {
        // Verify token
        const payload = await verifyJwtToken(token);
        if (!payload) throw new Error('Invalid token');

        // Token is valid, allow access
        return NextResponse.next();
    } catch (error) {
        console.log('Auth error:', error);
        // Redirect to login with callback URL on invalid token
        return redirectToLogin(request);
    }
}

function redirectToLogin(request: NextRequest) {
    const callbackUrl = encodeURIComponent(request.nextUrl.pathname + request.nextUrl.search);
    const url = request.nextUrl.pathname;

    let redirectUrl = `/login?callbackUrl=${callbackUrl}`;
    if (url.startsWith('/dashboard')) {
        redirectUrl += `&role=${UserScope.RECRUITER}`;
    }
    if (url.startsWith('/my-applications')) {
        redirectUrl += `&role=${UserScope.CANDIDATE}`;
    }

    return NextResponse.redirect(new URL(redirectUrl, request.url));
}
