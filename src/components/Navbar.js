'use client';
import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import { ChevronRight, ArrowRight, LayoutDashboardIcon, PenBox } from "lucide-react";
import { signOut } from "next-auth/react";
import { useUser } from '@/context/UserContext';


export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const { userAvailableInLocalStorage, updateUserAvailability } = useUser();
  const [userDetails, setUserDetails] = useState(null);
  const dropdownRef = useRef(null);


  useEffect(() => {
    const user = localStorage.getItem('userDetails');
    const userDetails = JSON.parse(user);
    setUserDetails(userDetails);
  }, [userAvailableInLocalStorage]);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsProfileOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle initial mount and dark mode
  useEffect(() => {
    setMounted(true);
    // Set light mode as default if no preference is stored
    if (!('theme' in localStorage)) {
      localStorage.theme = 'light';
      document.documentElement.classList.remove('dark');
      setDarkMode(false);
    } else {
      // Use stored preference
      const isDark = localStorage.theme === 'dark';
      setDarkMode(isDark);
      if (isDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }, []);

  // Skip rendering until everything is loaded
  if (!mounted || status === "loading") {
    return (
      <nav className="fixed top-0 w-full bg-customBlue-light dark:bg-customBlue-light z-50 transition-colors duration-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="text-2xl font-bold text-primary dark:text-white">
                <img
                  src={darkMode ? "/assets/images/logo_white.png" : "/assets/images/logo_blue.png"}
                  alt="Onlyc2c logo"
                  className="h-8"
                />
              </div>
            </div>
            {/* Show skeleton loader for nav items */}
            <div className="hidden md:flex items-center space-x-6">
              <div className="w-32 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
            </div>
          </div>
        </div>
      </nav>
    );
  }

  const toggleTheme = () => {
    if (darkMode) {
      document.documentElement.classList.remove('dark');
      localStorage.theme = 'light';
      setDarkMode(false);
    } else {
      document.documentElement.classList.add('dark');
      localStorage.theme = 'dark';
      setDarkMode(true);
    }
  };

  const handleSignOut = () => {
    localStorage.removeItem('userDetails');
    updateUserAvailability();
    signOut({ callbackUrl: '/login' });
    setUserDetails(null);
  };

  const renderAuthenticatedNav = () => {
    if (userDetails?.role === 'recruiter') {
      return (
        <Link
          href="/dashboard"
          className="group px-6 py-2 rounded-full bg-white hover:bg-gray-100 text-customBlue font-medium transition duration-200 flex items-center"
        >
          <LayoutDashboardIcon className="w-4 h-4 mr-2" />
          Dashboard
        </Link>
      );
    }
    return (
      <Link
        href="/my-applications"
        className="group px-6 py-2 rounded-full bg-white hover:bg-gray-100 text-customBlue font-medium transition duration-200 flex items-center"
      >
        <PenBox className="w-4 h-4 mr-2" />
        My Applications
      </Link>
    );
  };

  return (
    <nav className="fixed top-0 w-full bg-customBlue-light dark:bg-customBlue-light z-50 transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="text-2xl font-bold text-primary dark:text-white flex items-center">
              <img
                src="/assets/images/logo_white.png"
                alt="Onlyc2c logo"
                className="h-8"
              />
            </Link>
          </div>
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <Link
              href="https://blog.onlyc2c.com/avoid_7_mistakes/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-white dark:text-gray-300 hover:text-gray-200 dark:hover:text-white transition-colors duration-200 flex items-center"
            >
              <span className="mr-1"></span>
              Blogs
            </Link>
            {userAvailableInLocalStorage ? (
              <>
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setIsProfileOpen(!isProfileOpen)}
                    className="flex items-center space-x-2 focus:outline-none"
                  >
                    <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-[#05668D] dark:border-[#05668D]/80">
                      {userDetails?.user?.image ? (
                        <>
                          <img
                            src={userDetails?.user.image}
                            alt="Profile"
                            className="w-full h-full object-cover"
                          />
                        </>
                      ) : (
                        <div className="w-full h-full bg-[#fff] dark:bg-[#fff] flex items-center justify-center text-customBlue dark:text-customBlue">
                          {userDetails?.user?.name?.charAt(0) || userDetails?.user?.email?.charAt(0)}
                        </div>
                      )}
                    </div>
                  </button>

                  {/* Profile Dropdown */}
                  {isProfileOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 border border-gray-200 dark:border-gray-700">
                      <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{userDetails?.user?.name}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{userDetails?.user?.email}</p>
                      </div>
                      <button
                        onClick={handleSignOut}
                        className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
                      >
                        Sign out
                      </button>
                    </div>
                  )}
                </div>
                {renderAuthenticatedNav()}
              </>
            ) : (
              <>
                <Link
                  href="/login"
                  className="group text-white dark:text-gray-300 flex items-center hover:text-gray-200 dark:hover:text-white transition-colors duration-200"
                >
                  Need an account
                  <ChevronRight className="w-4 h-4 ml-1 block group-hover:hidden transition-transform duration-1000 ease-in-out" />
                  <ArrowRight className="w-4 h-4 ml-1 hidden group-hover:block transition-transform duration-1000 ease-in-out" />
                </Link>
                <Link
                  href="/post-job"
                  className="group px-6 py-2 rounded-full bg-white hover:bg-gray-100 text-customBlue font-medium transition duration-200 flex items-center"
                >
                  Post Jobs
                  <ChevronRight className="w-4 h-4 ml-1 block group-hover:hidden transition-transform duration-1000 ease-in-out" />
                  <ArrowRight className="w-4 h-4 ml-1 hidden group-hover:block transition-transform duration-1000 ease-in-out" />
                </Link>
              </>
            )}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg text-white hover:text-gray-200 transition-colors duration-200"
              aria-label="Toggle theme"
            >
              {darkMode ? (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                  />
                </svg>
              )}
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-4">
            {/* Theme Toggle Button for Mobile */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg text-white hover:text-gray-200 transition-colors duration-200"
              aria-label="Toggle theme"
            >
              {darkMode ? (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                  />
                </svg>
              )}
            </button>

            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-white hover:text-gray-200 transition-colors duration-200"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-customBlue-light dark:bg-customBlue-light transition-colors duration-300">
              {userAvailableInLocalStorage ? (
                <>
                  {userDetails?.role === 'recruiter' ? (
                    <Link
                      href="/dashboard"
                      className="block text-white hover:text-gray-200 py-2 transition-colors duration-200"
                    >
                      Dashboard
                    </Link>
                  ) : (
                    <Link
                      href="/my-applications"
                      className="block text-white hover:text-gray-200 py-2 transition-colors duration-200"
                    >
                      My Applications
                    </Link>
                  )}
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="block text-white hover:text-gray-200 py-2 transition-colors duration-200"
                  >
                    Sign in
                  </Link>
                  <Link
                    href="/login?role=recruiter"
                    className="block text-white hover:text-gray-200 py-2 transition-colors duration-200"
                  >
                    Post Jobs
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
} 