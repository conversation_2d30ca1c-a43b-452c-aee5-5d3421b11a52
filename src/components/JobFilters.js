'use client';

import { useState, useRef } from 'react';
import { Search, Loader2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Command } from 'lucide-react';

const JobFilters = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const searchInputRef = useRef(null);

    const handleSearch = () => {
        setIsLoading(true);
        if (searchQuery.trim()) {
            window.dispatchEvent(new CustomEvent('global-job-search', { detail: { query: searchQuery.trim() } }));
        }
        setIsLoading(false);
    };

    const handleKeyDown = (e) => {
        setIsLoading(true);
        if (e.key === 'Enter') {
            e.preventDefault();
            window.dispatchEvent(new CustomEvent('global-job-search', { detail: { query: searchQuery.trim() } }));
        }
        setIsLoading(false);
    };

    const handleSearchChange = (e) => {
        const newValue = e.target.value;
        setSearchQuery(newValue);

        // If the input becomes empty, trigger the search event
        if (!newValue) {
            window.dispatchEvent(new CustomEvent('global-job-search', { detail: { query: '' } }));
        }
    };

    const handleClearSearch = () => {
        setSearchQuery('');
        window.dispatchEvent(new CustomEvent('global-job-search', { detail: { query: '' } }));
        if (searchInputRef.current) {
            searchInputRef.current.focus();
        }
    };

    return (
        <div className="md:relative fixed md:bottom-auto bottom-0 left-0 right-0 md:left-auto md:right-auto md:w-[600px] md:mx-auto bg-gray-300/50 dark:bg-gray-800/50 p-4 rounded-xl md:rounded-xl rounded-b-none shadow-lg backdrop-blur-sm z-50">
            <div className="grid grid-cols-1 gap-2 max-w-screen-md mx-auto">
                <div className="relative">
                    <input
                        ref={searchInputRef}
                        type="text"
                        value={searchQuery}
                        onChange={handleSearchChange}
                        onKeyDown={handleKeyDown}
                        placeholder="Ask me about jobs... (e.g., 'Show me React developer jobs in New York')"
                        className="w-full pl-12 pr-24 p-4 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-base focus:ring-2 focus:ring-blue-500/50 focus:outline-none shadow-inner transition-all duration-300"
                    />
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                        <Sparkles className="w-5 h-5 text-blue-500 animate-pulse" />
                    </div>
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex gap-2">
                        {(searchQuery || isLoading) && (
                            <button
                                onClick={handleClearSearch}
                                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full transition-colors duration-200"
                            >
                                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        )}
                        {isLoading ? (
                            <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
                        ) : (
                            <button
                                onClick={handleSearch}
                                disabled={!searchQuery.trim()}
                                className={`p-2 rounded-lg relative overflow-hidden transition-all duration-300 ${searchQuery.trim()
                                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-blue-500/50'
                                    : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-lg hover:shadow-purple-500/50'
                                    }`}
                            >
                                <ArrowUp className="w-4 h-4 relative z-10" />
                                {searchQuery.trim() && (
                                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shine" />
                                )}
                            </button>
                        )}
                    </div>
                </div>
                {searchQuery.trim() && (
                    <div className="flex items-center justify-left gap-2 text-xs text-gray-500 dark:text-gray-400 opacity-0 animate-fade-in">
                        <span>Press</span>
                        <kbd className="px-1.5 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500">Enter</kbd>
                        <span>or click</span>
                        <ArrowUp className="w-3 h-3" />
                        <span>to search</span>
                    </div>
                )}
            </div>
            <style jsx>{`
                @keyframes shine {
                    from {
                        transform: translateX(-100%);
                    }
                    to {
                        transform: translateX(100%);
                    }
                }
                .animate-shine {
                    animation: shine 2s infinite;
                }
                @keyframes fadeIn {
                    from {
                        opacity: 0;
                        transform: translateY(-5px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-fade-in {
                    animation: fadeIn 0.2s ease-out forwards;
                }
            `}</style>
        </div>
    );
};

export default JobFilters;