"use client";

import { useState, useEffect, useRef } from 'react';
import SkillTag from './SkillTag';
import { Building2Icon, MapPin, Clock, ChevronUpIcon, ChevronDownIcon, BriefcaseIcon, UserCircle, Trash2Icon, CheckCircle, Copy, Check, ExternalLink, Trees, Car, Computer, XCircle } from 'lucide-react';
import CustomPhoneInput from './PhoneInput';
import { storeFile, getFile, deleteFile } from '@/utils/indexedDB';
import LoginCard from './LoginCard';
import { uploadResume } from '@/utils/resumeOps';
import { applyToJob } from '@/utils/jobApplicationsOps';
import { getDeterministicNumber } from '@/utils/stringOps';
import Image from 'next/image';
import { toast } from 'react-toastify';
import Link from 'next/link';

const formatRelativeTime = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  const diffInWeeks = Math.floor(diffInDays / 7);
  const diffInMonths = Math.floor(diffInDays / 30);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffInDays === 0) {
    return 'Today';
  } else if (diffInDays === 1) {
    return 'Yesterday';
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else if (diffInWeeks < 4) {
    return `${diffInWeeks} ${diffInWeeks === 1 ? 'week' : 'weeks'} ago`;
  } else if (diffInMonths < 12) {
    return `${diffInMonths} ${diffInMonths === 1 ? 'month' : 'months'} ago`;
  } else {
    return date.toLocaleDateString();
  }
};

const JobCard = ({ job, expanded = false }) => {
  const [isExpanded, setIsExpanded] = useState(expanded);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isPhoneValid, setIsPhoneValid] = useState(false);
  const [resume, setResume] = useState(null);
  const [error, setError] = useState('');
  const [showLoginCard, setShowLoginCard] = useState(false);
  const [resumeFileName, setResumeFileName] = useState('');
  const [userDetails, setUserDetails] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const phoneInputRef = useRef();
  const [selectedResumeId, setSelectedResumeId] = useState(null);

  // Check for existing resume when component mounts
  useEffect(() => {
    const checkExistingResume = async () => {
      try {
        const existingResume = await getFile('userResume');
        if (existingResume) {
          setResume(existingResume.file);
          setResumeFileName(existingResume.name);
        }
      } catch (err) {
        console.error('Error checking existing resume:', err);
      }
    };

    const checkExistingPhoneNumber = async () => {
      const savedApplication = localStorage.getItem('pendingApplication');
      if (savedApplication) {
        const parsedApplication = JSON.parse(savedApplication);
        setPhoneNumber(parsedApplication.phoneNumber);
        // Validate the loaded phone number
        if (parsedApplication.phoneNumber) {
          setIsPhoneValid(true); // Set validation state to true for pre-loaded number
        }
      }
    };

    const setUserDetailsIfLoggedIn = async () => {
      const userDetails = localStorage.getItem('userDetails');
      if (userDetails) {
        const parsedUserDetails = JSON.parse(userDetails);
        setUserDetails(parsedUserDetails);
      } else {
        setUserDetails(null);
      }
    }

    checkExistingResume();
    checkExistingPhoneNumber();
    setUserDetailsIfLoggedIn();
  }, []);

  const handlePhoneValidation = (isValid) => {
    setIsPhoneValid(isValid);
  };

  const handleResumeUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        await storeFile('userResume', file);
        setResume(file);
        setResumeFileName(file.name);
        setError('');
        setSelectedResumeId(null);
      } catch (err) {
        console.error('Error storing resume:', err);
        setError('Error uploading resume. Please try again.');
      }
    }
  };

  const handleDeleteResume = async () => {
    try {
      await deleteFile('userResume');
      setResume(null);
      setResumeFileName('');
      setSelectedResumeId(null);
    } catch (err) {
      console.error('Error deleting resume:', err);
      setError('Error deleting resume. Please try again.');
    }
  };

  const handleSelectResume = (resume) => {
    setResume(resume);
    setResumeFileName(resume.label);
    setSelectedResumeId(resume._id);
    setError('');
  };

  const handleApply = async () => {
    if (isSubmitting) return;
    setError('');

    // First validate phone number using the ref
    const isPhoneValid = phoneInputRef.current?.validate();
    setIsPhoneValid(isPhoneValid);

    if (!isPhoneValid) {
      return;
    }

    if (!resume) {
      setError('Please select or upload a resume');
      return;
    }

    setIsSubmitting(true); // Start loading

    if (userDetails) {
      try {
        let resumeId;
        if (resume instanceof File) {
          const resumeUploaded = await uploadResume(resume, resumeFileName);
          resumeId = resumeUploaded._id;
        } else {
          // Existing resume selected
          resumeId = resume._id;
        }
        const jobApplication = await applyToJob(job._id, resumeId);
        console.log('jobApplication', jobApplication);
        toast.success('Application submitted successfully');
      } catch (error) {
        console.error('Error applying to job:', error);
        toast.error('Error applying to job. Please try again later');
      } finally {
        setIsSubmitting(false); // End loading
      }
    } else {
      // Store current application data and show login card
      localStorage.setItem('pendingApplication', JSON.stringify({
        jobId: job._id,
        phoneNumber,
      }));
      setShowLoginCard(true);
      setIsSubmitting(false); // End loading
    }
  };

  const handleLoginSuccess = () => {
    // Handle post-login success
    setShowLoginCard(false);
    // You can add additional logic here to process the pending application
  };

  const handleCopyDescription = async () => {
    try {
      await navigator.clipboard.writeText(job.description);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  return (
    <div className="flex flex-col gap-4">
      {/* Main Job Card */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm hover:shadow-lg 
                  dark:shadow-none dark:hover:shadow-[0_0_15px_rgba(5,102,141,0.3)] 
                  border border-gray-100 dark:border-gray-700 
                  transition-all duration-300 hover:translate-y-[-2px]">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          {/* Left - Company Logo */}
          <div className="flex-shrink-0">
            {job.companyLogo ? (
              <div className="relative w-20 h-20">
                <Image
                  src={job.companyLogo}
                  alt={`${job.company} logo`}
                  fill
                  sizes="80px"
                  className="rounded-lg bg-gray-400 dark:bg-gray-700 p-2 object-contain"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextElementSibling.style.display = 'flex';
                  }}
                />
              </div>
            ) : (
              <div className="w-20 h-20 rounded-lg bg-gray-50 dark:bg-gray-700 p-2 flex items-center justify-center">
                <Building2Icon className="w-8 h-8 text-gray-400 dark:text-gray-500" />
              </div>
            )}
          </div>

          {/* Middle - Job Details */}
          <div className="flex-grow">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white hover:text-[#05668D] transition-colors flex items-center gap-2">
                {job.title}
                <Link target="_blank" href={`/c2c-jobid-${job.jobId}`}>
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </h3>
              {job.verified && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-800">
                  <CheckCircle className="w-3.5 h-3.5 mr-1" />
                  Verified
                </span>
              )}
            </div>

            <div className="flex flex-wrap items-center gap-2 mt-2 text-sm text-gray-900 dark:text-white">
              <div className="flex items-center">
                <Building2Icon className="w-4 h-4 mr-1 text-gray-900 dark:text-white" />
                {job.company}
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1 text-gray-900 dark:text-white" />
                {job.location}
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1 text-gray-900 dark:text-white" />
                Posted {formatRelativeTime(job.postedTime)}
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mt-3">
              {job.skills?.slice(0, 5).map((skill, index) => (
                <SkillTag key={index} skill={skill} />
              ))}
              {job.skills?.length > 5 && (
                <span
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 dark:text-gray-300 rounded-full cursor-pointer"
                >
                  +{job.skills.length - 5} more
                </span>
              )}
              {
                job.type === 'REMOTE' && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-200 dark:border-blue-800">
                    <Trees className="w-3.5 h-3.5 mr-1" />
                    Remote
                  </span>
                )
              }
              {
                job.type === 'HYBRID' && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 border border-purple-200 dark:border-purple-800">
                    <Car className="w-3.5 h-3.5 mr-1" />
                    Hybrid
                  </span>
                )
              }
              {
                job.type === 'ONSITE' && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-800">
                    <Computer className="w-3.5 h-3.5 mr-1" />
                    Onsite
                  </span>
                )
              }
            </div>
          </div>

          {/* Right - Apply Button */}
          <div className="flex-shrink-0 w-full md:w-auto flex flex-col items-center gap-2">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {job.applicants + getDeterministicNumber(job._id)} applicants
            </span>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="w-full md:w-auto bg-[#05668D] hover:bg-[#034963] text-white px-6 py-2 rounded-lg transition duration-300 flex items-center justify-center gap-2"
            >
              Apply Now
              {isExpanded ? (
                <ChevronUpIcon className="w-4 h-4" />
              ) : (
                <ChevronDownIcon className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Expanded Details Section */}
      {isExpanded && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 transition-colors duration-300">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column - Job Description */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Job Description</h4>
              <div className="relative p-4 rounded-lg prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-100 bg-gray-50 dark:bg-gray-700/50 group">
                <button
                  onClick={handleCopyDescription}
                  className="absolute top-2 right-2 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 bg-white dark:bg-gray-600 rounded-lg shadow-sm"
                  title="Copy to clipboard"
                >
                  {isCopied ? (
                    <Check className="w-4 h-4 text-green-500" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
                <p className="text-gray-600 dark:text-gray-300 whitespace-pre-wrap pr-8">
                  {job.description}
                </p>
              </div>
              {job?.skills.length > 0 && <div className="bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h4 className="font-medium mb-2 text-gray-800 dark:text-gray-200">All Skills:</h4>
                <div className="flex flex-wrap gap-x-2 text-sm text-gray-600 dark:text-gray-300">
                  {job.skills?.join(', ')}
                </div>
              </div>}
            </div>

            {/* Right Column - Recruiter Info & Application */}
            <div className="space-y-6">
              {/* Recruiter Info */}
              <div
                onClick={() => console.log('Navigate to recruiter profile:', job.recruiter?.id)}
                className="flex items-start gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200 cursor-pointer group"
              >
                {console.log(job)}
                {job.recruiterProfileImage ? (
                  <img
                    referrerPolicy="no-referrer"
                    className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 group-hover:border-[#05668D]"
                    src={job?.recruiterProfileImage}
                    alt={`${job?.recruiterName} || 'Recruiter's profile`}
                  />
                ) : (
                  <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center border-2 border-gray-200 dark:border-gray-600 group-hover:border-[#05668D]">
                    <UserCircle className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                  </div>
                )}
                <div>
                  <h5 className="font-semibold text-gray-900 dark:text-white">
                    {job?.recruiterName || "Recruiter Name"} <small className="text-xs text-gray-500 dark:text-gray-400">is hiring for this role!</small>
                  </h5>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {job?.recruiterEmail || "No email provided"}
                  </p>
                </div>
              </div>

              {/* Application Form */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Phone Number *
                  </label>
                  <CustomPhoneInput
                    ref={phoneInputRef}
                    phoneNumber={phoneNumber}
                    setPhoneNumber={setPhoneNumber}
                    onValidation={handlePhoneValidation}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {userDetails?.resumes?.length > 0 ? "Select Previous Resume" : "Upload Resume *"}
                  </label>

                  {/* Previous Resumes Section - Moved to top */}
                  {userDetails?.resumes?.length > 0 && (
                    <div className="mb-4">
                      <div className="grid gap-2">
                        {userDetails.resumes.map((resumeItem) => (
                          <div
                            key={resumeItem._id}
                            onClick={() => handleSelectResume(resumeItem)}
                            className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedResumeId === resumeItem._id
                              ? 'border-[#05668D] bg-blue-50 dark:bg-blue-900/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-[#05668D]/50'
                              }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-900 dark:text-white">
                                {resumeItem.label.replace(/_/g, ' ')}
                              </span>
                              <div className="text-right">
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {new Date(resumeItem.createdAt).toLocaleDateString()}
                                </span>
                                {selectedResumeId === resumeItem._id && (
                                  <div className="mt-1">
                                    <span className="px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full">
                                      Selected
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      <p className="text-center text-sm text-gray-500 dark:text-gray-400 my-2">
                        - or -
                      </p>
                    </div>
                  )}

                  {/* Upload Section - Moved below */}
                  <div className="flex items-center justify-center w-full">
                    <label className={`relative w-full flex flex-col items-center px-4 py-6 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg border ${(resume instanceof File) ? 'border-green-500' : 'border-gray-300 dark:border-gray-600'
                      } cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600`}>
                      {(resume instanceof File) && (
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            handleDeleteResume();
                          }}
                          className="absolute top-2 right-2 p-1 text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 transition-colors duration-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600"
                          title="Delete resume"
                        >
                          <Trash2Icon className="w-5 h-5" />
                        </button>
                      )}
                      <BriefcaseIcon className="w-8 h-8 mb-2" />
                      <span className="text-sm">
                        {(resume instanceof File) ? resumeFileName : 'Upload Resume'}
                      </span>
                      {(resume instanceof File) && (
                        <span className="text-xs text-green-500 mt-1">
                          ✓ File uploaded
                        </span>
                      )}
                      <input
                        type="file"
                        className="hidden"
                        accept=".pdf,.doc,.docx"
                        onChange={handleResumeUpload}
                      />
                    </label>
                  </div>
                </div>

                {error && error !== 'Please enter a valid phone number' && (
                  <p className="text-sm text-red-500 dark:text-red-400">
                    {error}
                  </p>
                )}

                <button
                  onClick={handleApply}
                  disabled={isSubmitting || !job.active}
                  className={`w-full px-6 py-3 rounded-lg transition duration-300 flex items-center justify-center gap-2 
                    ${job.active
                      ? 'bg-[#05668D] hover:bg-[#034963] text-white'
                      : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'}`}
                >
                  {isSubmitting ? (
                    <>
                      <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-white border-r-transparent"></span>
                      Submitting...
                    </>
                  ) : !job.active ? (
                    <>
                      <XCircle className="w-4 h-4" />
                      Recruiter has closed this job
                    </>
                  ) : (
                    'Submit Application'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Login Card Modal */}
      {showLoginCard && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="relative">
            {/* Close button */}
            <button
              onClick={() => setShowLoginCard(false)}
              className="absolute -top-2 -right-2 bg-white dark:bg-gray-700 rounded-full p-1 shadow-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            >
              <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <LoginCard
              defaultRole="candidate"
              hideRoleSelector={true}
              onLoginSuccess={handleLoginSuccess}
              callbackUrl={`/c2c-jobid-${job.jobId}`}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default JobCard; 