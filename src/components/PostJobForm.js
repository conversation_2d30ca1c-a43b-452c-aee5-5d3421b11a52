"use client"
import { useState } from "react";
import Modal from '@/components/Modal';
import LoginCard from '@/components/LoginCard';

export default function PostJobForm() {
  return (
    <>
      <LoginCard
          defaultRole="recruiter"
          hideRoleSelector={true}
          callbackUrl="/dashboard/post-job"
          onLoginSuccess={() => setShowLoginModal(false)}
      />
    </>
  );
}