"use client";

import { MapPin, Clock, BriefcaseIcon, ChevronDownIcon, Building2Icon } from 'lucide-react';
import SkillTag from './SkillTag';
import { getDeterministicNumber } from '@/utils/stringOps';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInWeeks = Math.floor(diffInDays / 7);
    const diffInMonths = Math.floor(diffInDays / 30);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInMinutes < 60) {
        return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInHours < 24) {
        return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInDays === 0) {
        return 'Today';
    } else if (diffInDays === 1) {
        return 'Yesterday';
    } else if (diffInDays < 7) {
        return `${diffInDays} days ago`;
    } else if (diffInWeeks < 4) {
        return `${diffInWeeks} ${diffInWeeks === 1 ? 'week' : 'weeks'} ago`;
    } else if (diffInMonths < 12) {
        return `${diffInMonths} ${diffInMonths === 1 ? 'month' : 'months'} ago`;
    } else {
        return date.toLocaleDateString();
    }
};

const OldJobCard = ({ job, applicationPageUrl }) => {
    const router = useRouter();

    const handleApply = () => {
        router.push(`/apply/c2c-jobs-${job.job_id}`);
    };

    return (
        <div className="flex flex-col gap-4">
            {/* Main Job Card */}
            <div
                onClick={handleApply}
                className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm hover:shadow-lg 
                  dark:shadow-none dark:hover:shadow-[0_0_15px_rgba(5,102,141,0.3)] 
                  border border-gray-100 dark:border-gray-700 
                  transition-all duration-300 hover:translate-y-[-2px] cursor-pointer">

                {/* Mobile Layout */}
                <div className="md:hidden">
                    <div className="flex gap-4">
                        {/* Left side - Logo and Company Name */}
                        <div className="flex flex-col items-center">
                            <div className="relative w-16 h-16 rounded-lg bg-gray-50 dark:bg-gray-700">
                                <Image
                                    src="/1.png"
                                    alt={`${job.company_name} logo`}
                                    className="rounded-lg object-contain p-2"
                                    fill
                                    sizes="64px"
                                    priority
                                />
                            </div>
                            <span className="text-sm font-medium text-gray-900 dark:text-white mt-1 text-center line-clamp-2">
                                {job.company_name}
                            </span>
                        </div>

                        {/* Right side - Location and Posted Time */}
                        <div className="flex-1 flex flex-col justify-start gap-1 text-sm text-gray-600 dark:text-gray-300">
                            <div className="flex items-center">
                                <MapPin className="w-4 h-4 mr-1" />
                                <span className="line-clamp-1">{job.city}, {job.state}</span>
                            </div>
                            <div className="flex items-center">
                                <Clock className="w-4 h-4 mr-1" />
                                <span>Posted {formatRelativeTime(job.created_at)}</span>
                            </div>
                        </div>
                    </div>

                    {/* Role Title and Job Type */}
                    <div className="mt-4">
                        <div className="flex items-center gap-2">
                            <h3 className="text-lg font-bold text-gray-900 dark:text-white">{job.role}</h3>
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-200 dark:border-blue-800">
                                {job.job_type}
                            </span>
                        </div>
                    </div>

                    {/* Applicants count and Full-width Apply Button */}
                    <div className="mt-4 flex flex-col w-full">
                        <span className="text-xs text-gray-500 dark:text-gray-400 text-center mb-2">
                            {getDeterministicNumber(job._id)} applicants
                        </span>
                        <button
                            onClick={handleApply}
                            className="w-full bg-[#05668D] hover:bg-[#034963] text-white px-6 py-3 rounded-lg transition duration-300"
                        >
                            <div className="flex items-center justify-center gap-2">
                                <span>Apply Now</span>
                                <ChevronDownIcon className="w-4 h-4" />
                            </div>
                        </button>
                    </div>
                </div>

                {/* Desktop Layout */}
                <div className="hidden md:flex flex-row items-start md:items-center justify-between gap-4">
                    {/* Left - Company Logo */}
                    <div className="flex-shrink-0">
                        <div className="relative w-20 h-20 rounded-lg bg-gray-50 dark:bg-gray-700">
                            <Image
                                src="/1.png"
                                alt={`${job.company_name} logo`}
                                className="rounded-lg object-contain p-2"
                                fill
                                sizes="80px"
                                priority
                            />
                        </div>
                    </div>

                    {/* Middle - Job Details */}
                    <div className="flex-grow">
                        <div className="flex items-center gap-2">
                            <h3 className="text-lg font-bold text-gray-900 dark:text-white">{job.role}</h3>
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-200 dark:border-blue-800">
                                {job.job_type}
                            </span>
                        </div>

                        <div className="flex flex-wrap items-center gap-2 mt-2 text-sm text-gray-900 dark:text-white">
                            <div className="flex items-center">
                                <Building2Icon className="w-4 h-4 mr-1 text-gray-900 dark:text-white" />
                                {job.company_name}
                            </div>
                            <div className="flex items-center">
                                <MapPin className="w-4 h-4 mr-1 text-gray-900 dark:text-white" />
                                {job.city}, {job.state}
                            </div>
                            <div className="flex items-center">
                                <Clock className="w-4 h-4 mr-1 text-gray-900 dark:text-white" />
                                Posted {formatRelativeTime(job.created_at)}
                            </div>
                        </div>

                        <div className="flex flex-wrap gap-2 mt-3">
                            {job.skills?.slice(0, 5).map((skill, index) => (
                                <SkillTag key={index} skill={skill} />
                            ))}
                            {job.skills?.length > 5 && (
                                <span className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 dark:text-gray-300 rounded-full">
                                    +{job.skills.length - 5} more
                                </span>
                            )}
                        </div>
                    </div>

                    {/* Right - Apply Button */}
                    <div className="flex-shrink-0 w-full md:w-auto flex flex-col items-center gap-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                            {getDeterministicNumber(job._id)} applicants
                        </span>
                        <button
                            onClick={handleApply}
                            className="w-full md:w-auto bg-[#05668D] hover:bg-[#034963] text-white px-6 py-2 rounded-lg transition duration-300"
                        >
                            Apply Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default OldJobCard;
