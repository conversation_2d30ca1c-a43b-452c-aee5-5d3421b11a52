'use client';
import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { countryList } from '@/utils/countryList'; // We'll create this

const CustomPhoneInput = forwardRef((props, ref) => {
    const [mounted, setMounted] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCountry, setSelectedCountry] = useState(countryList.find(c => c.code === 'US'));
    const [isValid, setIsValid] = useState(true);
    const [errorMessage, setErrorMessage] = useState('');
    const [localNumber, setLocalNumber] = useState(''); // For displaying number without country code
    const dropdownRef = useRef(null);

    useEffect(() => {
        setMounted(true);
        // Close dropdown when clicking outside
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Initialize or update local number when phoneNumber changes
    useEffect(() => {
        if (props.phoneNumber) {
            // If phone number starts with country code, remove it for display
            const countryCode = selectedCountry.dialCode;
            if (props.phoneNumber.startsWith(countryCode)) {
                setLocalNumber(props.phoneNumber.substring(countryCode.length));
            } else {
                setLocalNumber(props.phoneNumber);
            }
        }
    }, [props.phoneNumber, selectedCountry.dialCode]);

    // Add this useEffect to validate on initial mount
    useEffect(() => {
        validatePhoneNumber(localNumber, selectedCountry);
    }, []); // Empty dependency array = run only on mount

    // Add this effect to validate when localNumber or country changes
    useEffect(() => {
        validatePhoneNumber(localNumber, selectedCountry);
    }, [localNumber, selectedCountry]);

    const handlePhoneChange = (e) => {
        const input = e.target.value;
        const cleaned = input.replace(/\D/g, '');

        // Maximum length check (without country code)
        if (cleaned.length > selectedCountry.maxLength) return;

        setLocalNumber(cleaned);

        // Combine country code with local number for full phone number
        const fullNumber = selectedCountry.dialCode + cleaned;
        props.setPhoneNumber(fullNumber);

        // Always validate
        validatePhoneNumber(cleaned, selectedCountry);
    };

    const validatePhoneNumber = (number, country) => {
        if (!number || number.length === 0) {
            setIsValid(false);
            setErrorMessage('Phone number is required');
            props.onValidation?.(false);
            return false;
        }

        const minLength = country.minLength || 10;
        if (number.length < minLength) {
            setIsValid(false);
            setErrorMessage(`Phone number must be ${minLength} digits`);
            props.onValidation?.(false);
            return false;
        }

        setIsValid(true);
        setErrorMessage('');
        props.onValidation?.(true);
        return true;
    };

    const handleCountrySelect = (country) => {
        setSelectedCountry(country);
        setIsOpen(false);

        // Update full phone number with new country code
        if (localNumber) {
            props.setPhoneNumber(country.dialCode + localNumber);
            validatePhoneNumber(localNumber, country);
        }
    };

    const filteredCountries = countryList.filter(country =>
        country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        country.dialCode.includes(searchQuery)
    );

    // Expose validate method to parent
    useImperativeHandle(ref, () => ({
        validate: () => {
            const countryCode = selectedCountry.dialCode;
            let local = props.phoneNumber.startsWith(countryCode)
                ? props.phoneNumber.substring(countryCode.length)
                : props.phoneNumber;
            return validatePhoneNumber(local, selectedCountry);
        }
    }));

    if (!mounted) {
        return (
            <div className="w-full h-[42px] bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
        );
    }

    return (
        <div className="relative space-y-1" ref={dropdownRef}>
            <div className="relative flex">
                {/* Country Selector Button */}
                <button
                    type="button"
                    onClick={() => setIsOpen(!isOpen)}
                    className={`flex items-center px-3 border rounded-l-lg border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 ${!isValid ? 'border-red-500 dark:border-red-500' : ''
                        }`}
                >
                    <span className="text-lg mr-2">{selectedCountry.flag}</span>
                    <span className="text-gray-900 dark:text-white text-sm">
                        {selectedCountry.dialCode}
                    </span>
                    <svg
                        className={`w-4 h-4 ml-2 transition-transform ${isOpen ? 'rotate-180' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                        />
                    </svg>
                </button>

                {/* Phone Input */}
                <input
                    type="tel"
                    value={localNumber}
                    onChange={handlePhoneChange}
                    className={`flex-1 p-3 border rounded-r-lg text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-[#05668D] dark:focus:ring-[#05668D]/50 focus:border-transparent bg-white dark:bg-gray-800 ${!isValid ? 'border-red-500 dark:border-red-500 focus:ring-red-500 dark:focus:ring-red-500' : ''
                        }`}
                    placeholder={`Enter your ${selectedCountry.minLength} digit phone number`}
                />
            </div>

            {/* Error Message */}
            {!isValid && (
                <p className="text-sm text-red-500 dark:text-red-400 mt-1">
                    {errorMessage}
                </p>
            )}

            {/* Country Dropdown */}
            {isOpen && (
                <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                    {/* Search Input */}
                    <div className="sticky top-0 p-2 bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700">
                        <input
                            type="text"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            placeholder="Search countries..."
                            className="w-full p-2 border rounded-md text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-[#05668D] dark:focus:ring-[#05668D]/50 focus:border-transparent bg-white dark:bg-gray-800"
                        />
                    </div>

                    {/* Country List */}
                    <div className="overflow-y-auto">
                        {filteredCountries.map((country) => (
                            <button
                                key={country.code}
                                onClick={() => handleCountrySelect(country)}
                                className="w-full flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-left"
                            >
                                <span className="text-lg mr-3">{country.flag}</span>
                                <span className="text-gray-900 dark:text-white">
                                    {country.name}
                                </span>
                                <span className="ml-auto text-gray-500 dark:text-gray-400">
                                    {country.dialCode}
                                </span>
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
});

export default CustomPhoneInput;
