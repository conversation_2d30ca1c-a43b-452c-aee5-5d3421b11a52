'use client';

const companies = [
  { name: 'ITProfound Inc', logo: '/assets/images/microsoft.svg' },
  { name: 'Aetherland', logo: '/assets/images/google.svg' },
  { name: 'Tek<PERSON><PERSON>', logo: '/assets/images/amazon.svg' },
  { name: 'Delta', logo: '/assets/images/meta.svg' },
  { name: 'Alpabet solutions', logo: '/assets/images/apple.svg' },
];

const TrustedCompanies = () => {
  return (
    <div className="mb-12">
      <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 text-center">
        Trusted by Leading Companies
      </h2>
      
      {/* Marquee container */}
      <div className="relative overflow-hidden bg-transparent py-8">
        {/* Gradient Overlays */}
        <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-gray-50 dark:from-gray-900 to-transparent z-10"></div>
        <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-gray-50 dark:from-gray-900 to-transparent z-10"></div>
        
        {/* Sliding content */}
        <div className="flex animate-marquee whitespace-nowrap">
          {[...companies, ...companies].map((company, index) => (
            <div
              key={index}
              className="flex items-center mx-8 text-gray-500 dark:text-gray-400"
            >
              <span className="text-xl font-semibold">{company.name}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TrustedCompanies; 