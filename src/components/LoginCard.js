'use client';
import { signIn } from "next-auth/react";
import { useState, useEffect } from "react";

export default function LoginCard({
    defaultRole = 'candidate',
    hideRoleSelector = false,
    callbackUrl = undefined,
}) {
    const [role, setRole] = useState(defaultRole);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        setRole(defaultRole);
    }, [defaultRole]);

    const handleSignIn = async (provider) => {
        setIsLoading(true);
        try {
            let callback = `/@profile/${role}`;

            // If there's a callbackUrl from the middleware redirect
            if (callbackUrl) {
                // Pass the callbackUrl to the profile handler
                callback += `${callbackUrl}`;
            } else {
                // Default redirect based on role
                (role == "candidate") ? callback += `/` : callback += `/dashboard/post-job`;
            }

            // Initiate sign in
            const result = await signIn(provider, {
                callbackUrl: callback,
                redirect: false
            });

            if (result?.error) {
                console.error('Sign in error:', result.error);
                return;
            }
        } catch (error) {
            console.error('Login error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="w-full max-w-md p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
            <div className="flex flex-col items-center gap-6">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        Welcome to OnlyC2C.
                    </h1>
                    <p className="text-gray-600 dark:text-gray-300">
                        {role === 'recruiter'
                            ? 'Sign in to start posting jobs'
                            : 'Sign in to explore opportunities'}
                    </p>
                </div>

                {!hideRoleSelector && (
                    <div className="w-full">
                        <div className="flex bg-[#05668D]/10 dark:bg-[#05668D]/20 rounded-lg p-1">
                            <button
                                onClick={() => setRole("candidate")}
                                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${role === "candidate"
                                    ? "bg-white dark:bg-[#05668D] text-[#05668D] dark:text-white shadow-sm"
                                    : "text-[#05668D]/60 dark:text-white/70 hover:text-[#05668D] dark:hover:text-white"
                                    }`}
                            >
                                I'm a Candidate
                            </button>
                            <button
                                onClick={() => setRole("recruiter")}
                                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${role === "recruiter"
                                    ? "bg-white dark:bg-[#05668D] text-[#05668D] dark:text-white shadow-sm"
                                    : "text-[#05668D]/60 dark:text-white/70 hover:text-[#05668D] dark:hover:text-white"
                                    }`}
                            >
                                I'm a Recruiter
                            </button>
                        </div>
                    </div>
                )}

                <div className="w-full space-y-4">
                    {/* Google Sign In */}
                    <button
                        onClick={() => handleSignIn("google")}
                        disabled={isLoading}
                        className="w-full px-6 py-3 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg transition-colors duration-200 border border-gray-300 dark:border-gray-600 flex items-center justify-center gap-3 group disabled:opacity-50"
                    >
                        {isLoading ? (
                            <div className="w-5 h-5 border-2 border-gray-300 border-t-[#05668D] rounded-full animate-spin" />
                        ) : (
                            <>
                                <svg className="w-5 h-5" viewBox="0 0 24 24">
                                    <path
                                        fill="currentColor"
                                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                        className="fill-[#4285F4]"
                                    />
                                    <path
                                        fill="currentColor"
                                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                        className="fill-[#34A853]"
                                    />
                                    <path
                                        fill="currentColor"
                                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                        className="fill-[#FBBC05]"
                                    />
                                    <path
                                        fill="currentColor"
                                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                        className="fill-[#EA4335]"
                                    />
                                </svg>
                                Continue with Google
                            </>
                        )}
                    </button>

                    {/* LinkedIn */}
                    <button
                        onClick={() => handleSignIn("linkedin")}
                        disabled={isLoading}
                        className="w-full px-6 py-3 bg-[#0077B5] hover:bg-[#0077B5]/90 text-white rounded-lg transition-colors duration-200 flex items-center justify-center gap-3 group disabled:opacity-50"
                    >
                        {isLoading ? (
                            <div className="w-5 h-5 border-2 border-gray-300 border-t-white rounded-full animate-spin" />
                        ) : (
                            <>
                                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z" />
                                </svg>
                                Continue with LinkedIn
                            </>
                        )}
                    </button>

                    {/* GitHub - Coming Soon */}
                    <button
                        disabled
                        className="w-full px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 rounded-lg border border-gray-200 dark:border-gray-600 flex items-center justify-center gap-3 cursor-not-allowed relative group"
                    >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                        </svg>
                        Continue with GitHub
                        <span className="absolute right-0 top-0 -mt-2 -mr-2 px-2 py-1 text-xs font-medium bg-[#05668D] text-white rounded-full">Coming Soon</span>
                    </button>
                </div>

                <div className="text-sm text-gray-500 dark:text-gray-400 text-center">
                    By continuing, you agree to our{' '}
                    <a href="#" className="text-[#05668D] hover:underline">Terms of Service</a>
                    {' '}and{' '}
                    <a href="#" className="text-[#05668D] hover:underline">Privacy Policy</a>
                </div>
            </div>
        </div>
    );
}