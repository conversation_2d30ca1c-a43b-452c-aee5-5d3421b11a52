'use client';
import { useState, useEffect } from 'react';

const TypewriterText = ({ text, speed = 30, onComplete }) => {
    const [displayedText, setDisplayedText] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        if (!text) return;

        // Reset when new text arrives
        setDisplayedText('');
        setCurrentIndex(0);

        // Type out the text
        const intervalId = setInterval(() => {
            setCurrentIndex(prevIndex => {
                if (prevIndex >= text.length) {
                    clearInterval(intervalId);
                    onComplete?.();
                    return prevIndex;
                }
                setDisplayedText(text.slice(0, prevIndex + 1));
                return prevIndex + 1;
            });
        }, speed);

        return () => clearInterval(intervalId);
    }, [text, speed, onComplete]);

    return (
        <div className="whitespace-pre-wrap">
            {displayedText}
            {currentIndex < (text?.length || 0) && (
                <span className="animate-pulse">|</span>
            )}
        </div>
    );
};

export default TypewriterText; 