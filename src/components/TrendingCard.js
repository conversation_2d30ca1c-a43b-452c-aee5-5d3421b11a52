'use client';

import React, { useState, useRef, useEffect } from "react";
import { ArrowUpRight, ArrowDownRight } from "lucide-react";

const skillTrends = {
    React: [12, 15, 18, 20, 22, 25, 30, 28, 27, 29, 31, 35],
    Angular: [10, 12, 14, 16, 18, 20, 22, 21, 23, 25, 27, 30],
    ".NET": [8, 9, 11, 13, 15, 17, 19, 18, 20, 22, 24, 26],
    FullStack: [7, 8, 9, 10, 12, 14, 16, 15, 17, 19, 21, 23],
    Python: [14, 16, 18, 20, 22, 24, 26, 25, 27, 29, 31, 33],
    QA: [6, 7, 8, 9, 10, 12, 14, 13, 15, 17, 19, 21],
    SpringBoot: [8, 10, 12, 14, 13, 15, 17, 16, 18, 20, 22, 25],
    // ...add more if needed
};

const skillColors = {
    React: { bg: "#e6f4ea", main: "#3bb77e" }, // soft green
    Angular: { bg: "#ffece6", main: "#ff7043" }, // deep orange
    ".NET": { bg: "#e7eafd", main: "#6c63ff" }, // soft purple
    FullStack: { bg: "#e6f0fa", main: "#21759b" }, // soft blue
    Python: { bg: "#eaf4fd", main: "#4584b6" }, // soft blue
    QA: { bg: "#fff4e6", main: "#f59e42" }, // soft orange
    SpringBoot: { bg: "#eafae6", main: "#6db33f" }, // soft green
    // ...add more if needed
};

const skills = Object.keys(skillTrends);

function TrendGraph({ data, color, width, active }) {
    const height = 48;
    const max = Math.max(...data);
    const min = Math.min(...data);

    // Animate stroke color on active change
    return (
        <svg width={width} height={height}>
            <defs>
                <linearGradient id={`shadow-gradient-${color.replace('#', '')}`} x1="0" y1="0" x2="0" y2={height} gradientUnits="userSpaceOnUse">
                    <stop stopColor={color} />
                    <stop offset="1" stopColor={color} stopOpacity="0" />
                </linearGradient>
            </defs>
            <path
                d={getShadowPath(data, width, height, min, max)}
                fill={`url(#shadow-gradient-${color.replace('#', '')})`}
                opacity={active ? "0.28" : "0.18"}
                style={{
                    transition: "opacity 0.4s cubic-bezier(.4,0,.2,1)"
                }}
            />
            <path
                d={getLinePath(data, width, height, min, max)}
                fill="none"
                stroke={color}
                strokeWidth={active ? "3.5" : "2.5"}
                style={{
                    transition: "stroke 0.4s cubic-bezier(.4,0,.2,1), stroke-width 0.4s cubic-bezier(.4,0,.2,1)"
                }}
            />
        </svg>
    );
}

// Helper functions for SVG paths
function getLinePath(data, width, height, min, max) {
    const points = data.map((v, i) => {
        const x = (i / (data.length - 1)) * (width - 16) + 8;
        const y = height - 8 - ((v - min) / (max - min || 1)) * (height - 16);
        return [x, y];
    });
    return points.map(([x, y], i) => (i === 0 ? `M${x},${y}` : `L${x},${y}`)).join(" ");
}
function getShadowPath(data, width, height, min, max) {
    const points = data.map((v, i) => {
        const x = (i / (data.length - 1)) * (width - 16) + 8;
        const y = height - 8 - ((v - min) / (max - min || 1)) * (height - 16);
        return [x, y];
    });
    const linePath = points.map(([x, y], i) => (i === 0 ? `M${x},${y}` : `L${x},${y}`)).join(" ");
    return (
        linePath +
        ` L${points[points.length - 1][0]},${height - 8}` +
        ` L${points[0][0]},${height - 8} Z`
    );
}

const TrendingCard = () => {
    const [hovered, setHovered] = useState(null);
    const [activeSkill, setActiveSkill] = useState(skills[0]);
    const [containerWidth, setContainerWidth] = useState(180);
    const skillsRef = useRef(null);
    const intervalRef = useRef(null);

    // Handle container width
    useEffect(() => {
        if (skillsRef.current) {
            setContainerWidth(skillsRef.current.offsetWidth);
        }
        const handleResize = () => {
            if (skillsRef.current) {
                setContainerWidth(skillsRef.current.offsetWidth);
            }
        };
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    // Auto-cycle trending skills in order when not hovered
    useEffect(() => {
        if (hovered) {
            setActiveSkill(hovered);
            if (intervalRef.current) clearInterval(intervalRef.current);
            return;
        }
        let idx = skills.indexOf(activeSkill);
        intervalRef.current = setInterval(() => {
            idx = (idx + 1) % skills.length;
            setActiveSkill(skills[idx]);
        }, 2000);
        return () => clearInterval(intervalRef.current);
    }, [hovered]);

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl flex-1 flex flex-col overflow-hidden border border-gray-200 dark:border-gray-700 min-h-[260px]">
            {/* Mac window UI header */}
            <div className="bg-gray-100 dark:bg-gray-700 px-4 py-2 border-b border-gray-200 dark:border-gray-600 flex items-center">
                <div className="flex space-x-2 mr-4">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div className="text flex-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Job Market Trends
                </div>
            </div>

            {/* Content */}
            <div className="p-6 flex-1 flex flex-col">
                <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-4">
                    Hired recently for
                </h3>
                <div
                    className="flex flex-wrap gap-2 relative mb-4 w-full"
                    ref={skillsRef}
                >
                    {skills.map(skill => {
                        const isActive = skill === activeSkill;
                        return (
                            <div
                                key={skill}
                                className="relative"
                                onMouseEnter={() => setHovered(skill)}
                                onMouseLeave={() => setHovered(null)}
                            >
                                <span
                                    className={`px-3 py-1.5 rounded-full text-sm font-medium cursor-pointer transition-colors duration-400`}
                                    style={{
                                        backgroundColor: isActive
                                            ? skillColors[skill].main
                                            : skillColors[skill].bg,
                                        color: isActive
                                            ? "#fff"
                                            : skillColors[skill].main,
                                        boxShadow: isActive
                                            ? `0 0 0 2px ${skillColors[skill].main}33`
                                            : undefined,
                                        transition: "background-color 0.4s cubic-bezier(.4,0,.2,1), color 0.4s cubic-bezier(.4,0,.2,1), box-shadow 0.4s cubic-bezier(.4,0,.2,1)"
                                    }}
                                >
                                    {skill}
                                </span>
                            </div>
                        );
                    })}
                </div>
                {/* Dedicated area for the TrendGraph */}
                <div className="min-h-[60px] flex flex-col items-center justify-center w-full">
                    {activeSkill && (
                        <>
                            <TrendGraph
                                data={skillTrends[activeSkill]}
                                color={skillColors[activeSkill].main}
                                width={containerWidth}
                                active={true}
                            />
                            <span className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                {skillTrends[activeSkill].at(-1)} jobs last month
                            </span>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TrendingCard;
