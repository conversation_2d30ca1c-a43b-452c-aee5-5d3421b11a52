"use client";
import React, { useState, useRef, useEffect } from 'react';
import axios from 'axios';
import { Loader2 } from 'lucide-react';

const SkillsSelect = ({ skills, setSkills, required = false, skillsWithNamesForAiText, setSkillsWithNamesForAiText }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [isCreatingNew, setIsCreatingNew] = useState(false);
    const [newSkill, setNewSkill] = useState('');
    const [availableSkills, setAvailableSkills] = useState([]);
    const [loading, setLoading] = useState(true);
    const [skillsMap, setSkillsMap] = useState({}); // Map to store id -> skill object mapping
    const wrapperRef = useRef(null);

    // Click outside handler
    useEffect(() => {
        function handleClickOutside(event) {
            if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        }
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Fetch skills and initialize skill map
    useEffect(() => {
        const fetchSkills = async () => {
            try {
                const response = await axios.get(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/skills`);
                const fetchedSkills = response.data.data.items;

                // Create a map of id -> skill object for quick lookup
                const skillsMapping = {};
                fetchedSkills.forEach(skill => {
                    skillsMapping[skill._id] = skill;
                });

                setSkillsMap(skillsMapping);
                setAvailableSkills(fetchedSkills);

                // If we have skills but no names, initialize the names
                if (skills.length > 0 && skillsWithNamesForAiText.length === 0) {
                    const skillNames = skills.map(skillId => {
                        const skill = skillsMapping[skillId];
                        return skill ? skill.name : 'Loading...';
                    });
                    setSkillsWithNamesForAiText(skillNames);
                }
            } catch (error) {
                console.error('Error fetching skills:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchSkills();
    }, [skills, setSkillsWithNamesForAiText]);

    const handleInputChange = (e) => {
        setSearchTerm(e.target.value);
        setIsOpen(true);
    };

    const handleSkillSelect = (selectedSkill) => {
        if (!skills.includes(selectedSkill._id)) {
            setSkills([...skills, selectedSkill._id]);
            setSkillsWithNamesForAiText([...skillsWithNamesForAiText, selectedSkill.name]);
        }
        setSearchTerm('');
        setIsOpen(false);
    };

    const handleRemoveSkill = (index, skillId) => {
        const updatedSkills = skills.filter((_, idx) => idx !== index);
        const updatedSkillNames = skillsWithNamesForAiText.filter((_, idx) => idx !== index);
        setSkills(updatedSkills);
        setSkillsWithNamesForAiText(updatedSkillNames);
    };

    const handleCreateSkill = async () => {
        if (!newSkill.trim()) return;

        try {
            setLoading(true);
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/skills`,
                {
                    name: newSkill.trim(),
                    category: "Technical"
                }
            );

            const createdSkill = response.data.data;
            setAvailableSkills(prev => [...prev, createdSkill]);

            // Update the skills map
            setSkillsMap(prev => ({
                ...prev,
                [createdSkill._id]: createdSkill
            }));

            handleSkillSelect(createdSkill);
            setNewSkill('');
            setIsCreatingNew(false);
        } catch (error) {
            console.error('Error creating skill:', error);
            alert('Failed to create new skill. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const filteredSkills = availableSkills.filter(skill =>
        skill.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !skills.includes(skill._id)
    );

    return (
        <div className="relative w-full" ref={wrapperRef}>
            <div className="w-full p-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 focus-within:ring-2 focus-within:ring-[#05668D] dark:focus-within:ring-[#05668D]/50 focus-within:border-transparent min-h-[3.25rem]">
                <div className="flex flex-wrap gap-2">
                    {/* Selected Skills Pills */}
                    {skills.map((skillId, index) => (
                        <span
                            key={skillId}
                            className="inline-flex items-center px-2.5 py-1.5 rounded-full text-sm font-medium bg-[#05668D]/10 text-[#05668D]/90 dark:bg-[#05668D]/20 dark:text-white/80"
                        >
                            {loading ? (
                                <div className="flex items-center">
                                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                    Loading...
                                </div>
                            ) : (
                                skillsWithNamesForAiText[index] || 'Unknown Skill'
                            )}
                            <button
                                type="button"
                                onClick={() => handleRemoveSkill(index, skillId)}
                                className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-[#05668D]/20"
                            >
                                ×
                            </button>
                        </span>
                    ))}

                    {/* Search Input */}
                    <input
                        type="text"
                        value={searchTerm}
                        onChange={handleInputChange}
                        onClick={() => setIsOpen(true)}
                        className="flex-grow min-w-[120px] bg-transparent border-none focus:ring-0 p-1.5 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                        placeholder={skills.length === 0 ? "Search skills..." : "Add more skills..."}
                        required={required && skills.length === 0}
                    />
                </div>
            </div>

            {/* Dropdown */}
            {isOpen && !isCreatingNew && (
                <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {loading ? (
                        <div className="p-3 text-gray-500 dark:text-gray-400 flex items-center">
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                            Loading skills...
                        </div>
                    ) : filteredSkills.length > 0 ? (
                        <>
                            {filteredSkills.map((skill) => (
                                <div
                                    key={skill._id}
                                    className="p-3 cursor-pointer hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700"
                                    onClick={() => handleSkillSelect(skill)}
                                >
                                    {skill.name}
                                </div>
                            ))}
                            {searchTerm && (
                                <div
                                    className="p-3 cursor-pointer text-[#05668D] dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 border-t border-gray-300 dark:border-gray-700"
                                    onClick={() => {
                                        setNewSkill(searchTerm);
                                        setIsCreatingNew(true);
                                    }}
                                >
                                    + Add "{searchTerm}" as new skill
                                </div>
                            )}
                        </>
                    ) : (
                        <div
                            className="p-3 cursor-pointer text-[#05668D] dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => {
                                setNewSkill(searchTerm);
                                setIsCreatingNew(true);
                            }}
                        >
                            + Add "{searchTerm}" as new skill
                        </div>
                    )}
                </div>
            )}

            {/* Create New Skill Dialog */}
            {isOpen && isCreatingNew && (
                <div className="absolute z-10 w-full mt-1 p-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-lg">
                    <div className="space-y-4">
                        <input
                            type="text"
                            value={newSkill}
                            onChange={(e) => setNewSkill(e.target.value)}
                            className="w-full p-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-[#05668D] dark:focus:ring-[#05668D]/50 focus:border-transparent"
                            placeholder="Enter skill name"
                        />
                        <div className="flex space-x-2">
                            <button
                                type="button"
                                onClick={() => setIsCreatingNew(false)}
                                className="flex-1 p-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
                            >
                                Cancel
                            </button>
                            <button
                                type="button"
                                onClick={handleCreateSkill}
                                disabled={!newSkill.trim() || loading}
                                className="flex-1 p-2 text-white bg-[#05668D] rounded-lg hover:bg-[#05668D]/90 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {loading ? (
                                    <div className="flex items-center justify-center">
                                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                        Adding...
                                    </div>
                                ) : (
                                    'Add Skill'
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default SkillsSelect;