
import { Trash2Icon } from 'lucide-react';

const LogoPreview = ({ userUploadedLogo, logo, organizationLogo, onDelete }) => {
    return (
      <div className="flex flex-col items-left space-y-4 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
        <div className="w-20 h-20 border rounded-lg overflow-hidden">
          <img
            src={userUploadedLogo ? URL.createObjectURL(logo) : organizationLogo}
            alt="Company Logo"
            className="w-full h-full object-contain"
          />
        </div>
        <button
            type="button"
            onClick={onDelete}
            className="flex items-center justify-center space-x-2 px-3 py-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition duration-150 text-sm font-medium"
        >
          <Trash2Icon className="w-4 h-4" />
          <span>Remove Logo</span>
        </button>
      </div>
    );
  };

export default LogoPreview;