import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-gray-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <Link href="/" className="text-2xl font-bold text-primary">
              <img src="/assets/images/logo_white.png" alt="Onlyc2c logo" className="h-10" />
            </Link>
            <p className="text-gray-400 mt-4">
              5858 Blackshire Path <br />
              STE B-2 <br />
              Invergrove Heights, MN 55076 <br />
              <EMAIL>
            </p>
          </div>
          <div>
            <h4 className="text-lg font-semibold mb-4">For Job Seekers</h4>
            <ul className="space-y-2">
              <li><Link href="/" className="text-gray-400 hover:text-white">Browse Jobs</Link></li>
              <li><Link href="/" className="text-gray-400 hover:text-white">Browse Companies</Link></li>
              <li><Link href="/" className="text-gray-400 hover:text-white">Career Advice</Link></li>
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-semibold mb-4">For Employers</h4>
            <ul className="space-y-2">
              <li><Link href="/post-job" className="text-gray-400 hover:text-white">Post a Job</Link></li>
              <li><Link href="/pricing" className="text-gray-400 hover:text-white">Pricing</Link></li>
              <li><Link href="/resources" className="text-gray-400 hover:text-white">Resources</Link></li>
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact</h4>
            <ul className="space-y-2">
              <li><Link href="/about" className="text-gray-400 hover:text-white">About Us</Link></li>
              <li><Link href="/contact" className="text-gray-400 hover:text-white">Contact Us</Link></li>
              <li><Link href="/support" className="text-gray-400 hover:text-white">Support</Link></li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} Onlyc2c. All rights reserved.
            <Link href="/terms" className="text-gray-400 hover:text-blue"> Terms and Conditions</Link> |
            <Link href="/privacy-policy" className="text-gray-400 hover:text-blue"> Privacy Policy</Link></p>
        </div>
      </div>
    </footer>
  );
} 