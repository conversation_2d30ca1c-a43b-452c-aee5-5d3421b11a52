"use client";

import { useState, useEffect } from 'react';
import { Linkedin, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { useUser } from '@/context/UserContext';
import axios from 'axios';
import { checkLinkedInConnectionFromBackend, connectLinkedIn } from '@/utils/linkedinOps';


export default function LinkedinIntegration() {
    const { userDetails } = useUser();
    const [linkedInStatus, setLinkedInStatus] = useState({
        isConnected: false,
        loading: true,
        error: null,
        accountType: null,
        profile: null,
        expiresAt: null,
    });

    useEffect(() => {
        if (userDetails) {
            checkLinkedInConnection();
            if (typeof window !== 'undefined') {
                const urlParams = new URLSearchParams(window.location.search);
                const success = urlParams.get('success');
                const error = urlParams.get('error');
                if (success === 'linkedin_connected') {
                    checkLinkedInConnection();
                } else if (error) {
                    setLinkedInStatus(prev => ({
                        ...prev,
                        error: error === 'invalid_state' ? 'Invalid authentication state' :
                            error === 'user_not_found' ? 'User not found' :
                                error === 'token_exchange_failed' ? 'Failed to authenticate with LinkedIn' :
                                    error === 'profile_fetch_failed' ? 'Failed to fetch LinkedIn profile' :
                                        'Failed to connect to LinkedIn',
                    }));
                }
                if (success || error) {
                    const url = new URL(window.location.href);
                    url.search = '';
                    window.history.replaceState({}, document.title, url.toString());
                }
            }
        }
    }, [userDetails]);

    const checkLinkedInConnection = async () => {
        try {
            setLinkedInStatus(prev => ({ ...prev, loading: true }));
            const response = await checkLinkedInConnectionFromBackend();
            const responseData = response.data || {};
            setLinkedInStatus({
                isConnected: responseData.isConnected || false,
                loading: false,
                error: null,
                accountType: responseData.accountType || null,
                profile: responseData.profile || null,
                expiresAt: responseData.expiresAt,
            });
        } catch (error) {
            setLinkedInStatus({
                isConnected: false,
                loading: false,
                error: error.response?.data?.message || 'Failed to check LinkedIn connection status',
                accountType: null,
            });
        }
    };

    const handleConnectLinkedIn = async () => {
        try {
            setLinkedInStatus(prev => ({ ...prev, loading: true }));
            // Get current page URL to return to after authentication
            const currentUrl = window.location.href;
            const response = await connectLinkedIn(currentUrl);
            const responseData = response.data || {};
            if (!responseData.authUrl) {
                throw new Error("No authorization URL returned from the server");
            }
            const width = 600;
            const height = 700;
            const left = window.screen.width / 2 - width / 2;
            const top = window.screen.height / 2 - height / 2;
            const popup = window.open(
                responseData.authUrl,
                'linkedin-auth',
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
            );
            if (!popup || popup.closed || typeof popup.closed === 'undefined') {
                window.location.href = responseData.authUrl;
            }
        } catch (error) {
            setLinkedInStatus(prev => ({
                ...prev,
                loading: false,
                error: error.response?.data?.message || 'Failed to connect to LinkedIn',
            }));
        }
    };

    const handleDisconnectLinkedIn = async () => {
        try {
            setLinkedInStatus(prev => ({ ...prev, loading: true }));
            await axios.delete('/api/v1/linkedin/disconnect');
            setLinkedInStatus({
                isConnected: false,
                loading: false,
                error: null,
                accountType: null,
            });
        } catch (error) {
            setLinkedInStatus(prev => ({
                ...prev,
                loading: false,
                error: error.response?.data?.message || 'Failed to disconnect LinkedIn',
            }));
        }
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                <Linkedin className="w-5 h-5 mr-2 text-[#0077B5]" />
                LinkedIn Integration
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
                Connect your LinkedIn account to post jobs directly to your LinkedIn profile.
            </p>
            {linkedInStatus.loading ? (
                <div className="flex items-center justify-center py-4">
                    <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                </div>
            ) : linkedInStatus.isConnected ? (
                <div className="space-y-4">
                    <div className="flex items-center text-green-600 dark:text-green-400">
                        <CheckCircle className="w-5 h-5 mr-2" />
                        <span>Connected to LinkedIn</span>
                    </div>
                    {linkedInStatus.profile && (
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Connected Account
                            </h3>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                                <p><span className="font-medium">Name:</span> {linkedInStatus.profile.name}</p>
                                {linkedInStatus.profile.email && (
                                    <p><span className="font-medium">Email:</span> {linkedInStatus.profile.email}</p>
                                )}
                                {linkedInStatus.expiresAt && (
                                    <p><span className="font-medium">Token Expires:</span> {new Date(linkedInStatus.expiresAt * 1000).toLocaleString()}</p>
                                )}
                            </div>
                        </div>
                    )}
                    <button
                        onClick={handleDisconnectLinkedIn}
                        className="px-4 py-2 bg-red-100 text-red-600 hover:bg-red-200 rounded-md transition-colors duration-200"
                    >
                        Disconnect
                    </button>
                </div>
            ) : (
                <div className="space-y-4">
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                        <XCircle className="w-5 h-5 mr-2" />
                        <span>Not connected to LinkedIn</span>
                    </div>
                    <div>
                        <button
                            onClick={handleConnectLinkedIn}
                            className="px-4 py-2 bg-[#0077B5] text-white hover:bg-[#0077B5]/90 rounded-md transition-colors duration-200 flex items-center justify-center"
                        >
                            <Linkedin className="w-4 h-4 mr-2" />
                            Connect LinkedIn Profile
                        </button>
                    </div>
                    {linkedInStatus.error && (
                        <div className="text-red-500 text-sm mt-2">
                            {linkedInStatus.error}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}