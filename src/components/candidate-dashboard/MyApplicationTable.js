"use client";

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { getApplicationsByApplicantId, getApplicationsByApplicantIdAndJobId } from '@/utils/jobApplicationsOps';
import { toast } from 'react-toastify';
import Link from 'next/link';
import { useUser } from '@/context/UserContext';
import { useSearchParams, useRouter } from 'next/navigation';

const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    // Format time in 12-hour format with AM/PM
    const timeString = date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    }).toLowerCase();

    if (diffInDays === 0) {
        return `Today at ${timeString}`;
    } else if (diffInDays === 1) {
        return `Yesterday at ${timeString}`;
    } else if (diffInDays < 7) {
        return `${diffInDays} days ago at ${timeString}`;
    } else if (diffInDays < 30) {
        const weeks = Math.floor(diffInDays / 7);
        return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago at ${timeString}`;
    } else {
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }) + ` at ${timeString}`;
    }
};

const MyApplicationTable = () => {
    const { userDetails } = useUser();
    const searchParams = useSearchParams();
    const router = useRouter();
    const [applications, setApplications] = useState([]);
    const [loading, setLoading] = useState(true);
    const [filteredJobName, setFilteredJobName] = useState('');
    const [pagination, setPagination] = useState({
        total: 0,
        count: 0,
        page: 1,
        pages: 1
    });

    const jobId = searchParams.get('job_id');

    const fetchApplications = async (page = 1) => {
        if (!userDetails?._id) return;
        setLoading(true);
        try {
            let response;
            if (jobId) response = await getApplicationsByApplicantIdAndJobId(userDetails._id, jobId);
            else response = await getApplicationsByApplicantId(userDetails._id, page);
            if (response?.success && response?.data?.data?.items) {
                setApplications(response.data.data.items);
                setPagination({
                    total: response.data.data.total || 0,
                    count: response.data.data.count || 0,
                    page: page,
                    pages: Math.ceil((response.data.data.total || 0) / 10)
                });

                // Set filtered job name if we have job_id and matching application
                if (jobId && response.data.data.items.length > 0) {
                    const jobDetails = response.data.data.items[0]?.job;
                    if (jobDetails) {
                        setFilteredJobName(jobDetails.role || 'Selected Job');
                    }
                }
            } else {
                console.error('Invalid response format:', response);
                toast.error('Failed to fetch applications: Invalid response format');
            }
        } catch (error) {
            console.error("Failed to fetch applications:", error);
            toast.error('Failed to fetch applications');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        console.log('UserDetails:', userDetails);
        if (userDetails?._id) {
            fetchApplications(1);
        }
    }, [userDetails, jobId]);

    const handlePrevPage = () => {
        if (pagination.page > 1) {
            fetchApplications(pagination.page - 1);
        }
    };

    const handleNextPage = () => {
        if (pagination.page < pagination.pages) {
            fetchApplications(pagination.page + 1);
        }
    };

    const handleClearFilter = () => {
        // Remove job_id from URL
        const params = new URLSearchParams(searchParams);
        params.delete('job_id');
        router.replace(`?${params.toString()}`);
        setFilteredJobName('');
    };

    return (
        <div className="space-y-4">
            {/* Pagination with filter info */}
            <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 px-4 py-3 rounded-lg shadow-sm">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                            Page {pagination.page} of {pagination.pages}
                        </p>
                        {jobId && filteredJobName && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                Filtered by: {filteredJobName}
                                <button
                                    onClick={handleClearFilter}
                                    className="ml-1.5 rounded-full bg-blue-200 dark:bg-blue-800/30 p-0.5 hover:bg-blue-300 dark:hover:bg-blue-700/40 focus:outline-none"
                                    aria-label="Clear filter"
                                >
                                    <svg
                                        className="h-3.5 w-3.5"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M6 18L18 6M6 6l12 12"
                                        />
                                    </svg>
                                </button>
                            </span>
                        )}
                    </div>
                    <div className="flex space-x-2">
                        <button
                            onClick={handlePrevPage}
                            disabled={pagination.page <= 1}
                            className={`px-3 py-2 border rounded-md flex items-center ${pagination.page <= 1
                                ? 'border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                                : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer'
                                }`}
                            aria-label="Previous page"
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </button>
                        <button
                            onClick={handleNextPage}
                            disabled={pagination.page >= pagination.pages}
                            className={`px-3 py-2 border rounded-md flex items-center ${pagination.page >= pagination.pages
                                ? 'border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                                : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer'
                                }`}
                            aria-label="Next page"
                        >
                            <ChevronRight className="h-5 w-5" />
                        </button>
                    </div>
                </div>
            </div>
            <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Sl No
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Job Details
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Company
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Location
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Applied On
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Status
                            </th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-900">
                        {loading ? (
                            <tr>
                                <td colSpan="6" className="px-4 py-8 text-center">
                                    <div className="flex justify-center">
                                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                                    </div>
                                </td>
                            </tr>
                        ) : applications.length === 0 ? (
                            <tr>
                                <td colSpan="6" className="px-4 py-8 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No applications found
                                </td>
                            </tr>
                        ) : (
                            applications.map((application, index) => (
                                <tr key={application._id} className="hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                    <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        {((pagination.page - 1) * 10) + index + 1}
                                    </td>
                                    <td className="px-4 py-4">
                                        <div className="font-medium text-gray-900 dark:text-white">
                                            {application.job?.role || 'N/A'}
                                        </div>
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            {console.log({ application })}
                                            <a className="text-blue-500 hover:underline" href={application?.resume?.file?.url} target="_blank" rel="noopener noreferrer">View Resume</a>
                                        </div>
                                    </td>
                                    <td className="px-4 py-4">
                                        <div className="flex items-center">
                                            <span className="text-sm text-gray-900 dark:text-white">
                                                {application.organization?.name || 'N/A'}
                                            </span>
                                        </div>
                                    </td>
                                    <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        {application.job?.locality ? (
                                            <>
                                                {application.job.locality.city}, {application.job.locality.state}
                                            </>
                                        ) : 'Location not specified'}
                                    </td>
                                    <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        {formatRelativeTime(application.createdAt)}
                                    </td>
                                    <td className="whitespace-nowrap px-4 py-4 text-sm">
                                        <span className={`inline-flex rounded-full px-3 py-1 text-xs font-semibold ${application.status === 'READ'
                                                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                                                : application.status === 'PENDING'
                                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                                                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                                            }`}>
                                            {application.status === 'READ' ? 'Recruiter viewed!' : application.status || 'PENDING'}
                                        </span>
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default MyApplicationTable;
