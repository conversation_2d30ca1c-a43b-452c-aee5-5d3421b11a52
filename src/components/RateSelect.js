import React from 'react';

const RateSelect = ({ hourlyRate, setHourlyRate, required = false }) => {
    const handleChange = (e) => {
        setHourlyRate(e.target.value);
    };

    return (
        <div>
            <label htmlFor="salary" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                Rate <span className="text-blue-500"> Optional</span>
            </label>
            <select
                id="salary"
                name="salary"
                value={hourlyRate}
                onChange={handleChange}
                required={required}
                className="w-full p-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-400 border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-[#05668D] dark:focus:ring-[#05668D]/50 focus:border-transparent"
            >
                <option value="">Select hourly rate range</option>
                <option value="Below $40/hr">Below $40/hr</option>
                <option value="$40/hr - $50">$40/hr - $50/hr</option>
                <option value="$50/hr - $60/hr">$50/hr - $60/hr</option>
                <option value="$60/hr - $70/hr">$60/hr - $70/hr</option>
                <option value="$70/hr - $80/hr">$70/hr - $80/hr</option>
                <option value="$80/hr - $90/hr">$80/hr - $90/hr</option>
                <option value="$90/hr - $100/hr">$90/hr - $100/hr</option>
                <option value="$100/hr - $120/hr">$100/hr - $120/hr</option>
                <option value="$120/hr - $150/hr">$120/hr - $150/hr</option>
                <option value="$150/hr - $200/hr">$150/hr - $200/hr</option>
                <option value="$200/hr or above">$200/hr or above</option>
            </select>
        </div>
    );
};

export default RateSelect; 