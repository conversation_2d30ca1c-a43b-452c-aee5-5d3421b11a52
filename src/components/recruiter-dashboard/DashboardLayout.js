"use client"
import { useState } from 'react';
import RecruiterSidebar from './RecruiterSidebar';

export default function DashboardLayout({ children }) {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 px-4 py-8">
            <div className="max-w-6xl mx-auto">
                {/* Header */}
                <div className="mb-6">
                    <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
                        Dashboard
                    </h1>
                </div>

                {/* Main Content Container */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                    <div className="flex">
                        {/* Sidebar - Fixed width */}
                        <div className="w-60 border-r border-gray-200 dark:border-gray-700">
                            <RecruiterSidebar />
                        </div>

                        {/* Main Content - Flexible width */}
                        <div className="flex-1 min-h-[calc(100vh-12rem)] p-6">
                            {children}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
} 