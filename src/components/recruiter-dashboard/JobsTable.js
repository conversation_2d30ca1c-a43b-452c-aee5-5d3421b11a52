"use client";

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import { getDeterministicNumber } from '@/utils/stringOps';
import { getJobs, updateJobStatus, closeJobAndLetCandidatesInform } from '@/utils/jobsOps';
import Link from 'next/link';
import { useUser } from '@/context/UserContext';
import { toast } from 'react-toastify';

const JobsTable = () => {
    const { userDetails } = useUser();
    const [jobs, setJobs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        total: 0,
        count: 0,
        page: 1,
        pages: 1
    });
    const [updatingJobId, setUpdatingJobId] = useState(null);

    const fetchUserJobs = async (page = 1) => {
        if (userDetails && userDetails._id) {
            setLoading(true);
            try {
                const myJobs = await getJobs({
                    posted_by: userDetails._id,
                    page: page,
                    limit: 10,
                    created_at: 'desc'
                });

                setJobs(myJobs.data.items || []);

                // Calculate total pages and ensure it's at least 1
                const totalPages = Math.max(1, Math.ceil((myJobs.data.total || 0) / 10));

                setPagination({
                    total: myJobs.data.total || 0,
                    count: myJobs.data.count || 0,
                    page: parseInt(myJobs.data.page || page),
                    pages: totalPages
                });
            } catch (error) {
                console.error("Failed to fetch jobs:", error);
            } finally {
                setLoading(false);
            }
        }
    };

    useEffect(() => {
        fetchUserJobs(1);
    }, [userDetails]);

    const toggleJobStatus = async (jobId, isActive) => {
        setUpdatingJobId(jobId);
        try {
            await updateJobStatus(jobId, !isActive);
            if (isActive) {
                await closeJobAndLetCandidatesInform(jobId);
            }
            setJobs(prevJobs =>
                prevJobs.map(job =>
                    job._id === jobId ? { ...job, active: !isActive } : job
                )
            );
            toast.success(isActive ? 'Job closed successfully' : 'Job reopened successfully');
        } catch (error) {
            console.error("Failed to update job status:", error);
            toast.error('Failed to update job status');
        } finally {
            setUpdatingJobId(null);
        }
    };

    const handlePrevPage = () => {
        if (pagination.page > 1) {
            const newPage = pagination.page - 1;
            fetchUserJobs(newPage);
        }
    };

    const handleNextPage = () => {
        if (pagination.page < pagination.pages) {
            const newPage = pagination.page + 1;
            fetchUserJobs(newPage);
        }
    };

    return (
        <div className="space-y-4">
            {/* Simple Pagination - Always visible */}
            <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 px-4 py-3 rounded-lg shadow-sm">
                <div className="flex items-center justify-between">
                    <div>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                            Page {pagination.page} of {pagination.pages}
                        </p>
                    </div>

                    <div className="flex space-x-2">
                        <button
                            onClick={handlePrevPage}
                            disabled={pagination.page <= 1}
                            className={`px-3 py-2 border rounded-md flex items-center ${pagination.page <= 1
                                ? 'border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                                : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer'
                                }`}
                            aria-label="Previous page"
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </button>

                        <button
                            onClick={handleNextPage}
                            disabled={pagination.page >= pagination.pages}
                            className={`px-3 py-2 border rounded-md flex items-center ${pagination.page >= pagination.pages
                                ? 'border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                                : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer'
                                }`}
                            aria-label="Next page"
                        >
                            <ChevronRight className="h-5 w-5" />
                        </button>
                    </div>
                </div>
            </div>
            <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Sl No
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Job Title
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Applicants
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Status
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-900">
                        {loading ? (
                            <tr>
                                <td colSpan="5" className="px-4 py-8 text-center">
                                    <div className="flex justify-center">
                                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                                    </div>
                                </td>
                            </tr>
                        ) : jobs.length === 0 ? (
                            <tr>
                                <td colSpan="5" className="px-4 py-8 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No jobs found
                                </td>
                            </tr>
                        ) : (
                            jobs.map((job, index) => (
                                <tr key={job._id} className="hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                    <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        {((pagination.page - 1) * 10) + index + 1}
                                    </td>
                                    <td className="px-4 py-4">
                                        <Link href={`/dashboard/job/${job._id}`} className="flex items-center">
                                            <div className="m-4">
                                                <div className="font-medium text-gray-900 dark:text-white">{job.role}</div>
                                            </div>
                                        </Link>
                                    </td>
                                    <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        {job.no_of_applicants}
                                    </td>
                                    <td className="whitespace-nowrap px-4 py-4 text-sm">
                                        <span className={`inline-flex rounded-full px-3 py-1 text-xs font-semibold ${job.active
                                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                                            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                            }`}>
                                            {job.active ? 'Active' : 'Inactive'}
                                        </span>
                                    </td>
                                    <td className="whitespace-nowrap px-4 text-sm text-left">
                                        <button
                                            onClick={() => toggleJobStatus(job._id, job.active)}
                                            disabled={updatingJobId === job._id}
                                            className={`px-3 py-1 rounded-md text-sm font-medium transition-all duration-300 min-w-[100px]
                                                ${updatingJobId === job._id
                                                    ? 'bg-gray-400 cursor-not-allowed'
                                                    : job.active
                                                        ? 'bg-red-500 text-white hover:bg-red-600'
                                                        : 'bg-green-500 text-white hover:bg-green-600'
                                                }`}
                                        >
                                            {updatingJobId === job._id ? (
                                                <span className="flex items-center justify-center gap-2">
                                                    <Loader2 className="w-4 h-4 animate-spin" />
                                                    {job.active ? 'Closing...' : 'Opening...'}
                                                </span>
                                            ) : (
                                                job.active ? 'Close Job' : 'Open Job'
                                            )}
                                        </button>
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default JobsTable; 