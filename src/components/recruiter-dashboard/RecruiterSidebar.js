"use client"
import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
    Briefcase,
    FileText,
    Users,
    Settings,
} from 'lucide-react';

export default function RecruiterSidebar() {
    const pathname = usePathname();

    const menuItems = [
        {
            name: 'Post Job',
            icon: Briefcase,
            href: '/dashboard/post-job',
            description: 'Create and publish new job listings'
        },
        {
            name: 'My Jobs',
            icon: FileText,
            href: '/dashboard/my-jobs',
            description: 'Manage your active and closed jobs'
        },
        {
            name: 'Applications',
            icon: Users,
            href: '/dashboard/applications',
            description: 'View and manage candidate applications'
        },
        {
            name: 'Settings',
            icon: Settings,
            href: '/dashboard/settings',
            description: 'Manage your account settings'
        },
    ];

    const isActive = (path) => {
        if (path === '/dashboard/post-job') {
            return pathname === '/dashboard' || pathname === '/dashboard/post-job';
        }
        return pathname === path;
    };

    return (
        <nav className="py-4 px-3">
            {menuItems.map((item) => (
                <Link
                    key={item.name}
                    href={item.href}
                    className={`block rounded-md transition-colors duration-150 ease-in-out mb-1 ${isActive(item.href)
                        ? 'bg-[#05668D]/10 text-[#05668D] dark:bg-[#05668D]/20 dark:text-white'
                        : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                        }`}
                >
                    <div className="px-3 py-2">
                        <div className="flex items-center">
                            <item.icon className={`h-5 w-5 mr-3 ${isActive(item.href)
                                ? 'text-[#05668D] dark:text-white'
                                : 'text-gray-500 dark:text-gray-400'
                                }`} />
                            <span className="font-medium">{item.name}</span>
                        </div>
                    </div>
                </Link>
            ))}
        </nav>
    );
}