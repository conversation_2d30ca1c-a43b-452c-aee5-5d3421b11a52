"use client";

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import axios from 'axios';
import { Image as ImageIcon, Linkedin, Copy, Check } from 'lucide-react';
import Image from 'next/image';
import { getJobById } from '@/utils/jobsOps';
import { generateLinkedinPost } from '@/utils/genAiOps';
import { checkLinkedInConnectionFromBackend, postToLinkedIn } from '@/utils/linkedinOps';
import LinkedinIntegration from '@/components/LinkedinIntegration';

export default function LinkedinPost() {
  const params = useParams();
  const jobId = params.id;

  const [job, setJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState(false);
  const [postText, setPostText] = useState('');
  const [linkedinIsConnected, setLinkedinIsConnected] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [isPosted, setIsPosted] = useState(false);
  const [postId, setPostId] = useState(null);

  useEffect(() => {
    const fetchJobDetails = async () => {
      if (!jobId) return;
      try {
        const response = await getJobById(jobId);
        setJob(response.data);
      } catch (error) {
        console.error('Error fetching job details:', error);
      } finally {
        setLoading(false);
      }
    };
    const checkLinkedInConnection = async () => {
      try {
        const response = await checkLinkedInConnectionFromBackend();
        setLinkedinIsConnected(response.data.isConnected);
        console.log("LinkedIn connection status:", response.data.isConnected);
      } catch (error) {
        console.error('Error checking LinkedIn connection:', error);
      }
    };
    fetchJobDetails();
    checkLinkedInConnection();
  }, [jobId]);

  useEffect(() => {
    if (job) {
      setPostText(generateLinkedinPost(job));
    }
  }, [job]);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(postText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handlePostToLinkedIn = async () => {
    try {
      setIsPosting(true);
      const response = await postToLinkedIn(jobId, postText);
      console.log('Posted to LinkedIn successfully:', response.data.postId);
      const newPostId = response.data.postId;
      setPostId(newPostId);
      console.log('Post ID:', newPostId);
      setIsPosted(true);
    } catch (error) {
      console.error('Error posting to LinkedIn:', error);
    } finally {
      setIsPosting(false);
    }
  };

  const handleViewPost = () => {
    if (postId) {
      window.open(`https://www.linkedin.com/feed/update/${postId}`, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <div className='flex justify-between items-center mb-4'>
        <div className="flex items-center gap-2">
          <Image width={100} height={100} src="/assets/images/Li-Logo.png" alt="LinkedIn Logo" />
          <span className="px-3 py-1 bg-gradient-to-r from-pink-500 to-orange-500 text-white text-xs font-semibold rounded-full shadow-sm">
            New
          </span>
        </div>
      </div>
      <div>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-600 dark:text-gray-200">Share now!</h2>
          <button
            onClick={copyToClipboard}
            className="flex items-center gap-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
            {copied ? 'Copied!' : 'Copy Text'}
          </button>
        </div>
        <textarea
          className="w-full min-h-[300px] max-h-[500px] p-4 rounded-lg border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-sm font-linkedin resize-vertical overflow-auto"
          value={postText}
          onChange={e => setPostText(e.target.value)}
          disabled={isPosted}
        />
        <div className='mt-4'>
          {linkedinIsConnected ? (
            <>
              {isPosted ? (
                <div className="w-full bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg p-4 text-center">
                  <div className="flex flex-col items-center justify-center gap-2 text-green-600 dark:text-green-400">
                    <div className="flex items-center gap-2">
                      <Check className="w-5 h-5" />
                      <span>Your job has been successfully posted to LinkedIn!</span>
                    </div>
                    <button
                      onClick={handleViewPost}
                      className="mt-3 bg-[#0A66C2] hover:bg-[#084d91] text-white py-2 px-4 rounded-lg flex items-center gap-2"
                    >
                      <Linkedin className="w-4 h-4" />
                      View Post on LinkedIn
                    </button>
                  </div>
                </div>
              ) : (
                <button
                  onClick={handlePostToLinkedIn}
                  disabled={isPosting || isPosted}
                  className="w-full bg-[#0A66C2] hover:bg-[#084d91] text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isPosting ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Posting...
                    </>
                  ) : (
                    <>
                      <Linkedin className="w-5 h-5" />
                      Post to LinkedIn
                    </>
                  )}
                </button>
              )}
            </>
          ) : (
            <LinkedinIntegration />
          )}
        </div>
      </div>
    </div>
  );
}