"use client";

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Eye, Copy, Check, Phone, X } from 'lucide-react';
import { getApplicationsByJobId, updateApplicationStatus } from '@/utils/jobApplicationsOps';
import { toast } from 'react-toastify';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';

const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    // Format time in 12-hour format with AM/PM
    const timeString = date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    }).toLowerCase(); // Convert AM/PM to lowercase

    if (diffInDays === 0) {
        return `Today @ ${timeString}`;
    } else if (diffInDays === 1) {
        return `Yesterday @ ${timeString}`;
    } else if (diffInDays < 7) {
        return `${diffInDays} days ago at ${timeString}`;
    } else if (diffInDays < 30) {
        const weeks = Math.floor(diffInDays / 7);
        return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago at ${timeString}`;
    } else {
        const dateString = date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
        return `${dateString} at ${timeString}`;
    }
};

const JobApplicationTable = ({ jobId }) => {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const applicantId = searchParams.get('applicant_id');

    const [applications, setApplications] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        total: 0,
        count: 0,
        page: 1,
        pages: 1
    });
    const [copiedEmail, setCopiedEmail] = useState(null);

    const fetchApplications = async (page = 1) => {
        setLoading(true);
        try {
            // If applicantId exists, add it to the query params
            const params = {
                page,
                limit: 10,
            };

            if (applicantId) {
                params.applicant = applicantId;
            }

            const response = await getApplicationsByJobId(jobId, params);

            if (response.success) {
                setApplications(response.data.data.items || []);
                setPagination({
                    total: response.data.data.total || 0,
                    count: response.data.data.count || 0,
                    page: parseInt(response.data.data.page || page),
                    pages: Math.max(1, Math.ceil((response.data.data.total || 0) / 10))
                });
            }
        } catch (error) {
            console.error("Failed to fetch applications:", error);
            toast.error('Failed to fetch applications');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (jobId) {
            fetchApplications(1);
        }
    }, [jobId, applicantId]); // Add applicantId to dependency array

    const handlePrevPage = () => {
        if (pagination.page > 1) {
            fetchApplications(pagination.page - 1);
        }
    };

    const handleNextPage = () => {
        if (pagination.page < pagination.pages) {
            fetchApplications(pagination.page + 1);
        }
    };

    const handleCopyEmail = async (email) => {
        try {
            await navigator.clipboard.writeText(email);
            setCopiedEmail(email);
            toast.info('Email copied!', {
                position: "bottom-right",
                autoClose: 2000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
                progress: undefined,
                theme: "colored",
            });
            setTimeout(() => setCopiedEmail(null), 2000);
        } catch (err) {
            console.error('Failed to copy email:', err);
            toast.error('Failed to copy email', {
                position: "bottom-right",
                autoClose: 2000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
                progress: undefined,
                theme: "colored",
            });
        }
    };

    const handleResumeOnClick = async (application) => {
        window.open(application.resume.file.url, '_blank');
        if (application.status !== 'READ') {
            try {
                await updateApplicationStatus(application._id, 'READ');
                fetchApplications(pagination.page);
                toast.success('Application marked as read!', {
                    position: "bottom-right",
                    autoClose: 2000,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    draggable: true,
                    progress: undefined,
                    theme: "colored",
                });
            } catch (error) {
                console.error('Failed to update application status:', error);
                toast.error('Failed to update status', {
                    position: "bottom-right",
                    autoClose: 2000,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    draggable: true,
                    progress: undefined,
                    theme: "colored",
                });
            }
        }
    };

    const handleClearFilter = () => {
        // Create new URLSearchParams without the applicant_id
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete('applicant_id');

        // Update the URL without the applicant_id parameter
        router.push(`${pathname}?${newSearchParams.toString()}`);
    };

    const filteredApplicantEmail = applications.length > 0 && applicantId
        ? applications[0]?.applicant?.user?.email
        : null;

    return (
        <div className="space-y-4">
            {/* Pagination */}
            <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 px-4 py-3 rounded-lg shadow-sm">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                    <div className="flex items-center gap-4">
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                            Page {pagination.page} of {pagination.pages}
                        </p>

                        {/* Filter Indicator with Email */}
                        {applicantId && filteredApplicantEmail && (
                            <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
                                {filteredApplicantEmail}
                                <button
                                    onClick={handleClearFilter}
                                    className="ml-1 p-0.5 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full transition-colors"
                                    title="Clear filter"
                                >
                                    <X className="h-3 w-3" />
                                </button>
                            </span>
                        )}
                    </div>

                    <div className="flex space-x-2">
                        <button
                            onClick={handlePrevPage}
                            disabled={pagination.page <= 1}
                            className={`px-3 py-2 border rounded-md flex items-center ${pagination.page <= 1
                                ? 'border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                                : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer'
                                }`}
                            aria-label="Previous page"
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </button>
                        <button
                            onClick={handleNextPage}
                            disabled={pagination.page >= pagination.pages}
                            className={`px-3 py-2 border rounded-md flex items-center ${pagination.page >= pagination.pages
                                ? 'border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                                : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer'
                                }`}
                            aria-label="Next page"
                        >
                            <ChevronRight className="h-5 w-5" />
                        </button>
                    </div>
                </div>
            </div>
            <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th scope="col" className="px-2 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Sl No
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Applicants
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Date @ time
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Status
                            </th>
                            <th scope="col" className="px-4 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                                Resume
                            </th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-900">
                        {loading ? (
                            <tr>
                                <td colSpan="5" className="px-4 py-8 text-center">
                                    <div className="flex justify-center">
                                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                                    </div>
                                </td>
                            </tr>
                        ) : applications.length === 0 ? (
                            <tr>
                                <td colSpan="5" className="px-4 py-8 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No applications found
                                </td>
                            </tr>
                        ) : (
                            applications.map((application, index) => (
                                <tr key={application._id} className="hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                    <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        {((pagination.page - 1) * 10) + index + 1}
                                    </td>
                                    <td className="px-4 py-4">
                                        <div className="flex items-center">
                                            <div>
                                                <div className="font-medium text-gray-900 dark:text-white flex items-center gap-3">
                                                    {application.applicant.user.name}
                                                    {application.applicant.phone && (
                                                        <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
                                                            <Phone className="h-3 w-3" />
                                                            {application.applicant.phone}
                                                        </span>
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                                                    {application.applicant.user.email}
                                                    <button
                                                        onClick={() => handleCopyEmail(application.applicant.user.email)}
                                                        className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors"
                                                        title="Copy email"
                                                    >
                                                        {copiedEmail === application.applicant.user.email ? (
                                                            <Check className="h-4 w-4 text-green-500" />
                                                        ) : (
                                                            <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                                                        )}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        {formatRelativeTime(application.createdAt)}
                                    </td>
                                    <td className="whitespace-nowrap px-4 py-4 text-sm">
                                        <span className={`inline-flex rounded-full px-3 py-1 text-xs font-semibold ${application.status === 'READ'
                                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                                            : application.status === 'PENDING'
                                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                                                : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                                            }`}>
                                            {application.status || 'PENDING'}
                                        </span>
                                    </td>
                                    <td className="whitespace-nowrap px-4 py-4 text-sm">
                                        <a
                                            href={application.resume.file.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            onClick={() => handleResumeOnClick(application)}
                                            className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                                        >
                                            View Resume
                                        </a>
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default JobApplicationTable; 