'use client';

import React from 'react';
import Image from 'next/image';
import { ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

const ProfilesCardStack = () => {
    const router = useRouter();

    const handleJoinClick = () => {
        router.push('/club/join');
    };

    return (
        <div className="flex flex-col relative">
            {/* Profile Images Stack */}
            <div className="relative h-16 ml-2 mb-4">
                {[...Array(8)].map((_, index) => (
                    <div
                        key={index}
                        className="absolute top-0 rounded-full overflow-hidden border-2 border-white hover:z-50 transition-transform hover:scale-110"
                        style={{
                            width: '64px',
                            height: '64px',
                            left: `${index * 40}px`, 
                            transform: 'translateY(0)', 
                            zIndex: 8 - index, // Stack order
                        }}
                    >
                        <Image
                            src={`/assets/people/person${index + 1}.jpg`}
                            alt={`Community member ${index + 1}`}
                            width={64}
                            height={64}
                            className="object-cover w-full h-full"
                        />
                    </div>
                ))}
            </div>
            {/* Heading below the stack */}
            <div className="relative z-10 flex items-center gap-2">
                <button
                    onClick={handleJoinClick}
                    className="inline-flex items-center gap-1 px-6 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors duration-200 shadow-lg"
                >
                    <span className="text-lg font-semibold">Join</span>
                    <ArrowRight className="w-5 h-5" />
                </button>
                <span className="text-white text-2xl font-semibold">The contractors club, Be the first to know always!</span>
            </div>
        </div>
    );
};

export default ProfilesCardStack;
