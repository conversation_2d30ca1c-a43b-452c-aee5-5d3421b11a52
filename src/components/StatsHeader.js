'use client';

import { Users, Briefcase, Building2 } from 'lucide-react';

export default function StatsHeader() {
    return (
        <div className="w-full bg-purple-900 text-white py-2">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-center space-x-8">
                    <div className="flex items-center">
                        <Users className="w-5 h-5 text-green-400 mr-2" />
                        <span className="text-sm">10,000+ Customers</span>
                    </div>
                    <div className="flex items-center">
                        <Building2 className="w-5 h-5 text-green-400 mr-2" />
                        <span className="text-sm">500+ Recruiters</span>
                    </div>
                    <div className="flex items-center">
                        <Briefcase className="w-5 h-5 text-green-400 mr-2" />
                        <span className="text-sm">20,000+ Jobs</span>
                    </div>
                </div>
            </div>
        </div>
    );
} 