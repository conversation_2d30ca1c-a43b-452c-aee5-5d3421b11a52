'use client';

import { useState, useEffect } from 'react';
import JobC<PERSON> from './JobCard';
import OldJobCard from './OldJobCard';
import JobFilters from './JobFilters';
import { searchJobs } from '@/utils/jobsOps';
import { Loader2 } from 'lucide-react';

export default function ClientJobList({ initialJobs, initialOldJobs }) {
    const [jobs, setJobs] = useState(initialJobs);
    const [oldJobs, setOldJobs] = useState(initialOldJobs);
    const [isLoading, setIsLoading] = useState(false);

    // Listen for global search events from standalone JobFilters
    useEffect(() => {
        const handleGlobalSearch = async (event) => {
            const query = event.detail.query;
            setIsLoading(true);
            try {
                if (query.length > 0) {
                    const data = await searchJobs(query);
                    setJobs(data.data.items);
                } else {
                    setJobs(initialJobs);
                }
            } catch (error) {
                console.error('Search failed:', error);
                setJobs([]);
            } finally {
                setIsLoading(false);
            }
        };

        if (typeof window !== 'undefined') {
            window.addEventListener('global-job-search', handleGlobalSearch);
        }

        return () => {
            if (typeof window !== 'undefined') {
                window.removeEventListener('global-job-search', handleGlobalSearch);
            }
        };
    }, []);

    const LoadingJobCards = () => (
        <>
            {[1, 2, 3].map((index) => (
                <div key={index} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 animate-pulse">
                    <div className="flex items-start gap-4">
                        {/* Company Logo Placeholder */}
                        <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                        <div className="flex-grow space-y-3">
                            {/* Title Placeholder */}
                            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                            {/* Company Info Placeholders */}
                            <div className="flex gap-4">
                                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                            </div>
                            {/* Skills Placeholders */}
                            <div className="flex gap-2">
                                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                            </div>
                        </div>
                    </div>
                </div>
            ))}
        </>
    );

    return (
        <div>
            <div className="grid gap-6">
                {isLoading ? (
                    <LoadingJobCards />
                ) : (
                    <>
                        {jobs.map((job) => (
                            <div key={job._id}>
                                <JobCard
                                    job={{
                                        _id: job?._id?.toString(),
                                        active: job?.active,
                                        title: job?.role,
                                        company: job?.organization?.name,
                                        location: `${job?.locality?.city}, ${job?.locality?.state}`,
                                        type: job?.job_type,
                                        companyLogo: job?.organization?.logo ? job?.organization?.logo?.url : "https://onlyc2c.com/assets/images/logo.png",
                                        description: job?.description,
                                        skills: job?.skills?.map(skill => skill.name),
                                        postedTime: job?.createdAt,
                                        applicants: job?.no_of_applicants,
                                        recruiterProfileImage: job.posted_by.user["image"],
                                        recruiterEmail: job?.posted_by?.user?.email,
                                        recruiterName: job?.posted_by?.user?.name,
                                        recruiterId: job?.posted_by?.user?._id?.toString(),
                                        jobId: job?.job_id,
                                        verified: job?.verified
                                    }}
                                />
                            </div>
                        ))}
                        {oldJobs.map((job) => (
                            <div key={job._id}>
                                <OldJobCard job={JSON.parse(JSON.stringify(job))} />
                            </div>
                        ))}
                        {jobs.length === 0 && oldJobs.length === 0 && (
                            <div className="text-center py-20">
                                <p className="text-gray-500 dark:text-gray-400">No jobs found</p>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
}