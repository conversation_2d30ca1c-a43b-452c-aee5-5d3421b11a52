'use client'; // Needs to be a client component for state and localStorage

import React, { useState, useEffect } from 'react';
import Link from 'next/link'; // Import Link for navigation

const CookieBanner = () => {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        // Check localStorage only on the client-side after mount
        // Use a try-catch block for environments where localStorage might be blocked
        let consent = null;
        try {
            consent = localStorage.getItem('cookieConsent');
        } catch (error) {
            console.error("Could not access localStorage:", error);
            // Decide how to handle this - maybe always show the banner?
            // Or assume consent if localStorage is inaccessible? For now, let's default to showing it.
        }

        if (consent !== 'true') {
            // Add a small delay before showing the banner
            const timer = setTimeout(() => {
                setIsVisible(true); // Show banner if consent not given or localStorage inaccessible
            }, 1000); // Show after 1 second
            return () => clearTimeout(timer); // Cleanup timer on unmount
        }
    }, []); // Empty dependency array means this runs once on mount

    const handleAccept = () => {
        try {
            localStorage.setItem('cookieConsent', 'true'); // Store consent
            setIsVisible(false); // Hide banner
        } catch (error) {
            console.error("Could not write to localStorage:", error);
            // Optionally hide the banner anyway or show an error
            setIsVisible(false);
        }
    };

    // New handler to simply close the banner without consent
    const handleClose = () => {
        setIsVisible(false);
    };

    if (!isVisible) {
        return null; // Don't render anything if hidden or consent given
    }

    return (
        // Changed positioning, size, added rounded corners and increased shadow
        <div className="fixed bottom-4 right-4 w-full max-w-md p-5 bg-customBlue dark:bg-customBlue text-white shadow-xl rounded-lg z-50">
            {/* Added close button */}
            <button
                onClick={handleClose}
                className="absolute top-2 right-2 p-1 text-white rounded-full hover:bg-white/20 transition-colors"
                aria-label="Close cookie banner"
            >
                {/* Simple X icon using SVG */}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>

            {/* Adjusted internal layout */}
            <div className="flex flex-col gap-3">
                <div className="text-sm">
                    <p className="text-blue-50 dark:text-blue-100 mb-2"> {/* Added margin-bottom */}
                        We use cookies to enhance your browsing experience and ensure basic site functionality. By clicking "I Agree", you consent to our use of cookies.
                    </p>
                    {/* Links stack vertically now */}
                    <div className="flex flex-col items-start text-xs">
                        <Link href="/privacy-policy" legacyBehavior>
                            <a className="underline text-blue-100 dark:text-blue-200 hover:text-white dark:hover:text-blue-100 transition-colors duration-200 mb-1">
                                Privacy Policy
                            </a>
                        </Link>
                        <Link href="/terms-conditions" legacyBehavior>
                            <a className="underline text-blue-100 dark:text-blue-200 hover:text-white dark:hover:text-blue-100 transition-colors duration-200">
                                Terms & Conditions
                            </a>
                        </Link>
                    </div>
                </div>
                {/* Adjusted button container */}
                <div className="flex justify-end mt-2"> {/* Added margin-top and align button right */}
                    <button
                        onClick={handleAccept}
                        // Adjusted padding slightly
                        className="px-5 py-2 bg-white dark:bg-gray-100 text-customBlue dark:text-customBlue font-semibold text-sm rounded-lg shadow hover:bg-gray-50 dark:hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-customBlue dark:focus:ring-offset-customBlue"
                    >
                        I Agree
                    </button>
                </div>
            </div>
        </div>
    );
};

export default CookieBanner;
