'use client';

import { useState } from 'react';
import axios from 'axios';
import { Check, Crown, Loader2 } from 'lucide-react';

interface PricingPlan {
    id: string;
    name: string;
    price: string;
    period: string;
    features: string[];
    popular?: boolean;
    planType: string;
}

const plans: PricingPlan[] = [
    {
        id: 'basic',
        name: 'Basic',
        price: '$9.99',
        period: 'month',
        planType: 'candidate_basic',
        features: [
            'Priority job notifications',
            'Enhanced profile visibility',
            'Basic application tracking',
            'Email support'
        ]
    },
    {
        id: 'pro',
        name: 'Pro',
        price: '$19.99',
        period: 'month',
        planType: 'candidate_pro',
        popular: true,
        features: [
            'All Basic features',
            'Advanced job matching',
            'Profile promotion to recruiters',
            'Priority customer support',
            'Application analytics',
            'Resume optimization tips'
        ]
    }
];

export default function CandidatePricingCard() {
    const [loading, setLoading] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);

    const handleSubscribe = async (plan: PricingPlan) => {
        setLoading(plan.id);
        setError(null);

        try {
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/subscriptions/premium/candidates/create-checkout-session`,
                {
                    planType: plan.planType,
                    successUrl: `${window.location.origin}/subscription/candidates/success`,
                    cancelUrl: `${window.location.origin}/subscription/candidates/pricing`
                }
            );

            if (response.data.data.sessionUrl) {
                window.location.href = response.data.data.sessionUrl;
            }
        } catch (error: any) {
            console.error('Error creating checkout session:', error);
            setError(error.response?.data?.message || 'Failed to start subscription process');
        } finally {
            setLoading(null);
        }
    };

    return (
        <div className="py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-12">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                        Choose Your Plan
                    </h2>
                    <p className="text-xl text-gray-600 dark:text-gray-400">
                        Unlock premium features to accelerate your job search
                    </p>
                </div>

                {error && (
                    <div className="max-w-md mx-auto mb-8 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                        <p className="text-red-700 dark:text-red-300 text-center">{error}</p>
                    </div>
                )}

                <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                    {plans.map((plan) => (
                        <div
                            key={plan.id}
                            className={`relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden ${
                                plan.popular ? 'ring-2 ring-blue-500' : 'border border-gray-200 dark:border-gray-700'
                            }`}
                        >
                            {plan.popular && (
                                <div className="absolute top-0 left-0 right-0 bg-blue-500 text-white text-center py-2 text-sm font-medium">
                                    Most Popular
                                </div>
                            )}

                            <div className={`p-8 ${plan.popular ? 'pt-16' : ''}`}>
                                <div className="text-center mb-8">
                                    <div className="flex items-center justify-center mb-4">
                                        <Crown className={`h-8 w-8 ${plan.popular ? 'text-blue-500' : 'text-gray-400'}`} />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                        {plan.name}
                                    </h3>
                                    <div className="flex items-baseline justify-center">
                                        <span className="text-4xl font-bold text-gray-900 dark:text-white">
                                            {plan.price}
                                        </span>
                                        <span className="text-gray-600 dark:text-gray-400 ml-1">
                                            /{plan.period}
                                        </span>
                                    </div>
                                </div>

                                <ul className="space-y-4 mb-8">
                                    {plan.features.map((feature, index) => (
                                        <li key={index} className="flex items-start">
                                            <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                                            <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                                        </li>
                                    ))}
                                </ul>

                                <button
                                    onClick={() => handleSubscribe(plan)}
                                    disabled={loading === plan.id}
                                    className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                                        plan.popular
                                            ? 'bg-blue-600 hover:bg-blue-700 text-white'
                                            : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
                                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                                >
                                    {loading === plan.id ? (
                                        <div className="flex items-center justify-center">
                                            <Loader2 className="h-5 w-5 animate-spin mr-2" />
                                            Processing...
                                        </div>
                                    ) : (
                                        `Subscribe to ${plan.name}`
                                    )}
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="text-center mt-12">
                    <p className="text-gray-600 dark:text-gray-400">
                        All plans include a 14-day free trial. Cancel anytime.
                    </p>
                </div>
            </div>
        </div>
    );
}
