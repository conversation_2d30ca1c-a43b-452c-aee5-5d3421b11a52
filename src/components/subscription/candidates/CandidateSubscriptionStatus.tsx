'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';
import { Crown, Calendar, AlertCircle, CheckCircle } from 'lucide-react';

interface SubscriptionData {
    hasSubscription: boolean;
    isActive: boolean;
    subscription?: {
        id: string;
        status: string;
        planType: string;
        currentPeriodStart: string;
        currentPeriodEnd: string;
        cancelAtPeriodEnd: boolean;
        createdAt: string;
        updatedAt: string;
    };
}

export default function CandidateSubscriptionStatus() {
    const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchSubscriptionStatus();
    }, []);

    const fetchSubscriptionStatus = async () => {
        try {
            const response = await axios.get(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/subscriptions/premium/candidates/status`
            );
            setSubscriptionData(response.data.data);
        } catch (error: any) {
            console.error('Error fetching subscription status:', error);
            setError(error.response?.data?.message || 'Failed to fetch subscription status');
        } finally {
            setLoading(false);
        }
    };

    const handleManageSubscription = async () => {
        try {
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/subscriptions/premium/candidates/create-portal-session`,
                {
                    returnUrl: window.location.href
                }
            );
            
            if (response.data.data.portalUrl) {
                window.location.href = response.data.data.portalUrl;
            }
        } catch (error: any) {
            console.error('Error creating portal session:', error);
            setError(error.response?.data?.message || 'Failed to open subscription management');
        }
    };

    if (loading) {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
                <div className="flex items-center">
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-red-700 dark:text-red-300">{error}</span>
                </div>
            </div>
        );
    }

    if (!subscriptionData?.hasSubscription) {
        return (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                <div className="flex items-center mb-4">
                    <Crown className="h-6 w-6 text-blue-500 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Premium Subscription
                    </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                    You don't have an active premium subscription. Upgrade to access premium features!
                </p>
                <button
                    onClick={() => window.location.href = '/subscription/candidates/pricing'}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                    View Plans
                </button>
            </div>
        );
    }

    const { subscription, isActive } = subscriptionData;
    const endDate = subscription ? new Date(subscription.currentPeriodEnd).toLocaleDateString() : '';

    return (
        <div className={`rounded-lg shadow-md p-6 ${
            isActive 
                ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' 
                : 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'
        }`}>
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                    {isActive ? (
                        <CheckCircle className="h-6 w-6 text-green-500 mr-2" />
                    ) : (
                        <AlertCircle className="h-6 w-6 text-yellow-500 mr-2" />
                    )}
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Premium Subscription
                    </h3>
                </div>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    isActive 
                        ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                }`}>
                    {subscription?.status.toUpperCase()}
                </span>
            </div>

            <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>
                        {subscription?.cancelAtPeriodEnd 
                            ? `Expires on ${endDate}` 
                            : `Renews on ${endDate}`
                        }
                    </span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                    Plan: <span className="font-medium">{subscription?.planType.replace('_', ' ').toUpperCase()}</span>
                </div>
            </div>

            {subscription?.cancelAtPeriodEnd && (
                <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3 mb-4">
                    <p className="text-sm text-yellow-800 dark:text-yellow-200">
                        Your subscription is set to cancel at the end of the current period.
                    </p>
                </div>
            )}

            <button
                onClick={handleManageSubscription}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
                Manage Subscription
            </button>
        </div>
    );
}
