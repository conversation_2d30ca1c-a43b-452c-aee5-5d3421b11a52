"use client";

import { useState, useEffect, useRef } from "react";
import { Wand2 } from "lucide-react";
import TypewriterText from './TypewriterText';

const SimpleTextEditor = ({ value, setDescription, setDescriptionHtml, onAiTextGenerativeButtonClick, aiText }) => {
  const [content, setContent] = useState(value || "");
  const [isMounted, setIsMounted] = useState(false);
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const completionTimeoutRef = useRef(null);

  useEffect(() => {
    setIsMounted(true);
    return () => {
      if (completionTimeoutRef.current) {
        clearTimeout(completionTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (aiText) {
      setContent(aiText);
      setDescription(aiText);
      setDescriptionHtml(`<p>${aiText}</p>`);
      setIsTypingComplete(false);
    }
  }, [aiText]);

  useEffect(() => {
    setContent(value);
  }, [value]);

  const handleTypingComplete = () => {
    completionTimeoutRef.current = setTimeout(() => {
      setIsTypingComplete(true);
    }, 0);
  };

  const handleChange = (e) => {
    const newValue = e.target.value;
    setContent(newValue);
    setDescription(newValue);
    setDescriptionHtml(`<p>${newValue}</p>`);
  };

  if (!isMounted) return null;

  return (
    <div className="flex flex-col gap-4 w-full max-w-4xl mx-auto">
      {/* Toolbar with AI Button */}
      <div className="flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <button
          onClick={(e) => {
            e.preventDefault();
            setIsTypingComplete(false);
            onAiTextGenerativeButtonClick();
          }}
          className="flex items-center gap-2 px-3 py-2 rounded-md bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity"
          title="Write with AI"
        >
          <Wand2 className="w-5 h-5" />
          <span className="font-medium">Write with AI</span>
        </button>
      </div>

      {/* Show TypewriterText when aiText is available and typing is not complete */}
      {aiText && !isTypingComplete ? (
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 min-h-[200px] bg-white dark:bg-gray-800 text-gray-900 dark:text-white ai-border-shine">
          <TypewriterText
            text={aiText}
            speed={15}
            onComplete={handleTypingComplete}
          />
        </div>
      ) : (
        <textarea
          value={content}
          onChange={handleChange}
          placeholder="Start typing..."
          className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 min-h-[200px] bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#05668D] focus:ring-offset-2 resize-y"
        />
      )}
      {/* Character Count */}
      <div className="text-sm text-gray-500 dark:text-gray-400 text-right">
        {content.length} characters
      </div>
    </div>
  );
};

export default SimpleTextEditor;