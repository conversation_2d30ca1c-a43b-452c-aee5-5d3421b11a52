"use client";
import React, { useState, useRef, useEffect } from 'react';
import { states } from '../utils/states'; // You'll need to create this
import axios from 'axios';
import { Loader2 } from 'lucide-react';

const Location = ({ location, setLocation, required = false, setLocationWithCityAndStateForAiText }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [isCreatingNew, setIsCreatingNew] = useState(false);
    const [newLocation, setNewLocation] = useState({
        state: '',
        city: ''
    });
    const [locations, setLocations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedLocationDisplay, setSelectedLocationDisplay] = useState('');
    const wrapperRef = useRef(null);

    // Click outside handler
    useEffect(() => {
        function handleClickOutside(event) {
            if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        }
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Fetch locations and set initial selected location
    useEffect(() => {
        const fetchLocations = async () => {
            try {
                const response = await axios.get(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/locality`);
                const items = response.data.data.items;
                setLocations(items);

                // If we have a location ID, find and set its display value
                if (location) {
                    const selectedLoc = items.find(item => item._id === location);
                    if (selectedLoc) {
                        const displayText = `${selectedLoc.city}, ${selectedLoc.state}`;
                        setSelectedLocationDisplay(displayText);
                        setSearchTerm(displayText);
                        setLocationWithCityAndStateForAiText(displayText);
                    }
                }
            } catch (error) {
                console.error('Error fetching locations:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchLocations();
    }, [location, setLocationWithCityAndStateForAiText]);

    const handleInputChange = (e) => {
        setSearchTerm(e.target.value);
        setIsOpen(true);
    };

    const handleLocationSelect = (selectedLoc) => {
        const displayText = `${selectedLoc.city}, ${selectedLoc.state}`;
        setLocation(selectedLoc._id);
        setSearchTerm(displayText);
        setSelectedLocationDisplay(displayText);
        setLocationWithCityAndStateForAiText(displayText);
        setIsOpen(false);
    };

    const handleNewLocationSubmit = async () => {
        if (!newLocation.state || !newLocation.city) return;

        try {
            setLoading(true);
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/locality`,
                {
                    city: newLocation.city,
                    state: newLocation.state,
                    country: "USA"
                }
            );

            const createdLocation = response.data.data;
            setLocations(prev => [...prev, createdLocation]);

            // Set the newly created location as selected
            const displayText = `${createdLocation.city}, ${createdLocation.state}`;
            setLocation(createdLocation._id);
            setSearchTerm(displayText);
            setSelectedLocationDisplay(displayText);
            setLocationWithCityAndStateForAiText(displayText);

            setIsCreatingNew(false);
            setNewLocation({ state: '', city: '' });
            setIsOpen(false);
        } catch (error) {
            console.error('Error creating new location:', error);
            alert('Failed to create new location. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const filteredLocations = locations.filter(loc =>
        `${loc.city}, ${loc.state}`.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <div className="relative w-full" ref={wrapperRef}>
            <div className="relative">
                <input
                    type="text"
                    value={searchTerm}
                    onChange={handleInputChange}
                    onClick={() => setIsOpen(true)}
                    placeholder="Select a location"
                    required={required}
                    className="w-full p-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-[#05668D] dark:focus:ring-[#05668D]/50 focus:border-transparent pr-20"
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                    {location && !loading && (
                        <button
                            type="button"
                            onClick={() => {
                                setLocation('');
                                setSearchTerm('');
                                setSelectedLocationDisplay('');
                                setLocationWithCityAndStateForAiText('');
                            }}
                            className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
                            title="Clear location"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    )}
                    {loading && <Loader2 className="w-5 h-5 animate-spin text-gray-400" />}
                </div>
            </div>

            {isOpen && !isCreatingNew && (
                <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {loading ? (
                        <div className="p-3 text-gray-500 dark:text-gray-400 flex items-center justify-center">
                            <Loader2 className="w-5 h-5 animate-spin mr-2" />
                            Loading locations...
                        </div>
                    ) : filteredLocations.length > 0 ? (
                        <>
                            {filteredLocations.map((loc) => (
                                <div
                                    key={loc._id}
                                    className={`p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${loc._id === location ? 'bg-gray-100 dark:bg-gray-700' : ''
                                        }`}
                                    onClick={() => handleLocationSelect(loc)}
                                >
                                    <span className="text-gray-900 dark:text-white">{`${loc.city}, ${loc.state}`}</span>
                                </div>
                            ))}
                            <div
                                className="p-3 cursor-pointer text-[#05668D] dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 border-t border-gray-300 dark:border-gray-700"
                                onClick={() => setIsCreatingNew(true)}
                            >
                                + Add new location
                            </div>
                        </>
                    ) : (
                        <div
                            className="p-3 cursor-pointer text-[#05668D] dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => setIsCreatingNew(true)}
                        >
                            + Add new location
                        </div>
                    )}
                </div>
            )}

            {isOpen && isCreatingNew && (
                <div className="absolute z-10 w-full mt-1 p-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-lg">
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                                State
                            </label>
                            <select
                                value={newLocation.state}
                                onChange={(e) => setNewLocation({ ...newLocation, state: e.target.value })}
                                className="w-full p-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-[#05668D] dark:focus:ring-[#05668D]/50 focus:border-transparent"
                            >
                                <option value="">Select a state</option>
                                {states.map((state) => (
                                    <option key={state.abbreviation} value={state.name}>
                                        {state.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                                City
                            </label>
                            <input
                                type="text"
                                value={newLocation.city}
                                onChange={(e) => setNewLocation({ ...newLocation, city: e.target.value })}
                                className="w-full p-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-[#05668D] dark:focus:ring-[#05668D]/50 focus:border-transparent"
                                placeholder="Enter city name"
                            />
                        </div>
                        <div className="flex space-x-2">
                            <button
                                type="button"
                                onClick={() => setIsCreatingNew(false)}
                                className="flex-1 p-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
                            >
                                Cancel
                            </button>
                            <button
                                type="button"
                                onClick={handleNewLocationSubmit}
                                disabled={!newLocation.state || !newLocation.city || loading}
                                className="flex-1 p-2 text-white bg-[#05668D] rounded-lg hover:bg-[#05668D]/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                            >
                                {loading ? (
                                    <>
                                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                        Adding...
                                    </>
                                ) : (
                                    'Add Location'
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Location;
