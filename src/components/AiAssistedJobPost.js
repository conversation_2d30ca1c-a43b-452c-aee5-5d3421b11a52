'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, ArrowLeft, Loader2, Eye, ChevronDown, ChevronUp, Pencil, Linkedin, ExternalLink } from 'lucide-react';
import { extractJobDetailsFromJobDescription } from '@/utils/genAiOps';
import SkillsSelect from './SkillsSelect';
import Location from './Location';
import { createJob } from '@/utils/jobsOps';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';

const JOB_TYPES = ['ONSITE', 'HYBRID', 'REMOTE'];

const AiAssistedJobPost = () => {
    const router = useRouter();
    const [step, setStep] = useState(1);
    const [jobDescription, setJobDescription] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [parsedJobData, setParsedJobData] = useState({
        role: '',
        type: 'ONSITE',
        description: '',
        location: '',
        skills: []
    });
    const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
    const [editingField, setEditingField] = useState(null);
    const [isPosting, setIsPosting] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [postedJobId, setPostedJobId] = useState(null);
    const [acceptC2C, setAcceptC2C] = useState(true);

    // Store both IDs and names for skills
    const [selectedSkills, setSelectedSkills] = useState([]);
    const [skillsWithNames, setSkillsWithNames] = useState([]);

    // Store both ID and display name for location
    const [selectedLocation, setSelectedLocation] = useState('');
    const [locationDisplay, setLocationDisplay] = useState('');

    // Load draft job from localStorage on component mount
    useEffect(() => {
        const draftJob = localStorage.getItem('draftJob');
        if (draftJob) {
            setJobDescription(draftJob);
        }
    }, []);

    const handleProcessDescription = async () => {
        setIsProcessing(true);
        try {
            const response = await extractJobDetailsFromJobDescription(jobDescription);
            // If we have data from AI, use it, otherwise use initial empty data
            if (response && (response.role || response.skills?.length > 0 || response.location)) {
                // Handle skills with both IDs and names
                if (response.skills && response.skills.length > 0) {
                    const uniqueSkills = response.skills.filter((skill, index, self) =>
                        index === self.findIndex((s) => s.id === skill.id)
                    );
                    setSelectedSkills(uniqueSkills.map(skill => skill.id));
                    setSkillsWithNames(uniqueSkills.map(skill => skill.name));
                }

                // Handle location with both ID and name
                if (response.location) {
                    setSelectedLocation(response.location.id);
                    setLocationDisplay(response.location.name);
                }

                setParsedJobData({
                    ...response,
                    type: response.type || 'ONSITE',
                    description: jobDescription,
                    location: response.location?.name || '',
                    skills: response.skills?.map(skill => skill.name) || [],
                    locationData: response.location,
                    skillsData: response.skills
                });
            } else {
                // No data extracted, set up empty form with original description
                setParsedJobData({
                    role: '',
                    type: 'ONSITE',
                    description: jobDescription,
                    location: '',
                    skills: []
                });
                // Add these lines to be certain:
                setSelectedSkills([]);
                setSkillsWithNames([]);
            }
        } catch (error) {
            console.error('Error:', error);
            // On error, still proceed with original description
            setParsedJobData({
                role: '',
                type: 'ONSITE',
                description: jobDescription,
                location: '',
                skills: []
            });
            // Also add these lines here:
            setSelectedSkills([]);
            setSkillsWithNames([]);
        } finally {
            setIsProcessing(false);
            setStep(2); // Always proceed to step 2
        }
    };

    const handleFinalSubmit = async () => {
        setIsPosting(true);
        try {
            const userDetails = JSON.parse(localStorage.getItem('userDetails'));

            const jobData = {
                description: parsedJobData.description,
                descriptionHtml: `<p>${parsedJobData.description}</p>`,
                jobRole: parsedJobData.role,
                skills: selectedSkills,
                location: selectedLocation,
                jobType: parsedJobData.type,
                hourlyRate: "NOT_APPLICABLE",
                userDetails: userDetails
            };

            const response = await createJob(jobData);
            setPostedJobId(response.data.data._id);
            setShowSuccessModal(true);

            // Clear draft after successful posting
            localStorage.removeItem('draftJob');

        } catch (error) {
            console.error('Error posting job:', error);
            toast.error('Failed to post job. Please try again.', {
                duration: 3000,
                position: 'top-center',
            });
            setIsPosting(false);
        }
    };

    const handleViewListing = () => {
        router.push(`/dashboard/my-jobs`);
    };

    const handlePostToSocials = () => {
        router.push(`/dashboard/post-in-socials/${postedJobId}`);
    };

    const toggleDescription = () => {
        setIsDescriptionExpanded(!isDescriptionExpanded);
    };

    const handleFieldEdit = (field, value) => {
        setParsedJobData(prev => {
            const updates = { ...prev };

            if (field === 'location') {
                // For location, we update both display value and maintain the ID
                updates.location = value; // Display value
                // locationData will be updated separately when saving in EditableField
            } else if (field === 'skills') {
                // For skills, we update both display values and maintain the IDs
                updates.skills = skillsWithNames; // Display values
                // skillsData will be updated separately when saving in EditableField
            } else {
                updates[field] = value;
            }

            return updates;
        });
        setEditingField(null);
    };

    const EditableField = ({ field, value, type = 'text', placeholder = '' }) => {
        const [editValue, setEditValue] = useState(value);
        const [isEditing, setIsEditing] = useState(false);

        // Update edit value when value prop changes
        useEffect(() => {
            setEditValue(value);
        }, [value]);

        if (isEditing || editingField === field) {
            return (
                <div className="flex gap-2">
                    {type === 'location' ? (
                        <div className="w-full">
                            <Location
                                location={selectedLocation}
                                setLocation={(locationId) => {
                                    setSelectedLocation(locationId);
                                }}
                                setLocationWithCityAndStateForAiText={(displayText) => {
                                    setLocationDisplay(displayText);
                                }}
                            />
                            <div className="flex justify-end mt-4 gap-2">
                                <button
                                    onClick={() => {
                                        if (selectedLocation || locationDisplay) {
                                            handleFieldEdit('location', locationDisplay);
                                            setParsedJobData(prev => ({
                                                ...prev,
                                                location: locationDisplay,
                                                locationData: {
                                                    id: selectedLocation,
                                                    name: locationDisplay
                                                }
                                            }));
                                            setIsEditing(false);
                                            setEditingField(null);
                                        }
                                    }}
                                    className="px-3 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                                >
                                    Save
                                </button>
                                {value && ( // Only show cancel if there was a previous value
                                    <button
                                        onClick={() => {
                                            setIsEditing(false);
                                            setEditingField(null);
                                        }}
                                        className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                )}
                            </div>
                        </div>
                    ) : type === 'skills' ? (
                        <div className="w-full">
                            <SkillsSelect
                                skills={selectedSkills}
                                setSkills={setSelectedSkills}
                                skillsWithNamesForAiText={skillsWithNames}
                                setSkillsWithNamesForAiText={setSkillsWithNames}
                            />
                            <div className="flex justify-end mt-4 gap-2">
                                <button
                                    onClick={() => {
                                        setParsedJobData(prev => ({
                                            ...prev,
                                            skills: [...skillsWithNames],
                                            skillsData: selectedSkills.map((id, index) => ({ id, name: skillsWithNames[index] }))
                                        }));
                                        setIsEditing(false);
                                        setEditingField(null);
                                    }}
                                    className="px-3 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                                >
                                    Save
                                </button>
                                <button
                                    onClick={() => {
                                        const originalSkillsData = parsedJobData.skillsData || [];
                                        const originalIds = originalSkillsData.map(skill => skill.id);
                                        const originalNames = originalSkillsData.map(skill => skill.name);

                                        setSelectedSkills(originalIds);
                                        setSkillsWithNames(originalNames);

                                        setParsedJobData(prev => ({
                                            ...prev,
                                            skills: [...originalNames]
                                        }));

                                        setIsEditing(false);
                                        setEditingField(null);
                                    }}
                                    className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    ) : type === 'select' ? (
                        <div className="w-full">
                            <select
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className="w-full p-2 border border-gray-200 dark:border-gray-600 rounded-lg 
                                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white 
                                         focus:ring-2 focus:ring-blue-500/50 focus:outline-none"
                            >
                                {JOB_TYPES.map((type) => (
                                    <option key={type} value={type}>
                                        {type}
                                    </option>
                                ))}
                            </select>
                            <div className="flex justify-end mt-4 gap-2">
                                <button
                                    onClick={() => {
                                        handleFieldEdit(field, editValue);
                                        setIsEditing(false);
                                    }}
                                    className="px-3 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                                >
                                    Save
                                </button>
                                {value && ( // Only show cancel if there was a previous value
                                    <button
                                        onClick={() => {
                                            setEditValue(value);
                                            setIsEditing(false);
                                        }}
                                        className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                )}
                            </div>
                        </div>
                    ) : type === 'textarea' ? (
                        <div className="w-full">
                            <textarea
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className="w-full p-2 border border-gray-200 dark:border-gray-600 rounded-lg 
                                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white 
                                         focus:ring-2 focus:ring-blue-500/50 focus:outline-none"
                                rows={4}
                                placeholder={placeholder}
                            />
                            <div className="flex justify-end mt-4 gap-2">
                                <button
                                    onClick={() => {
                                        if (editValue.trim()) {
                                            handleFieldEdit(field, editValue);
                                            setIsEditing(false);
                                        }
                                    }}
                                    className="px-3 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                                >
                                    Save
                                </button>
                                {value && ( // Only show cancel if there was a previous value
                                    <button
                                        onClick={() => {
                                            setEditValue(value);
                                            setIsEditing(false);
                                        }}
                                        className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                )}
                            </div>
                        </div>
                    ) : (
                        <div className="w-full">
                            <input
                                type="text"
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className="w-full p-2 border border-gray-200 dark:border-gray-600 rounded-lg 
                                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white 
                                         focus:ring-2 focus:ring-blue-500/50 focus:outline-none"
                                placeholder={placeholder}
                            />
                            <div className="flex justify-end mt-4 gap-2">
                                <button
                                    onClick={() => {
                                        if (editValue.trim()) {
                                            handleFieldEdit(field, editValue);
                                            setIsEditing(false);
                                        }
                                    }}
                                    className="px-3 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                                >
                                    Save
                                </button>
                                {value && ( // Only show cancel if there was a previous value
                                    <button
                                        onClick={() => {
                                            setEditValue(value);
                                            setIsEditing(false);
                                        }}
                                        className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        return (
            <div>
                <button
                    onClick={() => {
                        setIsEditing(true);
                        setEditingField(field);
                    }}
                    className="absolute top-0 right-0 p-1.5 rounded-lg opacity-0 group-hover:opacity-100 bg-gray-100 dark:bg-gray-600 hover:bg-blue-100 dark:hover:bg-blue-700 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-200 transition-all duration-200"
                >
                    <Pencil className="w-4 h-4" />
                </button>
                {type === 'textarea' ? (
                    <div className="prose dark:prose-invert max-w-none text-gray-700 dark:text-gray-300"
                        dangerouslySetInnerHTML={{ __html: value.replace(/\n/g, '<br/>') }} />
                ) : type === 'skills' ? (
                    <div className="flex flex-wrap gap-2">
                        {value.map((skill, index) => (
                            <span
                                key={index}
                                className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full text-sm"
                            >
                                {skill}
                            </span>
                        ))}
                    </div>
                ) : type === 'location' ? (
                    <p className="text-gray-700 dark:text-gray-300 pr-8">{value || 'No location set'}</p>
                ) : (
                    <p className="text-gray-700 dark:text-gray-300 pr-8">{value || <span className="text-gray-400 dark:text-gray-500 italic">Click to add {field}</span>}</p>
                )}
            </div>
        );
    };

    return (
        <div className="max-w-3xl mx-auto p-6">
            {step === 1 ? (
                <div className="space-y-6">
                    <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600">
                        <div className="flex items-center gap-2 mb-4">
                            <span className="text-gray-600 dark:text-gray-300">
                                Paste your job description or email here!
                            </span>
                        </div>
                        <textarea
                            value={jobDescription}
                            onChange={(e) => setJobDescription(e.target.value)}
                            placeholder="Example: We are looking for a Senior React Developer in New York. The role is full-time and remote-friendly. The ideal candidate should have 5+ years of experience in React, Redux, and TypeScript..."
                            className="w-full h-64 p-4 border border-gray-200 dark:border-gray-600 rounded-lg 
                                     bg-white dark:bg-gray-700 
                                     text-gray-900 dark:text-white 
                                     placeholder-gray-400 dark:placeholder-gray-500
                                     focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 dark:focus:border-blue-500
                                     focus:outline-none transition-all duration-200"
                        />
                        <div className="flex justify-end mt-4">
                            <button
                                onClick={handleProcessDescription}
                                disabled={!jobDescription.trim() || isProcessing}
                                className={`flex items-center gap-2 px-6 py-3 rounded-lg text-white transition-all duration-200
                                    ${!jobDescription.trim() || isProcessing
                                        ? 'bg-gray-300 dark:bg-gray-600 cursor-not-allowed'
                                        : 'bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 shadow-lg hover:shadow-blue-500/50 dark:hover:shadow-blue-600/50'
                                    }`}
                            >
                                {isProcessing ? (
                                    <>
                                        <Loader2 className="w-5 h-5 animate-spin" />
                                        Processing...
                                    </>
                                ) : (
                                    <>
                                        <Sparkles className="w-5 h-5" />
                                        Review and Post Job
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="space-y-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-100 dark:border-gray-700">
                        <div className="flex items-center gap-2 mb-6">
                            <Eye className="w-5 h-5 text-green-500" />
                            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                                {parsedJobData.role || parsedJobData.skills.length > 0 ?
                                    'Review Job Details' :
                                    'Enter Job Details'}
                            </h2>
                        </div>

                        <div className="space-y-4">
                            <div className="grid gap-4">
                                <div className="group relative p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600">
                                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">Job Role</h3>
                                    <EditableField
                                        field="role"
                                        value={parsedJobData.role}
                                        placeholder="Enter the job role (e.g., Senior Software Engineer)"
                                    />
                                </div>
                                <div className="group relative p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600">
                                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">Job Type</h3>
                                    <EditableField field="type" value={parsedJobData.type} type="select" />
                                </div>
                                <div className="group relative p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600">
                                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">Location</h3>
                                    <EditableField field="location" value={parsedJobData.location} type="location" />
                                </div>
                                <div className="group relative p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600">
                                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">Required Skills</h3>
                                    <EditableField field="skills" value={parsedJobData.skills} type="skills" />
                                </div>
                                <div className="group relative p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600">
                                    <div className="flex items-center justify-between">
                                        <button
                                            onClick={toggleDescription}
                                            className="flex items-center gap-2"
                                        >
                                            <h3 className="font-medium text-gray-900 dark:text-white">Description</h3>
                                            {isDescriptionExpanded ? (
                                                <ChevronUp className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                                            ) : (
                                                <ChevronDown className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                                            )}
                                        </button>
                                    </div>
                                    <div
                                        className={`mt-2 overflow-hidden transition-all duration-300 ease-in-out ${isDescriptionExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'
                                            }`}
                                    >
                                        <EditableField
                                            field="description"
                                            value={parsedJobData.description}
                                            type="textarea"
                                            placeholder="Enter a detailed job description..."
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="mt-6 mb-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600">
                            <label className="flex items-start gap-3 cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={acceptC2C}
                                    onChange={(e) => setAcceptC2C(e.target.checked)}
                                    className="mt-1 h-4 w-4 rounded border-gray-300 dark:border-gray-600 
                                             text-blue-600 focus:ring-blue-500 
                                             dark:bg-gray-700 dark:checked:bg-blue-600"
                                />
                                <span className="text-sm text-gray-700 dark:text-gray-300">
                                    I understand that ONLYC2C allows jobs that are OPEN to Corp2Corp as well, and I am considering third-party consultants as well.
                                </span>
                            </label>
                        </div>

                        <div className="flex justify-between mt-6">
                            <button
                                onClick={() => setStep(1)}
                                className="px-6 py-3 rounded-lg text-gray-700 dark:text-gray-300 
                                         hover:bg-gray-100 dark:hover:bg-gray-700 
                                         border border-gray-200 dark:border-gray-600
                                         transition-all duration-200
                                         flex items-center gap-2"
                                disabled={isPosting}
                            >
                                <ArrowLeft className="w-5 h-5" /> Back
                            </button>
                            <button
                                onClick={handleFinalSubmit}
                                disabled={isPosting || !parsedJobData.role.trim() || !acceptC2C}
                                className={`flex items-center gap-2 px-6 py-3 rounded-lg text-white transition-all duration-200
                                    ${(isPosting || !parsedJobData.role.trim() || !acceptC2C)
                                        ? 'bg-gray-300 dark:bg-gray-600 cursor-not-allowed'
                                        : 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 shadow-lg hover:shadow-green-500/50 dark:hover:shadow-green-600/50'
                                    }`}
                            >
                                {isPosting ? (
                                    <>
                                        <Loader2 className="w-5 h-5 animate-spin" />
                                        Posting Job...
                                    </>
                                ) : (
                                    'Post Job'
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Success Modal */}
            {showSuccessModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md w-full mx-4">
                        <div className="text-center mb-6">
                            <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                Your linkedin post is ready!
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400">
                                Your job has been posted. Would you like to share it on LinkedIn to get 3x more views?
                            </p>
                        </div>

                        <div className="space-y-4">
                            <button
                                onClick={handlePostToSocials}
                                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-[#0A66C2] hover:bg-[#084d91] text-white rounded-lg transition-colors relative"
                            >
                                <Linkedin className="w-5 h-5" />
                                1 click linkedin Post
                                <span className="absolute -top-2 -right-2 px-2 py-0.5 bg-gradient-to-r from-pink-500 to-orange-500 text-white text-xs font-semibold rounded-full shadow-sm">
                                    Save Time
                                </span>
                            </button>
                            <button
                                onClick={handleViewListing}
                                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                            >
                                <ExternalLink className="w-5 h-5" />
                                View Job Listing
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AiAssistedJobPost; 