"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';

const UserContext = createContext();

export function UserProvider({ children }) {
    const [userAvailableInLocalStorage, setUserAvailableInLocalStorage] = useState(false);
    const [userDetails, setUserDetails] = useState(null);

    useEffect(() => {
        if (typeof window !== 'undefined') {
            const storedUser = localStorage.getItem('userDetails');
            setUserAvailableInLocalStorage(!!storedUser);

            if (storedUser) {
                try {
                    setUserDetails(JSON.parse(storedUser));
                } catch (error) {
                    console.error("Failed to parse user details:", error);
                }
            }
        }
    }, []);

    const updateUserAvailability = () => {
        const storedUser = localStorage.getItem('userDetails');
        setUserAvailableInLocalStorage(!!storedUser);

        if (storedUser) {
            try {
                setUserDetails(JSON.parse(storedUser));
            } catch (error) {
                console.error("Failed to parse user details:", error);
                setUserDetails(null);
            }
        } else {
            setUserDetails(null);
        }
    };

    return (
        <UserContext.Provider value={{
            userAvailableInLocalStorage,
            userDetails,
            updateUserAvailability
        }}>
            {children}
        </UserContext.Provider>
    );
}

export function useUser() {
    const context = useContext(UserContext);
    if (context === undefined) {
        throw new Error('useUser must be used within a UserProvider');
    }
    return context;
} 