#!/usr/bin/env ts-node

/**
 * Test script for the job recommendations APIs
 * This script tests similar jobs and skill-based recommendations functionality
 */

import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

const BASE_URL = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'http://localhost:3000';

async function testSimilarJobsAPI() {
  console.log('🧪 Testing Job Recommendations APIs...\n');

  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log('');

  try {
    // Test 1: Get some existing jobs first
    console.log('Test 1: Fetching existing jobs to test with...');
    try {
      const jobsResponse = await axios.get(`${BASE_URL}/api/v1/jobs?limit=5`);
      
      if (jobsResponse.status === 200 && jobsResponse.data.data.items.length > 0) {
        console.log(`✅ Found ${jobsResponse.data.data.items.length} jobs to test with`);
        
        const testJob = jobsResponse.data.data.items[0];
        console.log(`📋 Test job: ${testJob.title} (ID: ${testJob._id})`);
        console.log(`🔧 Skills: ${testJob.skills.map((s: any) => s.name).join(', ')}`);
        
        // Test 2: Call similar jobs API with default parameters
        console.log('\nTest 2: Calling similar jobs API with default parameters...');
        try {
          const similarResponse = await axios.get(`${BASE_URL}/api/v1/jobs/${testJob._id}/similar`);
          
          if (similarResponse.status === 200) {
            console.log('✅ Similar jobs API call successful');
            console.log(`📊 Found ${similarResponse.data.data.total} similar jobs`);
            
            if (similarResponse.data.data.items.length > 0) {
              console.log('🎯 Sample similar job:');
              const similarJob = similarResponse.data.data.items[0];
              console.log(`   - Title: ${similarJob.title}`);
              console.log(`   - Match %: ${similarJob.match_info?.match_percentage?.toFixed(1)}%`);
              console.log(`   - Matched Skills: ${similarJob.match_info?.matched_skills?.map((s: any) => s.name).join(', ')}`);
            }
          } else {
            console.log(`❌ Unexpected status: ${similarResponse.status}`);
          }
        } catch (error: any) {
          console.log(`❌ Similar jobs API error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }

        // Test 3: Call with custom parameters
        console.log('\nTest 3: Calling similar jobs API with custom parameters...');
        try {
          const customResponse = await axios.get(`${BASE_URL}/api/v1/jobs/${testJob._id}/similar?mmp=50&lp=true&limit=3`);
          
          if (customResponse.status === 200) {
            console.log('✅ Custom parameters API call successful');
            console.log(`📊 Found ${customResponse.data.data.total} similar jobs with 50% minimum match`);
          } else {
            console.log(`❌ Unexpected status: ${customResponse.status}`);
          }
        } catch (error: any) {
          console.log(`❌ Custom parameters API error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }

        // Test 4: Test with invalid job ID
        console.log('\nTest 4: Testing with invalid job ID...');
        try {
          await axios.get(`${BASE_URL}/api/v1/jobs/invalid-job-id/similar`);
          console.log('❌ Expected failure but got success');
        } catch (error: any) {
          if (error.response?.status === 404 || error.response?.status === 400) {
            console.log('✅ Correctly handled invalid job ID');
          } else {
            console.log(`❌ Unexpected error: ${error.response?.status} - ${error.message}`);
          }
        }

        // Test 5: Test skill-based recommendations API
        console.log('\nTest 5: Testing skill-based recommendations API...');
        const skillsToTest = testJob.skills.slice(0, 2).map((s: any) => s.name).join(',');
        console.log(`🔧 Testing with skills: ${skillsToTest}`);

        try {
          const skillRecommendationsResponse = await axios.get(`${BASE_URL}/api/v1/jobs/recommendations/by-skills?skills=${encodeURIComponent(skillsToTest)}`);

          if (skillRecommendationsResponse.status === 200) {
            console.log('✅ Skill-based recommendations API call successful');
            console.log(`📊 Found ${skillRecommendationsResponse.data.data.total} job recommendations`);

            if (skillRecommendationsResponse.data.data.items.length > 0) {
              console.log('🎯 Sample recommended job:');
              const recommendedJob = skillRecommendationsResponse.data.data.items[0];
              console.log(`   - Title: ${recommendedJob.title}`);
              console.log(`   - Match %: ${recommendedJob.match_info?.match_percentage?.toFixed(1)}%`);
              console.log(`   - Matched Skills: ${recommendedJob.match_info?.matched_skills?.map((s: any) => s.name).join(', ')}`);
            }
          } else {
            console.log(`❌ Unexpected status: ${skillRecommendationsResponse.status}`);
          }
        } catch (error: any) {
          console.log(`❌ Skill-based recommendations API error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }

        // Test 6: Test skill-based recommendations with filters
        console.log('\nTest 6: Testing skill-based recommendations with filters...');
        try {
          const filteredResponse = await axios.get(`${BASE_URL}/api/v1/jobs/recommendations/by-skills?skills=${encodeURIComponent(skillsToTest)}&mmp=10&jt=REMOTE&limit=5`);

          if (filteredResponse.status === 200) {
            console.log('✅ Filtered skill-based recommendations API call successful');
            console.log(`📊 Found ${filteredResponse.data.data.total} remote job recommendations with 10% minimum match`);
          } else {
            console.log(`❌ Unexpected status: ${filteredResponse.status}`);
          }
        } catch (error: any) {
          console.log(`❌ Filtered recommendations API error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }

        // Test 7: Test with empty skills (should return latest 5 jobs)
        console.log('\nTest 7: Testing with empty skills (latest jobs)...');
        try {
          const latestJobsResponse = await axios.get(`${BASE_URL}/api/v1/jobs/recommendations/by-skills`);

          if (latestJobsResponse.status === 200) {
            console.log('✅ Latest jobs API call successful');
            console.log(`📊 Found ${latestJobsResponse.data.data.total} latest jobs`);
            console.log(`📋 Returned ${latestJobsResponse.data.data.items.length} jobs (should be max 5)`);
          } else {
            console.log(`❌ Unexpected status: ${latestJobsResponse.status}`);
          }
        } catch (error: any) {
          console.log(`❌ Latest jobs API error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }

        // Test 8: Test fuzzy skill matching
        console.log('\nTest 8: Testing fuzzy skill matching...');
        const fuzzySkill = testJob.skills[0]?.name;
        if (fuzzySkill && fuzzySkill.length > 3) {
          // Create a partial skill name for fuzzy testing
          const partialSkill = fuzzySkill.substring(0, fuzzySkill.length - 1);
          console.log(`🔧 Testing fuzzy match with partial skill: "${partialSkill}" (from "${fuzzySkill}")`);

          try {
            const fuzzyResponse = await axios.get(`${BASE_URL}/api/v1/jobs/recommendations/by-skills?skills=${encodeURIComponent(partialSkill)}&mmp=10`);

            if (fuzzyResponse.status === 200) {
              console.log('✅ Fuzzy skill matching API call successful');
              console.log(`📊 Found ${fuzzyResponse.data.data.total} jobs with fuzzy skill matching`);

              if (fuzzyResponse.data.data.items.length > 0) {
                const fuzzyJob = fuzzyResponse.data.data.items[0];
                console.log(`🎯 Sample fuzzy matched job: ${fuzzyJob.title}`);
                console.log(`   - Matched Skills: ${fuzzyJob.match_info?.matched_skills?.map((s: any) => s.name).join(', ')}`);
              }
            } else {
              console.log(`❌ Unexpected status: ${fuzzyResponse.status}`);
            }
          } catch (error: any) {
            console.log(`❌ Fuzzy matching API error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
          }
        } else {
          console.log('⏭️ Skipping fuzzy test - no suitable skill found');
        }

        // Test 9: Test candidate-based recommendations API
        console.log('\nTest 9: Testing candidate-based recommendations API...');
        try {
          // First, get some candidates to test with
          const candidatesResponse = await axios.get(`${BASE_URL}/api/v1/candidates?limit=1`);

          if (candidatesResponse.status === 200 && candidatesResponse.data.data.items.length > 0) {
            const testCandidate = candidatesResponse.data.data.items[0];
            console.log(`👤 Test candidate: ${testCandidate.user?.name || 'Unknown'} (ID: ${testCandidate._id})`);
            console.log(`🔧 Candidate skills: ${testCandidate.skills?.map((s: any) => s.name).join(', ') || 'No skills'}`);

            const candidateRecommendationsResponse = await axios.get(`${BASE_URL}/api/v1/jobs/recommendations/for-you/${testCandidate._id}`);

            if (candidateRecommendationsResponse.status === 200) {
              console.log('✅ Candidate recommendations API call successful');
              console.log(`📊 Found ${candidateRecommendationsResponse.data.data.total} job recommendations for candidate`);

              if (candidateRecommendationsResponse.data.data.items.length > 0) {
                console.log('🎯 Sample recommended job for candidate:');
                const candidateRecommendedJob = candidateRecommendationsResponse.data.data.items[0];
                console.log(`   - Title: ${candidateRecommendedJob.title}`);
                if (candidateRecommendedJob.match_info) {
                  console.log(`   - Match %: ${candidateRecommendedJob.match_info?.match_percentage?.toFixed(1)}%`);
                  console.log(`   - Matched Skills: ${candidateRecommendedJob.match_info?.matched_skills?.map((s: any) => s.name).join(', ')}`);
                }
              }
            } else {
              console.log(`❌ Unexpected status: ${candidateRecommendationsResponse.status}`);
            }

            // Test 10: Test candidate recommendations with filters
            console.log('\nTest 10: Testing candidate recommendations with filters...');
            try {
              const filteredCandidateResponse = await axios.get(`${BASE_URL}/api/v1/jobs/recommendations/for-you/${testCandidate._id}?mmp=10&jt=REMOTE&limit=3`);

              if (filteredCandidateResponse.status === 200) {
                console.log('✅ Filtered candidate recommendations API call successful');
                console.log(`📊 Found ${filteredCandidateResponse.data.data.total} remote job recommendations for candidate with 10% minimum match`);
              } else {
                console.log(`❌ Unexpected status: ${filteredCandidateResponse.status}`);
              }
            } catch (error: any) {
              console.log(`❌ Filtered candidate recommendations API error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
            }

          } else {
            console.log('❌ No candidates found in database to test with');
            console.log('💡 Please ensure there are some candidates in the database first');
          }
        } catch (error: any) {
          console.log(`❌ Failed to fetch candidates or test candidate recommendations: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }

        // Test 11: Test with invalid candidate ID
        console.log('\nTest 11: Testing candidate recommendations with invalid candidate ID...');
        try {
          await axios.get(`${BASE_URL}/api/v1/jobs/recommendations/for-you/invalid-candidate-id`);
          console.log('❌ Expected failure but got success');
        } catch (error: any) {
          if (error.response?.status === 404 || error.response?.status === 400) {
            console.log('✅ Correctly handled invalid candidate ID');
          } else {
            console.log(`❌ Unexpected error: ${error.response?.status} - ${error.message}`);
          }
        }

      } else {
        console.log('❌ No jobs found in database to test with');
        console.log('💡 Please ensure there are some jobs in the database first');
      }
    } catch (error: any) {
      console.log(`❌ Failed to fetch jobs: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testSimilarJobsAPI();
}

export { testSimilarJobsAPI };
