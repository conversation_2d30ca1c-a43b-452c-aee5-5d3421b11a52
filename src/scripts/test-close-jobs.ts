#!/usr/bin/env ts-node

/**
 * Test script for the close-jobs API endpoint
 * This script tests the functionality without actually closing jobs
 */

import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

const BASE_URL = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'http://localhost:3000';
const API_KEY = process.env.X_CRON_API_KEY;

async function testCloseJobsAPI() {
  console.log('🧪 Testing Close Jobs API...\n');

  if (!API_KEY) {
    console.error('❌ X_CRON_API_KEY not found in environment variables');
    process.exit(1);
  }

  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...`);
  console.log('');

  try {
    // Test 1: Call API without API key (should fail)
    console.log('Test 1: Calling API without API key...');
    try {
      await axios.post(`${BASE_URL}/api/cron/close-jobs`);
      console.log('❌ Expected failure but got success');
    } catch (error: any) {
      if (error.response?.status === 401) {
        console.log('✅ Correctly rejected request without API key');
      } else {
        console.log(`❌ Unexpected error: ${error.response?.status} - ${error.message}`);
      }
    }

    // Test 2: Call API with wrong API key (should fail)
    console.log('\nTest 2: Calling API with wrong API key...');
    try {
      await axios.post(`${BASE_URL}/api/cron/close-jobs`, {}, {
        headers: {
          'x-c2c-api-key': 'wrong-api-key'
        }
      });
      console.log('❌ Expected failure but got success');
    } catch (error: any) {
      if (error.response?.status === 401) {
        console.log('✅ Correctly rejected request with wrong API key');
      } else {
        console.log(`❌ Unexpected error: ${error.response?.status} - ${error.message}`);
      }
    }

    // Test 3: Call API with correct API key (should work in development)
    console.log('\nTest 3: Calling API with correct API key...');
    try {
      const response = await axios.post(`${BASE_URL}/api/cron/close-jobs`, {
        days: 3
      }, {
        headers: {
          'x-c2c-api-key': API_KEY
        }
      });

      if (response.status === 200) {
        console.log('✅ API call successful');
        console.log('📊 Response:', JSON.stringify(response.data, null, 2));
      } else {
        console.log(`❌ Unexpected status: ${response.status}`);
      }
    } catch (error: any) {
      if (error.response?.status === 401 && error.response?.data?.message?.includes('production')) {
        console.log('✅ Correctly rejected request in non-production environment');
        console.log('📝 Note: This API only works in production environment');
      } else {
        console.log(`❌ Unexpected error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testCloseJobsAPI();
}

export { testCloseJobsAPI };
