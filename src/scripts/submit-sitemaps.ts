import { submitAllSitemapsToGoogle } from '@/server/utils/sitemapUtils';

/**
 * <PERSON><PERSON><PERSON> to submit sitemaps to Google Search Console
 * This can be run as a cron job or manually
 */
async function main() {
  console.log('Submitting sitemaps to Google...');
  
  try {
    const result = await submitAllSitemapsToGoogle();
    
    if (result.success) {
      console.log('Successfully submitted sitemaps to Google!');
      process.exit(0);
    } else {
      console.error('Failed to submit sitemaps to Google:', result.results);
      process.exit(1);
    }
  } catch (error) {
    console.error('Error submitting sitemaps to Google:', error);
    process.exit(1);
  }
}

main();
