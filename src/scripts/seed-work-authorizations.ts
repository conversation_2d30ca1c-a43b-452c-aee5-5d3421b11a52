import connectToDb from "@/server/lib/db";
import { WorkAuthorization } from "@/server/models";

const workAuthorizations = [
    {
        name: "H-1B Specialty Occupation",
        description: "For foreign workers in specialty occupations that require theoretical or technical expertise, such as computer science or engineering. Requires a bachelor's degree or higher and a job offer from a U.S. employer.",
        category: "Temporary"
    },
    {
        name: "L-1A Executive or Manager",
        description: "For executives and managers being transferred to a U.S. office of the same employer (or its affiliate or subsidiary).",
        category: "Temporary"
    },
    {
        name: "L-1B Specialized Knowledge",
        description: "For employees with specialized knowledge being transferred to a U.S. office of the same employer (or its affiliate or subsidiary).",
        category: "Temporary"
    },
    {
        name: "O-1 Extraordinary Ability",
        description: "For individuals with extraordinary ability in sciences, arts, education, business, or athletics.",
        category: "Temporary"
    },
    {
        name: "E-1 Treaty Trader",
        description: "For individuals from countries with trade treaties with the U.S. who conduct substantial trade.",
        category: "Temporary"
    },
    {
        name: "E-2 Treaty Investor",
        description: "For individuals from countries with investment treaties with the U.S. who have invested substantially in a U.S. business.",
        category: "Temporary"
    },
    {
        name: "TN Visa",
        description: "For Canadian and Mexican professionals under NAFTA/USMCA trade agreement.",
        category: "Temporary"
    },
    {
        name: "E-3 Australian Professional",
        description: "For Australian professionals in specialty occupations.",
        category: "Temporary"
    },
    {
        name: "H-2A Agricultural Worker",
        description: "For temporary or seasonal agricultural workers.",
        category: "Temporary"
    },
    {
        name: "EB-1 First Preference",
        description: "Permanent visa for individuals with extraordinary ability, outstanding professors/researchers, or multinational managers/executives.",
        category: "Permanent"
    },
    {
        name: "EB-2 Second Preference",
        description: "Permanent visa for professionals with advanced degrees or exceptional ability.",
        category: "Permanent"
    },
    {
        name: "EB-3 Third Preference",
        description: "Permanent visa for skilled workers, professionals, and other workers.",
        category: "Permanent"
    },
    {
        name: "Green Card",
        description: "Permanent resident card that allows foreign nationals to live and work permanently in the U.S.",
        category: "Permanent"
    },
    {
        name: "Employment Authorization Document (EAD)",
        description: "Work permit that allows non-citizens to legally work in the U.S.",
        category: "Other"
    },
    {
        name: "OPT (Optional Practical Training)",
        description: "Temporary employment authorization for F-1 students related to their field of study.",
        category: "Temporary"
    },
    {
        name: "STEM OPT Extension",
        description: "Extended work authorization for international students in STEM fields.",
        category: "Temporary"
    },
    {
        name: "US Citizen",
        description: "U.S. citizenship status that allows unrestricted work authorization.",
        category: "Permanent"
    },
    {
        name: "C2C (Corp-to-Corp)",
        description: "Business arrangement where a foreign worker's corporation contracts with a U.S. company.",
        category: "Other"
    },
    {
        name: "H-4 EAD",
        description: "Work authorization for certain H-4 visa holders (spouses of H-1B visa holders).",
        category: "Temporary"
    }
];

async function seedWorkAuthorizations() {
    try {
        await connectToDb();
        
        // Check if collection is empty
        const count = await WorkAuthorization.countDocuments();
        
        if (count === 0) {
            console.log("Seeding work authorizations...");
            await WorkAuthorization.insertMany(workAuthorizations);
            console.log(`Successfully seeded ${workAuthorizations.length} work authorizations`);
        } else {
            console.log(`Work authorizations collection already has ${count} documents. Skipping seeding.`);
        }
        
        process.exit(0);
    } catch (error) {
        console.error("Error seeding work authorizations:", error);
        process.exit(1);
    }
}

seedWorkAuthorizations();
