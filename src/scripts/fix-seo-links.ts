// process.env.MONGO_URI = 'mongodb+srv://onlyc2c-admin:<EMAIL>/ONLYC2C_PROD?retryWrites=true&w=majority';
// process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL = 'https://onlyc2c.com';

import connectToDb from "@/server/lib/db";
import { IJob, ILocality, IOrganization, ISkill, Job } from "@/server/models";
import { generateJobMetadatas } from "@/server/utils/generateJobMetadata";


async function main() {
    await connectToDb();

    const populate = [
        {
            path: 'organization',
            select: 'name logo website',
        },
        {
            path: 'posted_by',
            select: 'phone',
            populate: {
                path: 'user',
                select: 'name email image',
            }
        }
    ];

    const jobs = await Job.find().populate(populate);
    for (const job of jobs) {
        console.log('Updating SEO link:', job.role);
        const metadatas = await generateJobMetadatas(
            job.job_id,
            job.role,
            job.job_type,
            job.organization as IOrganization,
            job.skills as ISkill[],
            job.locality as ILocality,
        );

        await Job.findByIdAndUpdate(
            job._id,
            { seo_link: metadatas.seo_link } as IJob,
        );
    }

    console.log('All jobs updated successfully!');
    process.exit(1);
}

main().catch(error => {
    console.error('Error running sitemap generation script:', error);
    process.exit(1);
});
