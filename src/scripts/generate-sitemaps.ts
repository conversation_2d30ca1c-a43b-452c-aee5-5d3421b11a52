import { 
  generateAndSaveAllSitemapsLocally, 
  submitAllSitemapsToGoogle 
} from '@/server/utils/sitemapUtils';

/**
 * <PERSON><PERSON><PERSON> to generate and optionally submit sitemaps
 * 
 * Usage:
 * - To generate sitemaps locally: npm run generate-sitemaps
 * - To generate and submit to Google: npm run generate-sitemaps -- --submit
 */
async function main() {
  console.log('Generating sitemaps...');
  
  // Generate all sitemaps and save them locally
  await generateAndSaveAllSitemapsLocally();
  
  // Check if we should submit to Google
  const shouldSubmit = process.argv.includes('--submit');
  
  if (shouldSubmit) {
    console.log('\nSubmitting sitemaps to Google...');
    const result = await submitAllSitemapsToGoogle();
    
    if (result.success) {
      console.log('Successfully submitted sitemaps to Google!');
    } else {
      console.error('Failed to submit sitemaps to Google:', result.results);
      process.exit(1);
    }
  }
  
  console.log('\nDone!');
}

main().catch(error => {
  console.error('Error running sitemap generation script:', error);
  process.exit(1);
});
