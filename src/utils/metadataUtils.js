/**
 * Generates base metadata for the site
 * @returns {Object} Base metadata object
 */
export function getBaseMetadata() {
    return {
        metadataBase: new URL('https://onlyc2c.com'),
        title: "Onlyc2c - Easily find latest corp-to-corp jobs matching your skill",
        description: "Onlyc2c - Easily find latest corp-to-corp jobs matching your skill. Connect with top companies and find your perfect job today.",
        keywords: "Corp to Corp, C2C, Jobs, Employment, US Jobs, IT Jobs, Tech Jobs, Free Job Search",
        authors: [{ name: "Onlyc2<PERSON>" }],
        creator: "Onlyc2<PERSON>",
        publisher: "Onlyc2c",
        robots: "index, follow",

        // Open Graph
        openGraph: {
            title: "Onlyc2c - Easily find latest corp-to-corp jobs matching your skill",
            description: "Onlyc2c - The best platform to find Corp to Corp jobs in the US for free. Connect with top companies and find your perfect job today.",
            url: "https://onlyc2c.com",
            siteName: "Onlyc2c",
            images: [
                {
                    url: "https://onlyc2c.com/assets/images/logo.png",
                    width: 800,
                    height: 600,
                },
            ],
            locale: "en_US",
            type: "website",
        },

        // Twitter
        twitter: {
            card: "summary_large_image",
            title: "Onlyc2c - Easily find latest corp-to-corp jobs matching your skill",
            description: "Onlyc2c - Easily find latest corp-to-corp jobs matching your skill. Connect with top companies and find your perfect job today.",
            images: ["https://onlyc2c.com/assets/images/logo.png"],
            site: "@Onlyc2c",
        },

        // Additional metadata
        manifest: "/manifest.json",
        icons: {
            icon: [
                { url: '/assets/icons/favicon.png', type: 'image/png' },
            ],
            apple: [
                { url: '/assets/icons/favicon.png', type: 'image/png' },
            ],
            other: [
                {
                    rel: 'apple-touch-icon',
                    url: '/assets/icons/favicon.png',
                },
            ],
        },
        appleWebApp: {
            capable: false,
            statusBarStyle: "black-translucent",
        },
        alternates: {
            canonical: "https://onlyc2c.com",
        },
    };
}

/**
 * Generates custom metadata by extending the base metadata
 * @param {Object} customMetadata - Custom metadata to override base values
 * @returns {Object} Combined metadata object
 */
export function generateMetadata(customMetadata = {}) {
    const baseMetadata = getBaseMetadata();

    // Deep merge for nested objects like openGraph and twitter
    const mergedMetadata = { ...baseMetadata };

    // Handle top-level properties
    Object.keys(customMetadata).forEach(key => {
        if (typeof customMetadata[key] === 'object' && customMetadata[key] !== null && !Array.isArray(customMetadata[key])) {
            // For objects, merge with existing objects
            mergedMetadata[key] = {
                ...(mergedMetadata[key] || {}),
                ...customMetadata[key]
            };
        } else {
            // For primitives and arrays, replace completely
            mergedMetadata[key] = customMetadata[key];
        }
    });

    return mergedMetadata;
} 