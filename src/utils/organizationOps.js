import axios from 'axios';

const createOrganization = async ({ company }) => {
    const response = await axios.post(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/organizations`, {
        name: company,
    });
    return response.data;
}

const updateOrganizationLogo = async ({ organizationId, logo }) => {
    try {
        const formData = new FormData();
        formData.append('file', logo);

        const response = await axios.put(
            `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/organizations/${organizationId}/avatar`,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            }
        );

        return {
            success: true,
            data: response.data,
            error: null
        };
    } catch (error) {
        console.error('Error updating organization logo:', error);
        return {
            success: false,
            data: null,
            error: error.response?.data?.message || 'Failed to update logo'
        };
    }
};

const deleteOrganizationLogo = async (organizationId) => {
    try {
        const response = await axios.delete(
            `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/organizations/${organizationId}/avatar`
        );

        return {
            success: true,
            data: response.data,
            error: null
        };
    } catch (error) {
        console.error('Error deleting organization logo:', error);
        return {
            success: false,
            data: null,
            error: error.response?.data?.message || 'Failed to delete logo'
        };
    }
};

const getOrganization = async ({ organizationId }) => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/organizations/${organizationId}`);
    return response.data;
}

const getAllOrganizations = async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/organizations`);
    return response.data;
}

const getOrganizationByQuery = async (query) => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/organizations?q=${query}`);
    return response.data;
}

export { createOrganization, updateOrganizationLogo, getOrganization, deleteOrganizationLogo, getOrganizationByQuery, getAllOrganizations };