import axios from 'axios';

const createJob = async ({ description, descriptionHtml, jobRole, skills, phoneNumber, location, jobType,
    hourlyRate, userDetails }) => {

    try {
        const jobData = {
            description: description,
            description_html: descriptionHtml,
            role: jobRole,
            skills: skills,
            phone: phoneNumber,
            locality: location,
            job_type: jobType,
            hourly_rate: hourlyRate,
            email: userDetails?.user?.email,
            organization: userDetails?.organization?._id,
        }
        const job = await axios.post(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/jobs`, jobData);
        return job;
    } catch (error) {
        console.error('Error creating job:', error);
        throw error;
    }
}

const getJobs = async (queryParams = {}) => {
    try {
        // Create URLSearchParams object from the query parameters
        const params = new URLSearchParams();

        // Add each parameter to the query string if it exists
        Object.entries(queryParams).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                params.append(key, value);
            }
        });

        // Build the URL with query parameters
        const queryString = params.toString();
        const url = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/jobs${queryString ? `?${queryString}` : ''}`;

        const response = await axios.get(url);
        return response.data;
    } catch (error) {
        console.error('Error getting jobs:', error);
        throw error;
    }
};

const getJobById = async (jobId) => {
    try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/jobs/${jobId}`);
        return response.data;
    } catch (error) {
        console.error('Error getting job by id:', error);
        throw error;
    }
};

const updateJobStatus = async (jobId, status) => {
    try {
        const response = await axios.patch(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/jobs/${jobId}`, { active: status });
        return response.data;
    } catch (error) {
        console.error('Error updating job status:', error);
        throw error;
    }
};

const searchJobs = async (query) => {
    try {
        const response = await axios.post(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/jobs/search`, { query });
        return response.data;
    } catch (error) {
        console.error('Error searching jobs:', error);
        throw error;
    }
}

const closeJobAndLetCandidatesInform = async (jobId) => {
    try {
        const response = await axios.patch(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/jobs/${jobId}/close`);
        return response.data;
    } catch (error) {
        console.error('Error closing job permanently:', error);
        throw error;
    }
}


export { createJob, getJobs, getJobById, updateJobStatus, searchJobs, closeJobAndLetCandidatesInform };