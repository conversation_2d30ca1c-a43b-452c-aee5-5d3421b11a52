import axios from "axios";

const jobDescriptionTemplates = [
  (data) => `We're seeking a ${data.jobRole} developer for an exciting ${data.jobType.toLowerCase()} opportunity in ${data.location}. This C2C position offers competitive compensation at ${data.hourlyRate}. The ideal candidate should have strong expertise in ${data.skills.join(', ')}. Join our dynamic team and work on cutting-edge projects that make a difference. We value innovation and offer a collaborative work environment where your skills in ${data.skills[0]} and ${data.skills[1]} will be put to great use. Our team is passionate about delivering high-quality solutions and staying ahead of industry trends. We provide opportunities for professional development and skill enhancement. The successful candidate will contribute to architectural decisions and help shape our technical roadmap. Regular team interactions and knowledge sharing sessions are part of our culture. We believe in maintaining a healthy work-life balance while pursuing technical excellence.`,

  (data) => `A thriving tech company in ${data.location} is looking for a ${data.jobRole} developer. This ${data.jobType.toLowerCase()} role comes with a competitive rate of ${data.hourlyRate} for C2C contracts. We're particularly interested in candidates proficient in ${data.skills.join(' and ')}. The position offers excellent growth opportunities and the chance to work with a talented team of professionals. You'll be involved in designing and implementing innovative solutions for our clients. We foster a culture of continuous learning and technical excellence. Our team members enjoy the flexibility to explore new technologies and approaches. Regular code reviews and technical discussions are part of our development process. You'll have the opportunity to mentor junior developers and contribute to our technical blog. We offer a supportive environment where your ideas and contributions are valued.`,

  (data) => `Exciting C2C opportunity for a ${data.jobRole} developer with expertise in ${data.skills.join(', ')}! This ${data.jobType.toLowerCase()} position is based in ${data.location} with an attractive rate of ${data.hourlyRate}. Join a forward-thinking team where your technical skills will be valued and challenged daily. We're working on innovative projects that require creative problem-solving abilities. Our development process emphasizes code quality and best practices. Team members are encouraged to contribute ideas and influence technical decisions. We provide regular opportunities for skill development and knowledge sharing. You'll be working in an agile environment with modern development tools and practices. The role includes mentoring opportunities and participation in architecture discussions. We maintain a collaborative atmosphere where everyone's input is appreciated.`,

  (data) => `We have an immediate opening for a ${data.jobRole} developer in our ${data.location} office. The role offers ${data.hourlyRate} for C2C contracts and requires expertise in ${data.skills.join(', ')}. This ${data.jobType.toLowerCase()} position provides an excellent opportunity to work on innovative projects while growing your career. We're committed to maintaining a positive and collaborative work environment. Our team values open communication and knowledge sharing. You'll be involved in all phases of the development lifecycle, from planning to deployment. We encourage experimentation with new technologies and approaches. Regular team building activities help maintain a strong and cohesive team culture. The ideal candidate will contribute to both technical and process improvements. We believe in celebrating successes and learning from challenges together.`,

  (data) => `Looking for a talented ${data.jobRole} developer with strong skills in ${data.skills.join(' and ')}. Based in ${data.location}, this ${data.jobType.toLowerCase()} C2C opportunity offers ${data.hourlyRate}. Join our team and be part of exciting projects that push technological boundaries. We pride ourselves on delivering high-quality solutions to complex problems. Our development process emphasizes collaboration and innovation. Team members are encouraged to explore new technologies and share their findings. We maintain a balance between individual contribution and team success. Regular technical discussions help keep our skills sharp and current. You'll have opportunities to influence our technical direction and practices. The role includes mentoring junior developers and contributing to our knowledge base. We believe in creating an environment where everyone can grow and succeed.`,

  (data) => `A fast-growing company in ${data.location} seeks a ${data.jobRole} developer for a ${data.jobType.toLowerCase()} position. This C2C role offers ${data.hourlyRate} and requires proficiency in ${data.skills.join(', ')}. Be part of an innovative team that values creativity and technical excellence.`,

  (data) => `Join our team as a ${data.jobRole} developer in ${data.location}! We're offering ${data.hourlyRate} for this ${data.jobType.toLowerCase()} C2C position. Your expertise in ${data.skills.join(' and ')} will be essential in driving our technical initiatives forward. Experience an engaging work environment with opportunities for professional growth.`,

  (data) => `Exceptional opportunity for a ${data.jobRole} developer with ${data.skills.join(', ')} expertise! This ${data.jobType.toLowerCase()} C2C role in ${data.location} offers competitive compensation at ${data.hourlyRate}. Join a dynamic team where your technical skills will make a significant impact.`,

  (data) => `We're expanding our team in ${data.location} and seeking a ${data.jobRole} developer. This ${data.jobType.toLowerCase()} C2C position offers ${data.hourlyRate} and requires strong proficiency in ${data.skills.join(' and ')}. Be part of an innovative environment where your expertise will be valued and rewarded.`,

  (data) => `Exciting C2C opportunity in ${data.location} for a ${data.jobRole} developer! This ${data.jobType.toLowerCase()} role offers ${data.hourlyRate} and focuses on ${data.skills.join(', ')}. Join our team and work on challenging projects while growing your technical expertise.`
];


const generateJobDescription = (jobData) => {
  const shuffledTemplates = [...jobDescriptionTemplates]
    .sort(() => Math.random() - 0.5);

  const template = shuffledTemplates[0];
  return template(jobData);
};

const extractJobDetailsFromJobDescription = async (jobDescription) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/ai/job-desc`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ hint: jobDescription }),
    });

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let finalData = null;

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // Keep the last incomplete line in the buffer

      for (const line of lines) {
        if (!line.trim()) continue;

        if (line.startsWith('data: ')) {
          const data = line.slice(6); // Remove 'data: ' prefix
          try {
            const parsedData = JSON.parse(data);

            // Check if this is the end event
            if (parsedData.complete) {
              finalData = {
                role: parsedData.metadata.role,
                type: parsedData.metadata.jobType,
                location: {
                  id: parsedData.metadata.location._id,
                  name: `${parsedData.metadata.location.city}, ${parsedData.metadata.location.state}`
                },
                skills: parsedData.metadata.skills ? parsedData.metadata.skills.map(skill => ({ id: skill._id, name: skill.name })) : [],
                description: parsedData.description,
                salary: parsedData.metadata.salary
              };
            }
          } catch (err) {
            console.log("err", err);
            console.log('Non-JSON data:', data);
          }
        }
      }
    }

    return finalData;
  } catch (error) {
    console.error('Error processing job description:', error);
    throw error;
  }
};


const generateLinkedinPost = (job) => {
  if (!job) return '';

  // Get first sentence of description
  const firstSentence = job?.description?.split(/[.!?]+/)[0] || '';

  return `🔥 Urgent Requirement 👉🏻 ${job?.role} on C2C, 

🏢 Position : ${job?.role || ''}
🌍 Location : ${job?.locality?.city || ''}, ${job?.locality?.state || ''}
🕒 Duration : ${job?.type || 'Full-time'}

👉🏻 Interested?

🔗 Apply directly: https://onlyc2c.com/c2c-jobid-${job?.job_id} 
--------- or ---------
💌 Send your resume to ${job?.email}

💬 Job Description:
${firstSentence}... Read more: https://onlyc2c.com/c2c-jobid-${job?.job_id}

${job.skills ? job.skills.map(skill => `#${(typeof skill === 'string' ? skill : skill.name).replace(/\s+/g, '')}`).join(' ') : ''}`;
};

export { generateJobDescription, extractJobDetailsFromJobDescription, generateLinkedinPost };