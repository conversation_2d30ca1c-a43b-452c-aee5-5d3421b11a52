export const getDeterministicNumber = (str, max = 40, min = 10) => {
    // Handle undefined or null str
    if (!str) {
        return min; // Return minimum value if str is undefined or null
    }

    // Convert to string if it's not already a string
    const strValue = String(str);

    let hash = 5381;
    for (let i = 0; i < strValue.length; i++) {
        hash = (hash * 33) ^ strValue.charCodeAt(i);
    }
    return (hash >>> 0) % (max - min + 1) + min;
}; 