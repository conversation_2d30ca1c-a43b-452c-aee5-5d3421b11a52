import axios from 'axios';

const updateRecruiter = async ({userid, phoneNumber, organization}) => {

    const body = {};
    if (phoneNumber) body.phone = phoneNumber;
    if (organization) body.organization = organization;

    const response = await axios.patch(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/recruiters/${userid}`, body);

    return response.data;   
}

export { updateRecruiter };