import { formatDistanceToNow, format } from 'date-fns';
import { zonedTimeToUtc } from 'date-fns-tz';

export function getTimeFromNowInCST(dateString) {
    try {
        // Create a date object from the input string
        const date = new Date(dateString);

        // Convert the date to CST
        const cstDate = new Date(date.toLocaleString('en-US', { timeZone: 'America/Chicago' }));

        // Calculate the time difference
        const timeAgo = formatDistanceToNow(cstDate, { addSuffix: true });

        return timeAgo;
    } catch (error) {
        console.error('Error calculating time difference:', error);
        return 'recently';
    }
}

export function formatDateToCST(dateString) {
    try {
        // Create a date object from the input string
        const date = new Date(dateString);

        // Format the date in CST
        const cstDateString = date.toLocaleString('en-US', {
            timeZone: 'America/Chicago',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
            hour12: true,
            timeZoneName: 'short'
        });

        return cstDateString;
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid date';
    }
} 