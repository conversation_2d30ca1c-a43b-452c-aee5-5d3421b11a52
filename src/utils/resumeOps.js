import axios from 'axios';

const getMyPreviouslyUploadedResumes = async () => {
    try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/resumes/my`);
        return response.data.data;
    } catch (error) {
        console.error('Error fetching previously uploaded resumes:', error);
        return [];
    }
};

const uploadResume = async (file, label) => {
    try {
        const formData = new FormData();
        formData.append('label', label);
        formData.append('file', file);

        const response = await axios.post(
            `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/resumes`,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            }
        );
        return response.data.data;
    } catch (error) {
        console.error('Resume upload failed:', error);
        throw error;
    }
};

export { getMyPreviouslyUploadedResumes, uploadResume };