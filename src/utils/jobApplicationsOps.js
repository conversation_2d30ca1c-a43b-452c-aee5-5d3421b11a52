import axios from 'axios';

const applyToJob = async (jobId, resumeId) => {
  const response = await axios.post(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/job-applications`,
    {
      job: jobId,
      resume: resumeId
    });
  return response.data;
};

/**
 * Get job applications with optional filters
 * @param {Object} params - Query parameters
 * @param {string} [params.organization] - Organization ID
 * @param {string} [params.applicant] - Applicant ID
 * @param {string} [params.job] - Job ID
 * @param {string} [params.recruiter] - Recruiter ID
 * @param {string} [params.status] - Application status
 * @param {number} [params.page] - Page number for pagination
 * @param {number} [params.limit] - Number of items per page
 * @param {string} [params.sort] - Sort field (e.g., 'created_at')
 * @param {string} [params.order] - Sort order ('asc' or 'desc')
 * @returns {Promise} Promise object represents the job applications
 */
const getJobApplications = async (params = {}) => {
  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/job-applications`,
      { params }
    );

    return {
      success: true,
      data: response.data,
      error: null
    };
  } catch (error) {
    console.error('Error fetching job applications:', error);
    return {
      success: false,
      data: null,
      error: error.response?.data?.message || 'Failed to fetch job applications'
    };
  }
};

/**
 * Get applications for a specific job
 * @param {string} jobId - Job ID
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise} Promise object represents the job applications
 */
const getApplicationsByJobId = async (jobId, additionalParams = {}) => {
  return getJobApplications({
    job: jobId,
    ...additionalParams
  });
};

/**
 * Get applications for a specific recruiter
 * @param {string} recruiterId - Recruiter ID
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise} Promise object represents the job applications
 */
const getApplicationsByRecruiterId = async (recruiterId, additionalParams = {}) => {
  return getJobApplications({
    recruiter: recruiterId,
    ...additionalParams
  });
};

/**
 * Get applications for a specific organization
 * @param {string} organizationId - Organization ID
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise} Promise object represents the job applications
 */
const getApplicationsByOrganizationId = async (organizationId, additionalParams = {}) => {
  return getJobApplications({
    organization: organizationId,
    ...additionalParams
  });
};

/**
 * Get applications for a specific applicant
 * @param {string} applicantId - Applicant ID
 * @param {Object} additionalParams - Additional query parameters
 * @returns {Promise} Promise object represents the job applications
 */
const getApplicationsByApplicantId = async (applicantId, additionalParams = {}) => {
  return getJobApplications({
    applicant: applicantId,
    ...additionalParams
  });
};

const getApplicationsByApplicantIdAndJobId = async (applicantId, jobId, additionalParams = {}) => {
  // Ensure additionalParams is an object
  if (typeof additionalParams !== 'object' || additionalParams === null) {
    additionalParams = {};
  }
  return getJobApplications({
    applicant: applicantId,
    job: jobId,
    ...additionalParams
  });
};

const updateApplicationStatus = async (applicationId, status) => {
  const response = await axios.patch(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/job-applications/${applicationId}/status`, {
    status
  });
  return response.data;
};

export {
  applyToJob,
  getJobApplications,
  getApplicationsByJobId,
  getApplicationsByRecruiterId,
  getApplicationsByOrganizationId,
  getApplicationsByApplicantId,
  updateApplicationStatus,
  getApplicationsByApplicantIdAndJobId
};
