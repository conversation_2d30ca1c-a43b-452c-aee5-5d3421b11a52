export const setupUserDetailsListener = (callback) => {
    // Listen for storage events
    const handleStorageChange = (e) => {
        if (e.key === 'userDetails') {
            try {
                const newUserDetails = e.newValue ? JSON.parse(e.newValue) : null;
                callback(newUserDetails);
            } catch (error) {
                console.error('Error parsing userDetails:', error);
            }
        }
    };

    // Add event listener
    window.addEventListener('storage', handleStorageChange);

    // Create a custom event for same-tab updates
    const customEvent = new Event('userDetailsUpdate');
    const originalSetItem = localStorage.setItem;

    // Override localStorage.setItem
    localStorage.setItem = function (key, value) {
        if (key === 'userDetails') {
            document.dispatchEvent(customEvent);
        }
        originalSetItem.apply(this, arguments);
    };

    // Listen for same-tab updates
    document.addEventListener('userDetailsUpdate', () => {
        try {
            const newUserDetails = JSON.parse(localStorage.getItem('userDetails'));
            callback(newUserDetails);
        } catch (error) {
            console.error('Error parsing userDetails:', error);
        }
    });

    // Return cleanup function
    return () => {
        window.removeEventListener('storage', handleStorageChange);
        document.removeEventListener('userDetailsUpdate', handleStorageChange);
        localStorage.setItem = originalSetItem;
    };
}; 