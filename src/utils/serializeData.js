export const serializeJob = (job) => {
    return {
        _id: job._id.toString(),
        role: job.role || '',
        company_name: job.company_name || '',
        city: job.city || '',
        state: job.state || '',
        created_at: job.created_at?.toString() || '',
        job_type: job.job_type || '',
        job_id: job.job_id || '',
        skills: job.skills || [],
        no_of_applicants: job.no_of_applicants || 0,
        seo_link: job.seo_link || [],
        description: job.description || '',
        description_html: job.description_html || '',
        email: job.email || '',
        phone_number: job.phone_number || '',
        hourly_rate: job.hourly_rate || 0,
    };
}; 