const DB_NAME = 'FileStorage';
const STORE_NAME = 'files';
const DB_VERSION = 1;

// Check if IndexedDB is supported
export const isIndexedDBSupported = () => {
    return Boolean(window.indexedDB);
};

// Initialize the database
export const initDB = () => {
    return new Promise((resolve, reject) => {
        if (!isIndexedDBSupported()) {
            reject(new Error('IndexedDB is not supported in this browser'));
            return;
        }

        const request = indexedDB.open(DB_NAME, DB_VERSION);

        request.onerror = (event) => {
            console.error('IndexedDB error:', event.target.error);
            reject(request.error);
        };

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains(STORE_NAME)) {
                db.createObjectStore(STORE_NAME);
            }
        };
    });
};

// Store a file
export const storeFile = async (key, file) => {
    const db = await initDB();
    
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAME], 'readwrite');
        const store = transaction.objectStore(STORE_NAME);
        
        const fileData = {
            file,
            name: file.name,
            type: file.type,
            size: file.size,
            lastModified: file.lastModified,
            timestamp: new Date().getTime()
        };

        const request = store.put(fileData, key);

        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(fileData);
    });
};

// Retrieve a file
export const getFile = async (key) => {
    const db = await initDB();
    
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAME], 'readonly');
        const store = transaction.objectStore(STORE_NAME);
        const request = store.get(key);

        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
    });
};

// Delete a file
export const deleteFile = async (key) => {
    const db = await initDB();
    
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAME], 'readwrite');
        const store = transaction.objectStore(STORE_NAME);
        const request = store.delete(key);

        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
    });
};

// Clear all files
export const clearFiles = async () => {
    const db = await initDB();
    
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAME], 'readwrite');
        const store = transaction.objectStore(STORE_NAME);
        const request = store.clear();

        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
    });
};