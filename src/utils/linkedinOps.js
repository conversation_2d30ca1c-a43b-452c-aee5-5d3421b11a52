import axios from 'axios';

const checkLinkedInConnectionFromBackend = async () => {
    try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/linkedin/status`);
        return response.data;
    } catch (error) {
        console.error('Error checking LinkedIn connection:', error);
        throw error;
    }
}

const connectLinkedIn = async (returnUrl = null) => {
    try {
        const url = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/linkedin/auth`;
        const params = returnUrl ? `?returnUrl=${encodeURIComponent(returnUrl)}` : '';
        const response = await axios.get(`${url}${params}`);
        return response.data;
    } catch (error) {
        console.error('Error connecting LinkedIn:', error);
        throw error;
    }
}

const postToLinkedIn = async (jobId, content) => {
    try {
        const formData = new FormData();
        formData.append('jobId', jobId);
        formData.append('content', content);

        const response = await axios.post(
            `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/jobs/${jobId}/post-linkedin`,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            }
        );
        console.log(response.data);
        return response.data;
    } catch (error) {
        console.error('Error posting to LinkedIn:', error);
        throw error;
    }
}


export { checkLinkedInConnectionFromBackend, connectLinkedIn, postToLinkedIn };