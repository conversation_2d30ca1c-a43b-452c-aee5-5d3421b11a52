'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Shield, Users, BarChart3, Settings } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const adminMenuItems = [
    {
        name: 'Overview',
        href: '/admin',
        icon: BarChart3,
        description: 'Dashboard overview'
    },
    {
        name: 'Candidate Subscriptions',
        href: '/admin/subscriptions/candidates',
        icon: Users,
        description: 'Manage candidate subscriptions'
    },
    {
        name: 'Analytics',
        href: '/admin/subscriptions/overview',
        icon: BarChart3,
        description: 'Subscription analytics'
    }
];

export default function AdminLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const [isAuthorized, setIsAuthorized] = useState(false);
    const [loading, setLoading] = useState(true);
    const router = useRouter();
    const pathname = usePathname();

    useEffect(() => {
        checkAdminAccess();
    }, []);

    const checkAdminAccess = async () => {
        try {
            // Simple admin check - in production, this should be more secure
            const adminKey = prompt('Enter admin access key:');
            
            if (adminKey === process.env.NEXT_PUBLIC_ADMIN_ACCESS_KEY || adminKey === 'admin123') {
                setIsAuthorized(true);
            } else {
                alert('Unauthorized access');
                router.push('/');
                return;
            }
        } catch (error) {
            console.error('Admin access check failed:', error);
            router.push('/');
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
                <div className="text-center">
                    <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 dark:text-gray-400">Checking admin access...</p>
                </div>
            </div>
        );
    }

    if (!isAuthorized) {
        return null;
    }

    const isActive = (path: string) => {
        if (path === '/admin') {
            return pathname === '/admin';
        }
        return pathname.startsWith(path);
    };

    return (
        <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
            <div className="flex">
                {/* Sidebar */}
                <div className="w-64 bg-white dark:bg-gray-800 shadow-lg">
                    <div className="p-6">
                        <div className="flex items-center mb-8">
                            <Shield className="h-8 w-8 text-red-600 mr-3" />
                            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                                Admin Panel
                            </h1>
                        </div>

                        <nav className="space-y-2">
                            {adminMenuItems.map((item) => {
                                const Icon = item.icon;
                                return (
                                    <Link
                                        key={item.name}
                                        href={item.href}
                                        className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
                                            isActive(item.href)
                                                ? 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-300'
                                                : 'text-gray-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'
                                        }`}
                                    >
                                        <Icon className="h-5 w-5 mr-3" />
                                        <div>
                                            <div className="font-medium">{item.name}</div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                                {item.description}
                                            </div>
                                        </div>
                                    </Link>
                                );
                            })}
                        </nav>
                    </div>
                </div>

                {/* Main Content */}
                <div className="flex-1 p-8">
                    <div className="max-w-7xl mx-auto">
                        {children}
                    </div>
                </div>
            </div>
        </div>
    );
}
