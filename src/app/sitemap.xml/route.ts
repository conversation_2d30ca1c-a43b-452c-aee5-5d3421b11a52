import { NextRequest, NextResponse } from 'next/server';
import { generateSitemapIndex } from '@/server/utils/sitemapUtils';

/**
 * Serves the sitemap.xml file at the root level
 * This is the file that search engines will look for by default
 */
export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'https://onlyc2c.com';
  
  // Define all the sitemaps we have
  const sitemaps = [
    `${baseUrl}/api/sitemap/static.xml`,
    `${baseUrl}/api/sitemap/jobs.xml`,
    `${baseUrl}/api/sitemap/locations.xml`,
    `${baseUrl}/api/sitemap/skills.xml`,
    `${baseUrl}/api/sitemap/job-types.xml`,
  ];

  // Generate the sitemap index XML
  const sitemapIndexXml = await generateSitemapIndex(sitemaps);
  
  // Return the XML with proper content type
  return new NextResponse(sitemapIndexXml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
