import { useState, useEffect } from "react";
import Image from "next/image";

const testimonials = [
    {
        name: "<PERSON><PERSON>",
        title: "CEO, Aether Talent Solutions",
        companyUrl: "https://aethertalentsolutions.com/",
        image: "/assets/people/aman.jpeg",
        linkedin: "https://www.linkedin.com/in/aman-ahmad-12a642215",
        quote: `Recently It's hard to get verified candidates, With the skills and expericence they claim to have, Good thing about the platform is that they verify the candidates skills presence in US, Makes it easier for us to submit profiles for review.`
    },
    {
        name: "<PERSON><PERSON>",
        title: "Hiring Manager, Dizer Corp",
        companyUrl: "https://dizercorp.com/",
        image: "/assets/people/umesh.jpeg",
        linkedin: "https://www.linkedin.com/in/umesh-kumar-0bb8741a7", 
        quote: `The best platform for finding C2C talent. The response rate is incredible, and the hiring process is seamless.`
    }, 
    {
        name: "Ramsaran CK",
        title: "Sr Recuiter, Tekwings LLC",
        companyUrl: "https://tekwings.com/",
        image: "/assets/people/ramsaran.png",
        linkedin: "https://www.linkedin.com/in/ramsaran-ck-8886b0214/", 
        quote: `It was more easy!, Cause I had to submit profiles as soon as possible, We found few in an hour and it was a great experience.`
    }
];

const FADE_DURATION = 500; // ms
const DISPLAY_DURATION = 5000; // ms

const MarketingMaterials = () => {
    const [current, setCurrent] = useState(0);
    const [fade, setFade] = useState(true);

    useEffect(() => {
        const fadeOutTimeout = setTimeout(() => setFade(false), DISPLAY_DURATION - FADE_DURATION);
        const switchTimeout = setTimeout(() => {
            setCurrent((prev) => (prev + 1) % testimonials.length);
            setFade(true);
        }, DISPLAY_DURATION);

        return () => {
            clearTimeout(fadeOutTimeout);
            clearTimeout(switchTimeout);
        };
    }, [current]);

    const t = testimonials[current];

    return (
        <div className="hidden lg:block space-y-8">
            <div className="space-y-6">
                <h2 className="text-xl font-semibold text-white dark:text-white">What Recruiters Say</h2>
                <div
                    className={`rounded-lg p-6 transition-opacity duration-500 ${fade ? "opacity-100" : "opacity-0"}`}
                    style={{ minHeight: 220 }} // Prevents layout shift
                >
                    {/* Row: Avatar + Message bubble */}
                    <div className="flex items-start gap-3 mb-4">
                        {/* Avatar */}
                        <Image
                            src={t.image}
                            alt={t.name}
                            width={40}
                            height={40}
                            className="rounded-full object-cover"
                        />
                        {/* Message bubble */}
                        <div className="relative">
                            <div className="bg-white dark:bg-gray-700 border border-blue-100 dark:border-gray-600 rounded-2xl px-5 py-4 shadow-lg text-gray-700 dark:text-gray-200 italic relative">
                                "{t.quote}"
                                {/* Bubble tail */}
                                <div className="absolute -left-3 top-5 w-0 h-0 
                                    border-y-8 border-y-transparent 
                                    border-r-8 border-r-white 
                                    dark:border-r-gray-700 
                                    border-l-0">
                                </div>
                            </div>
                        </div>
                    </div>
                    {/* Name, LinkedIn, Title/Company */}
                    <div className="flex flex-col items-start gap-0 ml-12">
                        <div className="flex items-center gap-1">
                            <p className="font-medium text-white">{t.name}</p>
                            <a
                                href={t.linkedin}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-block align-middle"
                                aria-label={`LinkedIn profile of ${t.name}`}
                            >
                                <Image
                                    src={"/assets/images/linkedin_logo.png"}
                                    alt="LinkedIn"
                                    width={18}
                                    height={18}
                                    className="ml-1 inline-block"
                                    style={{ verticalAlign: "middle" }}
                                />
                            </a>
                        </div>
                        <span className="text-sm text-white/80">
                            {t.title.split(',')[0]},{" "}
                            <a
                                href={t.companyUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="underline hover:text-blue-300"
                            >
                                {t.title.split(',')[1].trim()}
                            </a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MarketingMaterials;