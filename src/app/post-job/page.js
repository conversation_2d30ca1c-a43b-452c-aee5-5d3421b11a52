'use client';

import PostJobForm from '../../components/PostJobForm';
import MarketingMaterials from './MarketingMaterials';

export default function PostJobPage() {
    return (
        <div className="relative min-h-screen overflow-hidden">
            {/* Video Background */}
            <video
                autoPlay
                loop
                muted
                playsInline
                className="absolute inset-0 w-full h-full object-cover z-0"
            >
                <source src="/assets/videos/background.mp4" type="video/mp4" />
                {/* Fallback text */}
                Your browser does not support the video tag.
            </video>
            {/* Overlay for readability */}
            <div className="absolute inset-0 bg-black bg-opacity-60 z-10" />

            {/* Main Content */}
            <div className="relative z-20 min-h-screen flex items-center">
                <div className="w-full max-w-7xl mx-auto p-6 pt-24">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        <div className="w-full">
                            <div className="rounded-3xl shadow-2xl p-8">
                                <PostJobForm />
                            </div>
                        </div>
                        <div className="w-full  flex justify-center backdrop-blur-md">
                            <div className="w-full max-w-4xl rounded-3xl p-8 backdrop-blur-md">
                                <MarketingMaterials />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
