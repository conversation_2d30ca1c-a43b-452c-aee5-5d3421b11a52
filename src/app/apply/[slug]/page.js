import { notFound } from 'next/navigation';
import { oldJobService } from '@/server/services';

async function getJobDetails(jobId) {
    try {
        const job = await oldJobService.getOldJobById(jobId);
        if (!job) {
            throw new Error('Job not found');
        }
        return job;
    } catch (error) {
        console.error('Error fetching job:', error);
        return null;
    }
}

export async function generateMetadata({ params }) {
    try {
        const { slug } = await params;
        const jobId = slug.split('-').pop();
        const job = await getJobDetails(jobId);

        if (!job) {
            return {
                title: 'Job Not Found - OnlyC2C',
                description: 'The requested job posting could not be found.'
            };
        }

        return {
            title: `${job.title} - OnlyC2C`,
            description: job.meta_description,
            keywords: job.meta_keywords,
            alternates: {
                canonical: job.seo_link[0]
            }
        };
    } catch (error) {
        console.error('Metadata generation error:', error);
        return {
            title: 'OnlyC2C Jobs',
            description: 'Find your next contract opportunity'
        };
    }
}

export default async function JobPage({ params }) {
    try {
        const { slug } = await params;
        const jobId = slug.split('-').pop();
        console.log('Job ID:', jobId);

        const job = await getJobDetails(jobId);

        if (!job) {
            notFound();
        }

        return (
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-4xl mx-auto">
                    {/* Archive Notice Banner */}
                    <div className="bg-yellow-50 dark:bg-yellow-900 border-l-4 border-yellow-400 p-4 mb-8">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <p className="text-sm text-yellow-700 dark:text-yellow-200">
                                    This is an archived job posting from OnlyC2C V1. New applications are not being accepted.
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Job Details */}
                    <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
                        <div className="p-8">
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                                {job.title}
                            </h1>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                                <div>
                                    <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                                        Job Details
                                    </h2>
                                    <dl className="space-y-2">
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                                Location
                                            </dt>
                                            <dd className="text-sm text-gray-900 dark:text-gray-100">
                                                {job.city}, {job.state}
                                            </dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                                Job Type
                                            </dt>
                                            <dd className="text-sm text-gray-900 dark:text-gray-100">
                                                {job.job_type}
                                            </dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                                Role
                                            </dt>
                                            <dd className="text-sm text-gray-900 dark:text-gray-100">
                                                {job.role}
                                            </dd>
                                        </div>
                                    </dl>
                                </div>

                                <div>
                                    <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                                        Contact
                                    </h2>
                                    <p className="text-sm text-gray-900 dark:text-gray-100">
                                        <a href={`mailto:${job.email}`} className="text-blue-600 hover:text-blue-500">
                                            {job.email}
                                        </a>
                                    </p>
                                </div>
                            </div>

                            <div className="mb-8">
                                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                                    Skills Required
                                </h2>
                                <div className="flex flex-wrap gap-2">
                                    {job.skills.map((skill, index) => (
                                        <span
                                            key={index}
                                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                        >
                                            {skill}
                                        </span>
                                    ))}
                                </div>
                            </div>

                            <div className="prose dark:prose-invert max-w-none">
                                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                                    Job Description
                                </h2>
                                <div
                                    dangerouslySetInnerHTML={{ __html: job.description_html }}
                                    className="text-gray-600 dark:text-gray-300"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Back to Jobs Button */}
                    <div className="mt-8 text-center">
                        <a
                            href="/"
                            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            View Latest Jobs
                        </a>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('Page rendering error:', error);
        notFound();
    }
}
