.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.navbar {
  background: linear-gradient(145deg, #ffffff, #f0f0f0);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  padding: 1rem;
  margin-bottom: 2rem;
}

.heroSection {
  background: linear-gradient(135deg, #0062ff 0%, #0039e6 100%);
  color: white;
  padding: 4rem 2rem;
  border-radius: 20px;
  margin-bottom: 3rem;
  text-align: center;
}

.heroTitle {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #ffffff, #e6e9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.heroDescription {
  font-size: 1.25rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.ctaButton {
  background: #00ff88;
  color: #000;
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.ctaButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(0, 255, 136, 0.2);
  background: #00e67a;
}

.section {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.sectionTitle {
  color: #0039e6;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  font-weight: 700;
}

.card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.tableContainer {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  background: #0039e6;
  color: white;
  padding: 1rem;
  font-weight: 600;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.table tr:hover {
  background-color: #f8f9ff;
}

.footer {
  text-align: center;
  padding: 2rem;
  margin-top: 4rem;
  border-top: 1px solid #eee;
}

.footerLogo {
  margin: 0 0.5rem;
  vertical-align: middle;
}

.contactLink {
  color: #0039e6;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.contactLink:hover {
  color: #0062ff;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .heroTitle {
    font-size: 2rem;
  }

  .heroSection {
    padding: 2rem 1rem;
  }

  .table {
    font-size: 0.9rem;
  }
} 