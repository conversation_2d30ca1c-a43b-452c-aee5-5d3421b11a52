"use client";

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/recruiter-dashboard/DashboardLayout';
import { Linkedin, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { useUser } from '@/context/UserContext';
import axios from 'axios';
import LinkedinIntegration from '@/components/LinkedinIntegration';

export default function SettingsPage() {
    return (
        <DashboardLayout>
            <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Settings
                </h1>
                <div className="space-y-6">
                    <LinkedinIntegration />
                </div>
            </div>
        </DashboardLayout>
    );
}
