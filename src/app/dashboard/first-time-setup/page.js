'use client';

import { useState, useEffect, useRef } from 'react';
import { getOrganizationByQuery, createOrganization, updateOrganizationLogo, getAllOrganizations } from '@/utils/organizationOps';
import { updateRecruiter } from '@/utils/recruiterOps';
import CustomPhoneInput from '@/components/PhoneInput';
import { Loader2, Check, Building2, Upload } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

// Default avatar options
const DEFAULT_AVATARS = [
    '/avatars/avatar1.png',
    '/avatars/avatar2.png',
    '/avatars/avatar3.png',
    '/avatars/avatar4.png',
    '/avatars/avatar5.png',
];

export default function FirstTimeSetup() {
    const router = useRouter();
    const [companyName, setCompanyName] = useState('');
    const [phoneNumber, setPhoneNumber] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [selectedCompany, setSelectedCompany] = useState(null);
    const [selectedAvatar, setSelectedAvatar] = useState(null);
    const [customLogo, setCustomLogo] = useState(null);
    const searchTimeoutRef = useRef(null);
    const fileInputRef = useRef(null);
    const dropdownRef = useRef(null);
    const [showDropdown, setShowDropdown] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [userDetails, setUserDetails] = useState(null);

    // Set random avatar on client-side only
    useEffect(() => {
        const randomAvatar = DEFAULT_AVATARS[Math.floor(Math.random() * DEFAULT_AVATARS.length)];
        setSelectedAvatar(randomAvatar);
        const userDetails = JSON.parse(localStorage.getItem('userDetails'));
        setUserDetails(userDetails);
    }, []);

    useEffect(() => {
        // Handle clicks outside of dropdown
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleCompanySearch = async (value) => {
        setCompanyName(value);
        setShowDropdown(true);

        if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
        }

        if (value.trim().length === 0) {
            setSearchResults([]);
            setIsSearching(false);
            return;
        }

        setIsSearching(true);

        searchTimeoutRef.current = setTimeout(async () => {
            try {
                const response = await getOrganizationByQuery(value);
                setSearchResults(response.data.items || []);
            } catch (error) {
                console.error('Error searching organizations:', error);
                setSearchResults([]);
            } finally {
                setIsSearching(false);
            }
        }, 300);
    };

    const handleCompanySelect = (company) => {
        setSelectedCompany(company);
        setCompanyName(company.name);
        setShowDropdown(false);
        // Reset avatar selection if company already has a logo
        if (company.logo) {
            setSelectedAvatar(null);
            setCustomLogo(null);
        } else {
            // If company doesn't have a logo, keep the current avatar selection
            setSelectedAvatar(selectedAvatar || DEFAULT_AVATARS[Math.floor(Math.random() * DEFAULT_AVATARS.length)]);
        }
    };

    const handleFileUpload = (event) => {
        const file = event.target.files?.[0];
        if (file) {
            setCustomLogo(file);
            setSelectedAvatar(null);
        }
    };

    const handleSubmit = async () => {
        try {
            setIsSubmitting(true);

            if (selectedCompany) {
                // Case 1: User selected an existing organization
                const recruiterResponse = await updateRecruiter({
                    userid: userDetails._id,
                    organization: selectedCompany._id,
                    phoneNumber: phoneNumber || undefined
                });
                const updatedDetails = { ...recruiterResponse.data, role: "recruiter"};
                localStorage.setItem('userDetails', JSON.stringify(updatedDetails));
            } else {
                // Case 2: Create new organization
                // First create the organization
                const orgResponse = await createOrganization({
                    company: companyName
                });

                if (customLogo) {
                    // Upload custom logo if provided
                    await updateOrganizationLogo({
                        organizationId: orgResponse.data._id,
                        logo: customLogo
                    });
                } else if (selectedAvatar) {
                    // Convert default avatar to File object and upload
                    try {
                        const response = await fetch(selectedAvatar);
                        const blob = await response.blob();
                        const file = new File([blob], 'avatar.png', { type: 'image/png' });

                        await updateOrganizationLogo({
                            organizationId: orgResponse.data._id,
                            logo: file
                        });
                    } catch (error) {
                        console.error('Error uploading default avatar:', error);
                    }
                }

                // Update user with new organization
                const recruiterResponse = await updateRecruiter({
                    userid: userDetails._id,
                    organization: orgResponse.data._id,
                    phoneNumber: phoneNumber || undefined
                });
                const updatedDetails = { ...recruiterResponse.data, role: "recruiter"};
                localStorage.setItem('userDetails', JSON.stringify(updatedDetails));
            }

            // Redirect to post job page
            router.push('/dashboard/post-job');
        } catch (error) {
            console.error('Error during setup:', error);
            // Here you might want to show an error message to the user
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
            <div className="space-y-8">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        Complete Your Profile
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400">
                        Let's get your account set up with some basic information.
                    </p>
                </div>

                {/* Company Name Input with Search */}
                <div className="space-y-2" ref={dropdownRef}>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Company Name
                    </label>
                    <div className="relative">
                        <div className="relative">
                            <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500" />
                            <input
                                type="text"
                                value={companyName}
                                onChange={(e) => handleCompanySearch(e.target.value)}
                                className="w-full pl-10 pr-10 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                                placeholder="Enter company name..."
                            />
                            {companyName && (
                                <button
                                    onClick={() => {
                                        setCompanyName('');
                                        setSelectedCompany(null);
                                        setSearchResults([]);
                                        setShowDropdown(false);
                                        setSelectedAvatar(null);
                                        setCustomLogo(null);
                                    }}
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                </button>
                            )}
                        </div>

                        {/* Dropdown for search results */}
                        {showDropdown && (companyName.trim().length > 0 || isSearching) && (
                            <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto">
                                {isSearching ? (
                                    <div className="flex items-center justify-center p-4">
                                        <Loader2 className="w-6 h-6 animate-spin text-blue-500 dark:text-blue-400" />
                                        <span className="ml-2 text-gray-600 dark:text-gray-400">Searching...</span>
                                    </div>
                                ) : searchResults.length > 0 ? (
                                    searchResults.map((company) => (
                                        <button
                                            key={company._id}
                                            onClick={() => handleCompanySelect(company)}
                                            className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-3 transition-colors"
                                        >
                                            {company.logo ? (
                                                <Image
                                                    src={company.logo.url}
                                                    alt={company.name}
                                                    width={24}
                                                    height={24}
                                                    className="rounded-full"
                                                />
                                            ) : (
                                                <div className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                                    <span className="text-xs text-gray-600 dark:text-gray-300">{company.name[0]}</span>
                                                </div>
                                            )}
                                            <span className="text-gray-900 dark:text-white">{company.name}</span>
                                            {selectedCompany?._id === company._id && (
                                                <Check className="w-5 h-5 text-green-500 ml-auto" />
                                            )}
                                        </button>
                                    ))
                                ) : (
                                    <div className="p-4 text-gray-600 dark:text-gray-400 text-center">
                                        No companies found
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>

                {/* Phone Number Input */}
                <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Phone Number <span className="text-gray-500 dark:text-gray-400">(optional)</span>
                    </label>
                    <CustomPhoneInput
                        phoneNumber={phoneNumber}
                        setPhoneNumber={setPhoneNumber}
                    />
                </div>

                {/* Company Logo Section - Only show if selected company doesn't have a logo */}
                {(!selectedCompany?.logo) && (
                    <div className="space-y-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Choose an avatar or upload company logo
                        </label>

                        {/* Default Avatars */}
                        <div className="grid grid-cols-5 gap-4 mb-4">
                            {DEFAULT_AVATARS.map((avatar, index) => (
                                <button
                                    key={index}
                                    onClick={() => {
                                        setSelectedAvatar(avatar);
                                        setCustomLogo(null);
                                    }}
                                    className={`relative rounded-lg overflow-hidden aspect-square
                                        ${selectedAvatar === avatar ? 'ring-2 ring-blue-500' : 'hover:ring-2 hover:ring-gray-300 dark:hover:ring-gray-500'}
                                    `}
                                >
                                    <Image
                                        src={avatar}
                                        alt={`Avatar ${index + 1}`}
                                        fill
                                        className="object-cover p-4"
                                    />
                                </button>
                            ))}
                        </div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            or
                        </label>
                        {/* Custom Logo Upload */}
                        <div className="flex items-center justify-center w-full">
                            <label className="w-full flex flex-col items-center justify-center h-32 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                    {customLogo ? (
                                        <>
                                            <div className="flex flex-col items-center">
                                                <Check className="w-8 h-8 text-green-500 mb-2" />
                                                <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                                                    {customLogo.name}
                                                </p>
                                                <button
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        setCustomLogo(null);
                                                        if (fileInputRef.current) {
                                                            fileInputRef.current.value = '';
                                                        }
                                                    }}
                                                    className="flex items-center text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 text-sm transition-colors"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                                                    </svg>
                                                    Remove file
                                                </button>
                                            </div>
                                        </>
                                    ) : (
                                        <>
                                            <Upload className="w-8 h-8 text-gray-500 dark:text-gray-400 mb-2" />
                                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                                Upload your company logo
                                            </p>
                                        </>
                                    )}
                                </div>
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    className="hidden"
                                    accept="image/*"
                                    onChange={handleFileUpload}
                                />
                            </label>
                        </div>
                    </div>
                )}

                {/* Submit Button */}
                <button
                    onClick={handleSubmit}
                    disabled={!companyName.trim() || isSubmitting}
                    className="w-full py-3 px-4 bg-blue-500 dark:bg-blue-600 text-white rounded-lg hover:bg-blue-600 dark:hover:bg-blue-700 transition-colors
                             disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:text-gray-500 dark:disabled:text-gray-400 disabled:cursor-not-allowed"
                >
                    {isSubmitting ? (
                        <div className="flex items-center justify-center">
                            <Loader2 className="w-5 h-5 animate-spin mr-2" />
                            Setting up...
                        </div>
                    ) : (
                        'Complete Setup & Post job'
                    )}
                </button>
            </div>
        </div>
    );
}
