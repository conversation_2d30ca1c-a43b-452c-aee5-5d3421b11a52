"use client";
import { useState, useEffect } from "react";
import { use } from "react"; // Import React.use
import { getJobById, updateJobStatus, closeJobAndLetCandidatesInform } from "@/utils/jobsOps";
import DashboardLayout from "@/components/recruiter-dashboard/DashboardLayout";
import { BriefcaseIcon, ClockIcon, UsersIcon, MapPinIcon, ChevronDownIcon, ChevronUpIcon, XCircle, CheckCircle2 } from 'lucide-react';
import JobApplicationTable from "@/components/recruiter-dashboard/JobApplicationTable";
import { toast } from 'react-toastify';

export const dynamic = "force-dynamic";

const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInDays === 0) {
        return 'Today';
    } else if (diffInDays === 1) {
        return 'Yesterday';
    } else if (diffInDays < 7) {
        return `${diffInDays} days ago`;
    } else if (diffInDays < 30) {
        const weeks = Math.floor(diffInDays / 7);
        return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
    } else {
        return date.toLocaleDateString();
    }
};

export default function JobDetailsPage({ params }) {
    const resolvedParams = use(params);
    const [jobData, setJobData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [isDescriptionOpen, setIsDescriptionOpen] = useState(false);
    const [isStatusUpdating, setIsStatusUpdating] = useState(false);

    const handleJobStatusToggle = async () => {
        if (isStatusUpdating) return;

        setIsStatusUpdating(true);
        try {
            // Pass the opposite of current active status
            await updateJobStatus(resolvedParams.id, !jobData.active);
            if (jobData.active) {
                await closeJobAndLetCandidatesInform(resolvedParams.id);
            }
            // Update local state
            setJobData(prev => ({ ...prev, active: !prev.active }));

            // Show success toast
            toast.success(
                jobData.active
                    ? 'Job closed successfully'
                    : 'Job reopened successfully',
                {
                    position: "bottom-right",
                    autoClose: 3000,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    draggable: true,
                    progress: undefined,
                }
            );
        } catch (error) {
            console.error('Error updating job status:', error);
            toast.error('Failed to update job status', {
                position: "bottom-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
                progress: undefined,
            });
        } finally {
            setIsStatusUpdating(false);
        }
    };

    useEffect(() => {
        const fetchJobData = async () => {
            try {
                const job = await getJobById(resolvedParams.id); // Use resolvedParams instead of params
                setJobData(job.data);
            } catch (error) {
                console.error("Failed to fetch job data:", error);
            } finally {
                setLoading(false); // Set loading to false after fetching
            }
        };
        fetchJobData();
    }, [resolvedParams.id]); // Depend on resolvedParams.id

    return (
        <DashboardLayout>
            <div className="p-6 max-w-6xl mx-auto">
                {loading ? (
                    <div className="flex flex-col justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                        <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">Loading job details...</p>
                    </div>
                ) : jobData ? (
                    <div className="space-y-6">
                        {/* Header Section with Compact Info */}
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                            <div className="space-y-4">
                                {/* Title and Status */}
                                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                                    <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
                                        {jobData.role}
                                    </h1>
                                    <div className="flex items-center gap-3">
                                        <span className={`ijobnline-flex rounded-full px-3 py-1 text-sm font-semibold ${jobData.active
                                            ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                                            : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                                            }`}>
                                            {jobData.active ? "Active" : "Inactive"}
                                        </span>
                                        <button
                                            onClick={handleJobStatusToggle}
                                            disabled={isStatusUpdating}
                                            className={`px-4 py-2 rounded-lg transition duration-300 flex items-center justify-center gap-2 text-sm
                                                ${jobData.active
                                                    ? 'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30'
                                                    : 'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30'
                                                }`}
                                        >
                                            {isStatusUpdating ? (
                                                <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-current border-r-transparent"></span>
                                            ) : jobData.active ? (
                                                <>
                                                    <XCircle className="w-4 h-4" />
                                                    Close Job
                                                </>
                                            ) : (
                                                <>
                                                    <CheckCircle2 className="w-4 h-4" />
                                                    Reopen Job
                                                </>
                                            )}
                                        </button>
                                    </div>
                                </div>

                                {/* Description Toggle Button */}
                                <button
                                    onClick={() => setIsDescriptionOpen(!isDescriptionOpen)}
                                    className="w-full mt-4 px-4 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg flex items-center justify-between text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                                >
                                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
                                        <div className="flex items-center gap-1">
                                            <MapPinIcon className="w-4 h-4" />
                                            <span>{jobData.locality.city}, {jobData.locality.state}</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <BriefcaseIcon className="w-4 h-4" />
                                            <span>{jobData.job_type}</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <ClockIcon className="w-4 h-4" />
                                            <span>{formatRelativeTime(jobData.createdAt)}</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <UsersIcon className="w-4 h-4" />
                                            <span>{jobData.no_of_applicants || 0} applicants</span>
                                        </div>
                                    </div>
                                    <span>Job Description</span>
                                    {isDescriptionOpen ? (
                                        <ChevronUpIcon className="w-5 h-5" />
                                    ) : (
                                        <ChevronDownIcon className="w-5 h-5" />
                                    )}
                                </button>

                                {/* Collapsible Description */}
                                {isDescriptionOpen && (
                                    <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                        <p className="text-gray-600 dark:text-gray-300 whitespace-pre-wrap">
                                            {jobData.description}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                        {/* Additional Details or Other Content */}
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                            <JobApplicationTable jobId={resolvedParams.id} />
                        </div>
                    </div>
                ) : (
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
                        <p className="text-gray-500 dark:text-gray-400">Job not found</p>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
}