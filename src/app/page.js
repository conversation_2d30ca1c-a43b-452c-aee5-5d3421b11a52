import JobCard from '@/components/JobCard';
import JobFilters from '@/components/JobFilters';
import OldJobCard from '@/components/OldJobCard';
import { jobService, oldJobService } from '@/server/services';
import { getRandomSize, shuffleArray } from '@/utils/generalUiOps';
import ClientJobList from '@/components/ClientJobList'; // New client-side component
import ProfilesCardStack from '@/components/ProfilesCardStack';
import Image from 'next/image';

const baseCompanies = [
  'Aether Talent Solutions Inc',
  'Apetan Inc',
  'Apex Systems',
  // ... other companies
].map(name => ({ name, size: getRandomSize() }));


async function getJobs() {
  try {
    const data = await jobService.getAll({ created_at: "desc" });
    // Convert Mongoose documents to plain objects
    const jobs = data.items.map(item => {
      // Use toObject if available, otherwise spread the item
      const plainItem = item.toObject ? item.toObject() : { ...item };
      // Ensure nested objects are serialized
      return JSON.parse(JSON.stringify(plainItem));
    });
    return jobs;
  } catch (error) {
    console.error('Error loading jobs:', error);
    return [];
  }
}

async function getOldJobs() {
  try {
    const data = await oldJobService.getOldJobs();
    // Convert old jobs to plain objects
    const plainJobs = data.map(item => {
      const plainItem = item.toObject ? item.toObject() : { ...item };
      return JSON.parse(JSON.stringify(plainItem));
    });
    return [...plainJobs].reverse();
  } catch (error) {
    console.error('Error loading old jobs:', error);
    return [];
  }
}

export default async function Home() {
  const initialJobs = await getJobs();
  const initialOldJobs = await getOldJobs();

  return (
    <main className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <section className="relative z-20">
        <div className="w-full bg-customBlue-light dark:bg-opacity-90 relative overflow-hidden">
          {/* Slanted background overlay - for bottom only */}
          <div className="absolute bottom-0 left-0 right-0 h-40 overflow-hidden">
            <div className="absolute inset-0 transform -skew-y-6 origin-bottom-right bg-white dark:bg-gray-900"></div>
          </div>

          {/* Content */}
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-28 md:py-36">
            {/* Two-column layout */}
            <div className="flex flex-col md:flex-row md:items-start md:space-x-8">
              {/* Left column: Title + Search */}
              <div className="md:w-3/5 space-y-2 sm:space-y-2 md:space-y-2">
                <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white">
                  Unlock the secret to getting hired as a contractor
                </h1>
                <h3 className="text-1xl sm:text-1xl md:text-1xl lg:text-1xl font-normal text-white">Dive into our 23043+ contract jobs collection & Join community of thousands of sucessfull contractors</h3>
                <div className="flex justify-start w-full">
                  <div className="w-full md:w-auto md:min-w-[450px]">
                    <JobFilters />
                  </div>
                </div>
                <div className='mt-10'>
                  <ProfilesCardStack />
                </div>
              </div>

              {/* Right column: Congratulatory Card with Image */}
              <div className="md:w-2/5 mt-10 md:mt-0 flex flex-col h-full relative">
                <div className="relative w-full h-[600px] overflow-hidden rounded-2xl shadow-xl">
                  <Image
                    src="/assets/people/maria_franco.png"
                    alt="Professional woman smiling"
                    fill
                    className="object-cover object-bottom"
                    priority
                  />
                  {/* Overlay with congratulatory message */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/25 to-transparent flex flex-col justify-end p-8">
                    <div className="text-6xl mb-6">🎉</div>
                    <h2 className="text-3xl font-bold mb-4 text-white">Congratulations on your new contract!</h2>
                    <p className="text-xl text-white/90 mb-8">Thanks @Megha for beign a active member of the community!</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="relative py-12 md:py-20 bg-white dark:bg-gray-900 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <ClientJobList initialJobs={initialJobs} initialOldJobs={initialOldJobs} />
        </div>
      </section>
    </main>
  );
}