export default function Pricing() {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="text-center mb-16">
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                        Simple, Transparent Pricing
                    </h1>
                    <p className="text-xl text-gray-600 dark:text-gray-400">
                        Choose the plan that's right for you
                    </p>
                </div>

                {/* Pricing Cards */}
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {/* Free Tier */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                        <div className="p-6">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                Free
                            </h2>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                Get started with essential features
                            </p>
                            <p className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                                $0
                                <span className="text-base font-normal text-gray-600 dark:text-gray-400">
                                    /month
                                </span>
                            </p>
                            <ul className="space-y-4 mb-8">
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Post unlimited jobs
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Basic job listings
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Email support
                                </li>
                            </ul>
                            <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                                Get Started
                            </button>
                        </div>
                    </div>

                    {/* Premium Tier */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden ring-2 ring-blue-500">
                        <div className="absolute top-4 right-4">
                            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                Most Popular
                            </span>
                        </div>
                        <div className="p-6">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                Premium
                            </h2>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                For serious job seekers
                            </p>
                            <p className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                                $1.99
                                <span className="text-base font-normal text-gray-600 dark:text-gray-400">
                                    /month
                                </span>
                            </p>
                            <ul className="space-y-4 mb-8">
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Get to know jobs first
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Profile promotion to recruiters
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Daily verified job listings
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Priority application processing
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    14-day free trial
                                </li>
                            </ul>
                            <a
                                href="/subscription/candidates/pricing"
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors text-center block"
                            >
                                Start Free Trial
                            </a>
                        </div>
                    </div>

                    {/* Basic Tier (Coming Soon) */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden opacity-75">
                        <div className="absolute top-4 right-4">
                            <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                Coming Soon
                            </span>
                        </div>
                        <div className="p-6">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                Basic
                            </h2>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                For growing businesses
                            </p>
                            <p className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                                Coming Soon
                            </p>
                            <ul className="space-y-4 mb-8">
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Everything in Free
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Featured job listings
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Priority support
                                </li>
                            </ul>
                            <button disabled className="w-full bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 py-2 px-4 rounded-md cursor-not-allowed">
                                Coming Soon
                            </button>
                        </div>
                    </div>

                    {/* Pro Tier (Coming Soon) */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden opacity-75">
                        <div className="absolute top-4 right-4">
                            <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                Coming Soon
                            </span>
                        </div>
                        <div className="p-6">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                Pro
                            </h2>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                For professional recruiters
                            </p>
                            <p className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                                Coming Soon
                            </p>
                            <ul className="space-y-4 mb-8">
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Everything in Basic
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Advanced analytics
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Dedicated support
                                </li>
                            </ul>
                            <button disabled className="w-full bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 py-2 px-4 rounded-md cursor-not-allowed">
                                Coming Soon
                            </button>
                        </div>
                    </div>

                    {/* Enterprise Tier (Coming Soon) */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden opacity-75">
                        <div className="absolute top-4 right-4">
                            <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                Coming Soon
                            </span>
                        </div>
                        <div className="p-6">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                Enterprise
                            </h2>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                Custom solutions for large teams
                            </p>
                            <p className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                                Coming Soon
                            </p>
                            <ul className="space-y-4 mb-8">
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Everything in Pro
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Custom integration
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    24/7 support
                                </li>
                            </ul>
                            <button disabled className="w-full bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 py-2 px-4 rounded-md cursor-not-allowed">
                                Coming Soon
                            </button>
                        </div>
                    </div>
                </div>

                {/* FAQ or Additional Info */}
                <div className="mt-16 text-center">
                    <p className="text-gray-600 dark:text-gray-400">
                        Need a custom solution? <a href="/contact" className="text-blue-600 hover:text-blue-500">Contact us</a>
                    </p>
                </div>
            </div>
        </div>
    );
} 