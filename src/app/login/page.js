"use client";

import { useSearchParams } from 'next/navigation';
import LoginCard from '@/components/LoginCard';
import { useSession } from 'next-auth/react';
import { Suspense, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AuthClient() {
    const searchParams = useSearchParams();
    const urlRole = searchParams.get('role');
    const callbackUrl = searchParams.get('callbackUrl');
    const router = useRouter();

    const { status } = useSession();

    useEffect(() => {
        const userDetails = localStorage.getItem('userDetails');
        if (userDetails) localStorage.removeItem('userDetails');
    }, []);

    // If user is already authenticated and there's a callback URL, redirect to it
    useEffect(() => {
        if (status === 'authenticated' && callbackUrl) {
            router.push(callbackUrl);
        }
    }, [status, callbackUrl, router]);

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
            <Suspense>
                <LoginCard
                    defaultRole={urlRole || 'candidate'}
                    hideRoleSelector={!!urlRole}
                    callbackUrl={callbackUrl}
                />
            </Suspense>
        </div>
    );
}