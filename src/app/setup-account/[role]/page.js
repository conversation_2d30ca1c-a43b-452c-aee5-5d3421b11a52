"use client";

import React, { useEffect, useState } from 'react';
import getProfile from '@/utils/userDetails';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/context/UserContext';
import { useParams, useSearchParams } from 'next/navigation';
import { getMyPreviouslyUploadedResumes } from '@/utils/resumeOps';

const AccountSetupPage = () => {
    const [userAcknowledgement, setUserAcknowledgement] = useState('Please wait while we prepare everything for you.');
    const { data: session, status } = useSession({required: true});
    const router = useRouter();
    const { updateUserAvailability } = useUser();

    const { role } = useParams();
    const searchParams = useSearchParams();

    useEffect(() => {
        const fetchAndSetUserDetails = () => {
            if (status === 'unauthenticated') {
                return router.push('/');
            }

            getProfile(role)
                .then(async details => {
                    let resumes = [];
                    details.data.profile.user["image"] = session?.user?.image || null;
                    if(role == "candidate") {
                        setUserAcknowledgement('Preparing your previously uploaded resumes...');
                        const items = await getMyPreviouslyUploadedResumes();
                        resumes = [...items.items];
                    }
                    setUserAcknowledgement('Preparing your account...');
                    const updatedDetails = { ...details.data.profile, role: role, resumes: resumes };
                    localStorage.setItem('userDetails', JSON.stringify(updatedDetails));
                    updateUserAvailability();
                    if(role == "recruiter" && !updatedDetails.hasOwnProperty('organization')) {
                        router.push('/dashboard/first-time-setup');
                    }else{
                        setUserAcknowledgement('Account is getting ready! Redirecting to your account...');
                        const redirectUri = searchParams.get('redirectUri');
                        router.push(redirectUri || '/');
                    }
                })
                .catch(error => {
                    console.error('Error fetching user details:', error);
                    setUserAcknowledgement('Something went wrong! We are unable to prepare your account. Please try again later.');
                });
        };
        fetchAndSetUserDetails();
    }, [status, searchParams]);

    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-100">
            <div className="flex items-center justify-center mb-4">
                <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
            </div>
            <h1 className="text-2xl font-semibold">{userAcknowledgement}</h1>
        </div>
    );
}

export default AccountSetupPage;
