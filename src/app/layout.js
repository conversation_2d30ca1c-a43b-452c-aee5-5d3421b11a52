import { <PERSON>eist } from "next/font/google";
import "./../../styles/globals.css";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Script from 'next/script';
import { SessionProvider } from "next-auth/react";
import { Suspense } from 'react';
import { UserProvider } from '@/context/UserContext';
import { getBaseMetadata } from '@/utils/metadataUtils';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CookieBanner from '@/components/CookieBanner';

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist",
});

export const viewport = {
  themeColor: '#05668D',
}

export const metadata = getBaseMetadata();

export default function RootLayout({ children }) {


  return (
    <html lang="en">
      <head>
        <Script
          defer
          data-domain="onlyc2c.com"
          src="https://plausible.io/js/script.js"
          strategy="afterInteractive"
        />
      </head>
      <body className={`${geist.variable} font-sans`}>
        <UserProvider>
          <SessionProvider>
            <Suspense>
              <Navbar />
              <main className="min-h-screen pt-16">
                {children}
              </main>
              <Footer />
            </Suspense>
          </SessionProvider>
        </UserProvider>
        <CookieBanner />
        <ToastContainer
          position="bottom-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="colored"
        />
      </body>
    </html>
  );
}
