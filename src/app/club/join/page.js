'use client';

import { useState } from 'react';
import { ArrowRight, CheckCircle } from 'lucide-react';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import axios from 'axios';

const WaitlistForm = () => {
    const [email, setEmail] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const router = useRouter();

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/subscriptions`,
                { email },
                {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );

            setIsSubmitted(true);
            toast.success('Successfully added to waitlist! We will get in touch with you soon.', {
                onClose: () => {
                    router.push('/');
                }
            });
        } catch (error) {
            console.error('Error joining waitlist:', error);
            toast.error(error.response?.data?.message || 'Failed to join waitlist. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
            {/* Video Background */}
            <div className="absolute inset-0 w-full h-full z-0">
                <video
                    autoPlay
                    loop
                    muted
                    playsInline
                    className="absolute min-w-full min-h-full object-cover"
                    style={{ width: '100%', height: '100%' }}
                >
                    <source
                        src="/assets/videos/background.mp4"
                        type="video/mp4"
                        onError={(e) => console.error('Source error:', e)}
                    />
                    Your browser does not support the video tag.
                </video>
                {/* Optional overlay to make content more readable */}
                <div className="absolute inset-0 bg-black/50"></div>
            </div>

            {/* Main Content */}
            <div className="w-full max-w-6xl px-6 relative z-10">
                <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl border-2 border-gray-200 dark:border-gray-700 shadow-xl p-12">
                    <div className="text-center mb-12">
                        <h1 className="mb-2">
                            <span className="block text-5xl sm:text-5xl md:text-5xl font-extrabold text-gray-900 dark:text-white leading-tight">
                                Don't chase after recruiters
                            </span>
                            <span className="block text-6xl sm:text-7xl md:text-7xl font-extrabold text-gray-900 dark:text-white leading-tight mt-6">
                                Let them Chase you!
                            </span>
                        </h1>
                        <div className="mt-8">
                            <span className="inline-block text-4xl font-bold text-blue-600 dark:text-blue-400">
                                $1.99
                            </span>
                            <span className="text-xl text-gray-600 dark:text-gray-300">
                                /month
                            </span>
                        </div>
                    </div>

                    <div className="max-w-2xl mx-auto">
                        {isSubmitted ? (
                            <div className="text-center py-8">
                                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/30 mb-4">
                                    <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                                </div>
                                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
                                    You're on the list!
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    We'll notify you when we launch. Stay tuned!
                                </p>
                            </div>
                        ) : (
                            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3 max-w-xl mx-auto">
                                <div className="flex-grow">
                                    <input
                                        type="email"
                                        id="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                        placeholder="Enter your email"
                                        className="w-full px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-600 
                                                 bg-white dark:bg-gray-700 
                                                 text-gray-900 dark:text-white 
                                                 placeholder-gray-400 dark:placeholder-gray-500
                                                 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 
                                                 dark:focus:border-blue-500 focus:outline-none
                                                 transition-all duration-200"
                                    />
                                </div>
                                <button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg text-white 
                                             transition-all duration-200 whitespace-nowrap
                                             ${isSubmitting
                                            ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
                                            : 'bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 shadow-lg hover:shadow-green-500/50'
                                        }`}
                                >
                                    {isSubmitting ? (
                                        <>
                                            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                            Joining...
                                        </>
                                    ) : (
                                        <>
                                            Join Premium (14 day free trial)
                                            <ArrowRight className="w-5 h-5" />
                                        </>
                                    )}
                                </button>
                            </form>
                        )}

                        <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-4">
                            By joining, you agree to receive updates about the ONLYC2C Vetted Club. Monthly subscription fee: $1.99
                        </p>
                        {/* Benefits Section */}
                        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-100 dark:border-gray-700">
                                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">You get to know first!</h3>
                                <p className="text-gray-600 dark:text-gray-300 text-sm">
                                    Get to know a job right away when it's posted.
                                </p>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-100 dark:border-gray-700">
                                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Promote your profile</h3>
                                <p className="text-gray-600 dark:text-gray-300 text-sm">
                                    We personally promote your profile to recruiters while they post jobs automatically.
                                </p>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-100 dark:border-gray-700">
                                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Exclusive Benefits</h3>
                                <p className="text-gray-600 dark:text-gray-300 text-sm">
                                    Get free list of of jobs that are verified by our team, Send to you every day morning.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WaitlistForm;
