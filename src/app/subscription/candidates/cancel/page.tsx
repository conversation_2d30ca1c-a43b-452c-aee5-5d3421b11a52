'use client';

import { X<PERSON>ir<PERSON>, ArrowLeft, CreditCard } from 'lucide-react';
import Link from 'next/link';

export default function SubscriptionCancelPage() {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
            <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center">
                <div className="mb-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full mb-4">
                        <XCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        Subscription Cancelled
                    </h1>
                    <p className="text-gray-600 dark:text-gray-300">
                        Your subscription process was cancelled. No charges were made.
                    </p>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                        Don't worry! You can try again anytime. Your premium features are waiting for you.
                    </p>
                </div>

                <div className="space-y-3">
                    <Link
                        href="/subscription/candidates/pricing"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center"
                    >
                        <CreditCard className="w-4 h-4 mr-2" />
                        Try Again
                    </Link>
                    
                    <Link
                        href="/dashboard"
                        className="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center"
                    >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Dashboard
                    </Link>
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                        Need help? Contact our support team at{' '}
                        <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
                            <EMAIL>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    );
}
