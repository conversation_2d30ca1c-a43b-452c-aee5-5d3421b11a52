'use client';

import { useEffect, useState } from 'react';
import { CheckCircle, Crown, ArrowRight } from 'lucide-react';
import Link from 'next/link';

export default function SubscriptionSuccessPage() {
    const [countdown, setCountdown] = useState(5);

    useEffect(() => {
        const timer = setInterval(() => {
            setCountdown((prev) => {
                if (prev <= 1) {
                    clearInterval(timer);
                    window.location.href = '/dashboard';
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
            <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center">
                <div className="mb-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full mb-4">
                        <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        Welcome to Premium!
                    </h1>
                    <p className="text-gray-600 dark:text-gray-300">
                        Your subscription has been activated successfully.
                    </p>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                    <div className="flex items-center justify-center mb-2">
                        <Crown className="w-5 h-5 text-blue-500 mr-2" />
                        <span className="font-semibold text-blue-700 dark:text-blue-300">
                            Premium Features Unlocked
                        </span>
                    </div>
                    <ul className="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                        <li>✓ Instant job notifications</li>
                        <li>✓ Profile promotion to recruiters</li>
                        <li>✓ Daily verified job listings</li>
                        <li>✓ Priority application processing</li>
                    </ul>
                </div>

                <div className="space-y-3">
                    <Link
                        href="/dashboard"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center"
                    >
                        Go to Dashboard
                        <ArrowRight className="w-4 h-4 ml-2" />
                    </Link>
                    
                    <Link
                        href="/subscription/candidates/manage"
                        className="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white py-3 px-4 rounded-lg font-medium transition-colors block"
                    >
                        Manage Subscription
                    </Link>
                </div>

                <p className="text-xs text-gray-500 dark:text-gray-400 mt-6">
                    Redirecting to dashboard in {countdown} seconds...
                </p>
            </div>
        </div>
    );
}
