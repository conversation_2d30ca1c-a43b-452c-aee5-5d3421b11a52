/* Import Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom Global Styles */
:root {
  --primary-color: #0039e6;
  --secondary-color: #00ff88;
  --text-color: #333;
  --background-color: #f8f9fa;
}

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
}

* {
  box-sizing: border-box;
}

/* Custom Button Styles */
.btn {
  border-radius: 30px;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #0062ff;
  border-color: #0062ff;
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Custom Link Styles */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #0062ff;
  text-decoration: none;
}

/* Responsive Font Sizes */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

/* Custom Table Styles */
.table-responsive {
  margin-bottom: 1rem;
  border-radius: 10px;
  overflow: hidden;
}

/* Custom Card Styles */
.card {
  border: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* List Group Styles */
.list-group-item {
  border-left: none;
  border-right: none;
  padding: 1rem;
  transition: background-color 0.3s ease;
}

.list-group-item:hover {
  background-color: #f8f9ff;
}

/* Custom Image Styles */
.img-fluid {
  border-radius: 10px;
} 