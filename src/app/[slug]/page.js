// app/[slug]/page.js
import JobCard from '@/components/JobCard';
import { jobService } from '@/server/services';
import { generateMetadata as generateCustomMetadata } from '@/utils/metadataUtils';


// Parse slug into filters
function parseSlug(slug) {
  const segments = slug.split('-');
  const filters = {
    skills: [],
    states: [],
    cities: [],
    types: [],
    jobid: null,
  };
  let currentKey = null;

  for (const segment of segments) {
    if (segment === 'skill') {
      currentKey = 'skills';
    } else if (segment === 'state') {
      currentKey = 'states';
    } else if (segment === 'city') {
      currentKey = 'cities';
    } else if (segment === 'type') {
      currentKey = 'types';
    } else if (segment === 'jobid') {
      currentKey = 'jobid';
    } else if (currentKey) {
      if (currentKey === 'jobid') {
        const jobidIndex = segments.indexOf('jobid');
        if (jobidIndex !== -1 && jobidIndex + 1 < segments.length) {
          filters.jobid = segments.slice(jobidIndex + 1).join('-');
          break; // Exit the loop since we've processed everything after jobid
        }
      } else {
        filters[currentKey].push(segment.replace('_', ' ')); // Replace underscores with spaces
      }
    }
  }
  return filters;
}

// Fetch jobs based on parsed filters
async function getJobsBySlug(slug, page = 1, limit = 10) {
  try {
    const filters = parseSlug(slug);
    const query = {
      created_at: 'desc', // Default sort
      page: page,
      limit: limit
    };

    // Apply filters if they exist
    if (filters.skills.length > 0) query.skills = filters.skills;
    if (filters.states.length > 0) query.states = filters.states;
    if (filters.cities.length > 0) query.cities = filters.cities;
    if (filters.types.length > 0) query.job_type = filters.types;

    if (filters.jobid) {
      const data = await jobService.getAll({ job_id: filters.jobid });
      console.log("data", data);
      // Check the structure of the response
      if (data && data.items) {
        // Convert to array if it's not already
        const jobItems = Array.isArray(data.items) ? data.items : [data.items];
        return {
          items: jobItems.map(item => item.toObject ? item.toObject() : { ...item }),
          total: data.total || 1,
          count: data.count || 1,
          page: data.page || 1,
          pages: Math.ceil((data.total || 1) / limit)
        };
      }
      return {
        items: [],
        total: 0,
        count: 0,
        page: 1,
        pages: 0
      };
    }

    const data = await jobService.getAll(query);

    // Handle the response structure you provided
    if (data && data.items) {
      return {
        items: data.items.map(item => item.toObject ? item.toObject() : { ...item }),
        total: data.total || 0,
        count: data.count || 0,
        page: data.page || 1,
        pages: Math.ceil((data.total || 0) / limit)
      };
    } else {
      // Fallback for other structures
      console.log("Unexpected data structure:", data);
      return {
        items: [],
        total: 0,
        count: 0,
        page: 1,
        pages: 0
      };
    }
  } catch (error) {
    console.error('Error loading jobs:', error);
    return {
      items: [],
      total: 0,
      count: 0,
      page: 1,
      pages: 0
    };
  }
}

// Generate a readable title from filters
function generateTitle(filters) {
  const parts = [];
  if (filters.skills.length > 0) parts.push(filters.skills.join(', ') + ' corp-to-corp jobs');
  if (filters.states.length > 0) parts.push('in ' + filters.states.join(', '));
  if (filters.cities.length > 0) parts.push('in ' + filters.cities.join(', '));
  if (filters.types.length > 0) parts.push(filters.types.join(', '));
  return parts.length > 0 ? parts.join(' ') : 'C2C Jobs';
}

// Generate metadata for SEO
export async function generateMetadata({ params }) {
  const { slug } = params;
  const filters = parseSlug(slug);

  if (filters.jobid) {
    // For a specific job
    try {
      const data = await jobService.getAll({ job_id: filters.jobid });
      if (data && data.items && data.items.length > 0) {
        const job = data.items[0];

        // Create job-specific metadata
        return generateCustomMetadata({
          title: job.title,
          description: job.meta_description || `${job.role} job in ${job.locality.city}, ${job.locality.state}. ${job.job_type} position with ${job.organization?.name}.`,
          keywords: job.meta_keywords?.join(', ') || `${job.role}, ${job.job_type}, ${job.locality.city}, ${job.locality.state}, C2C, Corp to Corp`,
          openGraph: {
            title: job.title,
            description: job.meta_description || `${job.role} job in ${job.locality.city}, ${job.locality.state}. ${job.job_type} position with ${job.organization?.name}.`,
            url: job.seo_link || `https://onlyc2c.com/c2c-jobid-${job.job_id}`,
            type: 'article',
          },
          twitter: {
            title: job.title,
            description: job.meta_description || `${job.role} job in ${job.locality.city}, ${job.locality.state}. ${job.job_type} position with ${job.organization?.name}.`,
          },
          alternates: {
            canonical: job.seo_link || `https://onlyc2c.com/c2c-jobid-${job.job_id}`,
          }
        });
      }
    } catch (error) {
      console.error('Error loading job for metadata:', error);
    }
  }

  // For job listings
  const title = generateTitle(filters);
  return generateCustomMetadata({
    title: `${title} - OnlyC2C`,
    description: `Explore the latest ${title} C2C jobs on OnlyC2C. Find the best Corp to Corp opportunities matching your skills.`,
    keywords: `${title}, Corp to Corp, C2C, Jobs, ${filters.skills.join(', ')}, ${filters.states.join(', ')}, ${filters.cities.join(', ')}`,
    openGraph: {
      title: `${title} - OnlyC2C`,
      description: `Explore the latest ${title} C2C jobs on OnlyC2C. Find the best Corp to Corp opportunities matching your skills.`,
      url: `https://onlyc2c.com/${slug}`,
    },
    twitter: {
      title: `${title} - OnlyC2C`,
      description: `Explore the latest ${title} C2C jobs on OnlyC2C. Find the best Corp to Corp opportunities matching your skills.`,
    },
    alternates: {
      canonical: `https://onlyc2c.com/${slug}`,
    }
  });
}

export default async function JobPage({ params }) {
  const { slug } = params;
  const filters = parseSlug(slug);
  const jobs = await getJobsBySlug(slug);

  // Check if it's a job-related slug
  const isJobRelatedSlug = slug.includes('c2c') || slug.includes('corptocorp');
  if (!isJobRelatedSlug) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-center py-20">
        <p className="text-gray-500 dark:text-gray-400">404 - Page Not Found</p>
      </div>
    );
  }

  const title = generateTitle(filters);

  return (
    <main className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* Hero Section */}
      <section className="relative z-20">
        <div className="w-full bg-customBlue-light dark:bg-opacity-90 relative overflow-hidden">
          {/* Slanted background overlay */}
          <div className="absolute bottom-0 left-0 right-0 h-40 overflow-hidden">
            <div className="absolute inset-0 transform -skew-y-6 origin-bottom-right bg-white dark:bg-gray-900"></div>
          </div>

          {/* Content */}
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-28 md:py-36">
            <div className="text-center space-y-8">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white">
                {filters.jobid ? (jobs.items[0]?.role || "Job Details") : title}
              </h1>
              {filters.jobid && jobs.items[0] && (
                <div className="flex flex-wrap justify-center items-center gap-6 text-white">
                  {/* Location */}
                  <span className="flex items-center bg-white/10 px-4 py-2 rounded-full">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {`${jobs.items[0]?.locality?.city}, ${jobs.items[0]?.locality?.state}`}
                  </span>
                  {/* Job Type */}
                  <span className="flex items-center bg-white/10 px-4 py-2 rounded-full">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    {jobs.items[0]?.job_type}
                  </span>

                  {/* Salary Range */}
                  {jobs.items[0]?.salary_range && (
                    <span className="flex items-center bg-white/10 px-4 py-2 rounded-full">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {jobs.items[0]?.salary_range}
                    </span>
                  )}

                  {/* Experience Level */}
                  {jobs.items[0]?.experience_level && (
                    <span className="flex items-center bg-white/10 px-4 py-2 rounded-full">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      {jobs.items[0]?.experience_level}
                    </span>
                  )}

                  {/* Posted Date */}
                  <span className="flex items-center bg-white/10 px-4 py-2 rounded-full">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {new Date(jobs.items[0]?.createdAt).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </span>
                </div>
              )}

              {/* Notification Button */}
              <div className="mt-8">
                <a
                  href="/club/join"
                  className="inline-flex items-center px-8 py-4 border-2 border-white text-base font-medium rounded-full text-customBlue-light bg-white hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                  </svg>
                  Yes, I want to know when similar jobs get posted
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Job Details Section */}
      <section className="relative py-12 md:py-20 bg-white dark:bg-gray-900 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid gap-6">
            {jobs.items.map((job, index) => (
              <div key={job._id} className="transform transition-all duration-300 hover:scale-[1.02]">
                <JobCard
                  job={{
                    _id: job?._id?.toString(),
                    title: job?.role,
                    company: job?.organization?.name,
                    location: `${job?.locality?.city}, ${job?.locality?.state}`,
                    type: job?.job_type,
                    companyLogo: job?.organization?.logo ? job?.organization?.logo?.url : "https://onlyc2c.com/assets/images/logo.png",
                    description: job?.description,
                    skills: job?.skills?.map(skill => skill.name),
                    postedTime: job?.createdAt,
                    applicants: job?.no_of_applicants,
                    verified: job?.verified,
                    recruiterProfileImage: job.posted_by.user["image"],
                    recruiterEmail: job?.posted_by?.user?.email,
                    recruiterName: job?.posted_by?.user?.name,
                    recruiterId: job?.posted_by?.user?._id?.toString(),
                    jobId: job?.job_id,
                    verified: job?.verified,
                    active: job?.active
                  }}
                  expanded={index === 0}
                />
              </div>
            ))}
          </div>

          {jobs.items.length === 0 && (
            <div className="text-center py-20">
              <div className="max-w-md mx-auto">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No jobs found</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">We couldn't find any {title} jobs matching your criteria.</p>
              </div>
            </div>
          )}
        </div>
      </section>
    </main>
  );
}