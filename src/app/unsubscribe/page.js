"use client"
import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';

export default function UnsubscribePage() {
    const [status, setStatus] = useState('processing');
    const [message, setMessage] = useState('Processing unsubscription...');
    const searchParams = useSearchParams();
    const token = searchParams.get('token');

    useEffect(() => {
        const handleUnsubscribe = async () => {
            try {
                const response = await fetch('/api/unsubscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token }),
                });

                const data = await response.json();

                if (response.ok) {
                    setStatus('success');
                    setMessage(data.message || 'You have been successfully unsubscribed');
                } else {
                    setStatus('error');
                    setMessage(data.error || 'Failed to unsubscribe. Please try again.');
                }
            } catch (error) {
                setStatus('error');
                setMessage('An error occurred. Please try again later.');
            }
        };

        if (token) {
            handleUnsubscribe();
        } else {
            setStatus('error');
            setMessage('Invalid unsubscribe link');
        }
    }, [token]);

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
            <div className="max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg text-center">
                <div className="mx-auto h-24 w-24">
                    {status === 'processing' ? (
                        <svg
                            className="animate-spin h-full w-full text-blue-500"
                            viewBox="0 0 24 24"
                        >
                            <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                                fill="none"
                            />
                            <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            />
                        </svg>
                    ) : status === 'success' ? (
                        <svg
                            className="h-full w-full text-green-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M5 13l4 4L19 7"
                            />
                        </svg>
                    ) : (
                        <svg
                            className="h-full w-full text-red-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M6 18L18 6M6 6l12 12"
                            />
                        </svg>
                    )}
                </div>

                <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                    {status === 'success' ? 'Unsubscribed' : 'Error'}
                </h2>

                <p className="text-gray-600 dark:text-gray-400 text-lg">
                    {message}
                </p>

                {status === 'success' && (
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                        You will no longer receive any emails from our service.
                        <br />
                        <span className="text-blue-500">Changed your mind?</span>{' '}
                        <a
                            href="/subscribe"
                            className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
                        >
                            Resubscribe here
                        </a>
                    </p>
                )}
            </div>
        </div>
    );
}
