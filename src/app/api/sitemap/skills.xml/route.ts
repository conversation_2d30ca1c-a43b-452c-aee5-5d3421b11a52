import { NextRequest, NextResponse } from 'next/server';
import { generateSkillsSitemap } from '@/server/utils/sitemapUtils';

/**
 * Generates the sitemap for skill-based pages
 */
export async function GET(request: NextRequest) {
  // Generate the skills sitemap XML
  const sitemapXml = await generateSkillsSitemap();
  
  // Return the XML with proper content type
  return new NextResponse(sitemapXml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
