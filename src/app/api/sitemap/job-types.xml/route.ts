import { NextRequest, NextResponse } from 'next/server';
import { generateJobTypesSitemap } from '@/server/utils/sitemapUtils';

/**
 * Generates the sitemap for job type pages
 */
export async function GET(request: NextRequest) {
  // Generate the job types sitemap XML
  const sitemapXml = await generateJobTypesSitemap();
  
  // Return the XML with proper content type
  return new NextResponse(sitemapXml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
