import { NextRequest, NextResponse } from 'next/server';
import { generateStaticSitemap } from '@/server/utils/sitemapUtils';

/**
 * Generates the sitemap for static pages
 */
export async function GET(request: NextRequest) {
  // Generate the static pages sitemap XML
  const sitemapXml = await generateStaticSitemap();
  
  // Return the XML with proper content type
  return new NextResponse(sitemapXml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
