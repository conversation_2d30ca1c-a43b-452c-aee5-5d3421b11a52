import { NextRequest, NextResponse } from 'next/server';
import { generateJobsSitemap } from '@/server/utils/sitemapUtils';

/**
 * Generates the sitemap for job listings
 */
export async function GET(request: NextRequest) {
  // Generate the jobs sitemap XML
  const sitemapXml = await generateJobsSitemap();
  
  // Return the XML with proper content type
  return new NextResponse(sitemapXml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
