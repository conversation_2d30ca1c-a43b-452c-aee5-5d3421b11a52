import { NextRequest, NextResponse } from 'next/server';
import { generateLocationsSitemap } from '@/server/utils/sitemapUtils';

/**
 * Generates the sitemap for location-based pages
 */
export async function GET(request: NextRequest) {
  // Generate the locations sitemap XML
  const sitemapXml = await generateLocationsSitemap();
  
  // Return the XML with proper content type
  return new NextResponse(sitemapXml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
