import BaseResponse from "@/server/core/BaseResponse";
import { NextRequest } from "next/server";
import { candidateService, recruiterService, userService } from "@/server/services";
import { QueryUserParams } from "@/server/repositories";
import { ErrorNotFound } from "@/server/core/Errors";
import { HttpStatusCode } from "axios";
import { ICandidate, IRecruiter, IUser } from "@/server/models";
import { generateJwtToken, getTokenFromCookieOrRequest, verifyJwtToken } from "@/server/lib/jwt";
import { cookies } from "next/headers";
import { UserScope } from "@/server/enums";
import NextAuth from "next-auth";
import { GetProfileValidator } from "@/server/validators/profile";
import { authOptions } from "@/server/utils/authOptions";
import { sendWelcomeEmail } from "@/server/utils/emailUtils";

export async function GET(request: NextRequest) {
    const query = Object.fromEntries(request.nextUrl.searchParams);
    const { value, error } = GetProfileValidator.validate(query);
    if (error) {
        return BaseResponse.badRequest(error);
    }

    const { scope } = value;
    try {
        const email = await getEmail(request);
        if (!email) {
            return BaseResponse.unauthorized();
        }

        const users = await userService.getAll({ email } as QueryUserParams);
        if (!users || users.items.length === 0) {
            throw new ErrorNotFound(`User(${email}) not found`);
        }

        const user = users.items[0];
        var profile;
        if (scope === UserScope.CANDIDATE) {
            profile = await fetchOrCreateCandidateProfile(user);
        } else {
            profile = await fetchOrCreateRecruiterProfile(user);
        }

        let generatedToken = await generateToken(profile, scope);
        const response = {
            profile,
            token: generatedToken,
        }
        return BaseResponse.success(HttpStatusCode.Ok, "Profile fetched successfully", response);
    } catch (error) {
        return BaseResponse.error(error);
    }
}

async function getEmail(request: NextRequest): Promise<string | undefined> {
    try {
        const session = await NextAuth(authOptions).auth();
        var email = session?.user?.email;
        if (email) return email;

        const token = await getTokenFromCookieOrRequest(request);
        if (!token) return;

        const payload = await verifyJwtToken(token);
        return payload?.email;
    } catch (error) {
        console.log('getEmail:', error);
    }
}

async function fetchOrCreateCandidateProfile(user: IUser): Promise<ICandidate> {
    const userId = user._id;
    var candidate = await candidateService.getByUserId(userId.toString());
    if (!candidate) {
        const dto = {
            user: userId,
        }
        candidate = await candidateService.create(dto as ICandidate);
        await sendWelcomeEmail(
            user.name || '',
            user.email,
            UserScope.CANDIDATE
        );
    }
    return candidate;
}

async function fetchOrCreateRecruiterProfile(user: IUser): Promise<IRecruiter> {
    const userId = user._id;
    var recruiter = await recruiterService.getByUserId(userId.toString());
    if (!recruiter) {
        const dto = {
            user: userId,
        }
        recruiter = await recruiterService.create(dto as IRecruiter);
        await sendWelcomeEmail(
            user.name || '',
            user.email,
            UserScope.RECRUITER
        );
    }
    return recruiter;
}

async function generateToken(profile: ICandidate | IRecruiter, scope: string): Promise<string> {
    const token = await generateJwtToken(profile, scope);

    // Set token in cookie
    (await cookies()).set("x-c2c-token", token, {
        expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7),
    });

    return token;
}