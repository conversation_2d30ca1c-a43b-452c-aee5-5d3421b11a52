import { authOptions } from "@/server/utils/authOptions";
import NextAuth, { NextAuthConfig } from "next-auth";
import { NextRequest } from "next/server";

export const {
    handlers: { GET, POST },
} = NextAuth(
    async (request?: NextRequest) => handler(request),
);

async function handler(request?: NextRequest): Promise<NextAuthConfig> {
    const config = {
        ...authOptions,
    };

    if (request == null) return config;

    return {
        ...config,
        callbacks: {
            async redirect({ url, baseUrl }) {
                const isAuthCallbackUri = /^\/@profile\/(recruiter|candidate)/.test(url);
                if (isAuthCallbackUri) {
                    return authCallbackHandler(url);
                }
                return url;
            }
        },
    };
}

function authCallbackHandler(url: string) {
    const role = url.match(/(recruiter|candidate)/g)?.[0];
    const redirectUri = url.replace(/^\/@profile\/(recruiter|candidate)/, '');
    let callbackUrl = `/setup-account/${role}`;
    if (redirectUri && redirectUri.length > 0) {
        callbackUrl += `?redirectUri=${encodeURIComponent(redirectUri)}`;
    }
    return callbackUrl;
}