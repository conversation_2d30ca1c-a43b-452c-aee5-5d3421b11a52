import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { recruiterService } from "@/server/services";
import { CreateRecruiterValidator, GetRecruitersValidator } from "@/server/validators/recruiters";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetRecruitersValidator,
    },
    async handler({ query }) {
        const result = await recruiterService.getAll(query);
        return BaseResponse.success(HttpStatusCode.Ok, 'Recruiters fetched successfully', result);
    }
})

export const POST = BaseRequest({
    validators: {
        bodyValidator: CreateRecruiterValidator,
    },
    async handler({ body }) {
        const recruiter = await recruiterService.create(body);
        return BaseResponse.success(HttpStatusCode.Created, 'Recruiter created successfully', recruiter);
    }
})