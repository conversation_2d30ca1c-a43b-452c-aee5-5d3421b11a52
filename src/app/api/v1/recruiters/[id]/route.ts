import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { recruiterService } from "@/server/services";
import { UpdateRecruiterValidator } from "@/server/validators/recruiters";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const recruiter = await recruiterService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Recruiter fetched successfully', recruiter);
    }
})

export const PATCH = BaseRequest<PathParams>({
    validators: {
        bodyValidator: UpdateRecruiterValidator,
    },
    async handler({ params, body }) {
        const { id } = params;
        const recruiter = await recruiterService.update(id, body);
        return BaseResponse.success(HttpStatusCode.Ok, 'Recruiter updated successfully', recruiter);
    }
})

export const DELETE = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const recruiter = await recruiterService.delete(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Recruiter deleted successfully', recruiter);
    }
})