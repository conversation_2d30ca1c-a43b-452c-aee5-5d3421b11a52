import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { localityService } from "@/server/services";
import { BatchCreateLocalitiesValidator } from "@/server/validators/locality";
import { HttpStatusCode } from "axios";

export const POST = BaseRequest({
    validators: {
        bodyValidator: BatchCreateLocalitiesValidator
    },
    async handler({body}) {
        const result = await localityService.batchCreate(body);
        return BaseResponse.success(HttpStatusCode.Created, 'Localities created successfully', result);
    }
});