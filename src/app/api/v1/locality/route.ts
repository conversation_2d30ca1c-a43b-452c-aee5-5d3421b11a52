import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { localityService } from "@/server/services";
import { CreateLocalityValidator, GetLocalityValidator } from "@/server/validators/locality";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetLocalityValidator,
    },
    async handler({ query }) {
        const result = await localityService.getAll(query);
        return BaseResponse.success(HttpStatusCode.Ok, 'Fetched localities successfully', result);
    }
})

export const POST = BaseRequest({
    validators: {
        bodyValidator: CreateLocalityValidator,
    },
    async handler({ body }) {
        const locality = await localityService.create(body);
        return BaseResponse.success(HttpStatusCode.Created, 'Locality created successfully', locality,);
    }
})