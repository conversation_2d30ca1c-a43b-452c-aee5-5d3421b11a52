import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { localityService } from "@/server/services";
import { UpdateLocalityValidator } from "@/server/validators/locality";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const locality = await localityService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Locality fetched successfully', locality);
    }
})

export const PATCH = BaseRequest<PathParams>({
    validators: {
        bodyValidator: UpdateLocalityValidator,
    },
    async handler({ params, body }) {
        const { id } = params;
        const locality = await localityService.update(id, body);
        return BaseResponse.success(HttpStatusCode.Ok, 'Locality updated successfully', locality);
    }
})

export const DELETE = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const locality = await localityService.delete(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Locality deleted successfully', locality);
    }
})