import { CreateOrganizationValidator, GetOrganizationsValidator } from "@/server/validators/organization";
import { HttpStatusCode } from 'axios';
import BaseResponse from "@/server/core/BaseResponse";
import { getUserProfileId } from "@/server/utils/requestUtils";
import { AlreadyExistsError } from "@/server/core/Errors";
import { organizationService, recruiterService } from "@/server/services";
import { BaseRequest } from "@/server/core/BaseRequest";
import { UserScope } from "@/server/enums";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetOrganizationsValidator,
    },
    async handler({ request }) {
        const query = Object.fromEntries(request.nextUrl.searchParams);
        const { value, error } = GetOrganizationsValidator.validate(query);
        if (error) {
            return BaseResponse.badRequest(error);
        }
        const result = await organizationService.getAll(value);
        return BaseResponse.success(HttpStatusCode.Ok, 'Organizations fetched successfully', result);
    }
});

export const POST = BaseRequest({
    allowRoles: [UserScope.RECRUITER],
    validators: {
        bodyValidator: CreateOrganizationValidator,
    },
    async handler({ request, body }) {
        const profileId = getUserProfileId(request);
        let recruiter = await recruiterService.getById(profileId);
        if (!recruiter) {
            return BaseResponse.unauthorized();
        }

        if (recruiter.organization) {
            throw new AlreadyExistsError('Recruiter is already part of an organization');
        }

        body.created_by = profileId;
        const organization = await organizationService.create(body);

        recruiter.organization = organization._id;
        await recruiterService.update(profileId, recruiter);
        return BaseResponse.success(HttpStatusCode.Created, 'Organization created successfully', organization);
    }
})