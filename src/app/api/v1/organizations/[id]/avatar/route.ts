import BaseResponse from "@/server/core/BaseResponse";
import { HttpStatusCode } from "axios";
import { organizationService } from '@/server/services';
import { deleteFile, IFileRequest, randomFileSuffix, uploadFile } from "@/server/lib/gcs";
import { IOrganization } from "@/server/models";
import { BaseRequest } from "@/server/core/BaseRequest";
import { UserScope } from "@/server/enums";

type PathParams = { id: string; }

export const PUT = BaseRequest<PathParams>({
    allowRoles: UserScope.RECRUITER,
    async handler({ request, params }) {
        const { id } = params;
        const formData = await request.formData();
        const file = formData.get('file') as File;
        if (!file) {
            return BaseResponse.badRequest(new Error('File not found'));
        }

        const organization = await organizationService.getById(id);
        if (organization.logo) {
            await deleteFile(organization.logo.key);
        }

        const folderPath = `organization/${id}`;
        const fileName = `ORGANIZATION_AVATAR_${id}_${randomFileSuffix()}`;

        const fileRequest: IFileRequest = {
            file: file,
            fileName: fileName,
            folderPath: folderPath,
        };

        const fileResponse = await uploadFile(fileRequest);
        const dto = {
            logo: fileResponse
        } as IOrganization;

        const updatedOrganization = await organizationService.update(id, dto);
        return BaseResponse.success(HttpStatusCode.Ok, 'Job avatar updated successfully', updatedOrganization);
    }
})