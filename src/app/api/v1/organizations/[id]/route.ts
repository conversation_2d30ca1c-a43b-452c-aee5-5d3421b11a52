import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { organizationService } from "@/server/services";
import { UpdateOrganizationValidator } from "@/server/validators/organization";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const organization = await organizationService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Organization fetched successfully', organization);
    },
});

export const PATCH = BaseRequest<PathParams>({
    allowRoles: [UserScope.RECRUITER],
    validators: {
        bodyValidator: UpdateOrganizationValidator,
    },
    async handler({ params, body }) {
        const { id } = params;
        const organization = await organizationService.update(id, body);
        return BaseResponse.success(HttpStatusCode.Ok, 'Organization updated successfully', organization);
    }
});

export const DELETE = BaseRequest<PathParams>({
    allowRoles: [UserScope.RECRUITER],
    async handler({ params }) {
        const { id } = params;
        const organization = await organizationService.delete(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Organization deleted successfully', organization);
    }
});