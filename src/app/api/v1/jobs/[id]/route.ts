import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { jobService } from "@/server/services";
import { UpdateJobValidator } from "@/server/validators/jobs";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const result = await jobService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Job fetched successfully', result);
    }
})

export const PATCH = BaseRequest<PathParams>({
    allowRoles: UserScope.RECRUITER,
    validators: {
        bodyValidator: UpdateJobValidator,
    },
    async handler({ params, body }) {
        const { id } = params;
        const result = await jobService.update(id, body);
        return BaseResponse.success(HttpStatusCode.Ok, 'Job updated successfully', result);
    }
})

export const DELETE = BaseRequest<PathParams>({
    allowRoles: UserScope.RECRUITER,
    async handler({ params }) {
        const { id } = params;
        const result = await jobService.delete(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Job deleted successfully', result);
    }
})