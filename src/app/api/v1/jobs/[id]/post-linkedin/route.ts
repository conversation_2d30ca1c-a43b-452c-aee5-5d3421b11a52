import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { linkedInService } from "@/server/services";
import { getUserId } from "@/server/utils/requestUtils";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const POST = BaseRequest<PathParams>({
    allowRoles: [UserScope.RECRUITER],
    async handler({ request, params }) {
        const { id } = params;
        const userId = getUserId(request);

        if (!userId) {
            return BaseResponse.unauthorized("User ID is missing");
        }

        // Check if the request has form data
        const contentType = request.headers.get("content-type") || "";
        let imageUrl: string | undefined;

        if (!contentType.includes("multipart/form-data")) {
            return BaseResponse.badRequest(
                new Error("Invalid request format. Expected multipart/form-data."),
            );
        }

        const formData = await request.formData();
        const content = formData.get("content") as string;

        if (!content) {
            return BaseResponse.badRequest(
                new Error("Content is required"),
            );
        }

        try {
            // Handle form data with possible image
            const imageFile = formData.get("image") as File;

            if (imageFile) {
                // If image is provided as a file, we need to upload it first
                // For simplicity, we'll use the existing GCS upload functionality
                const { uploadFile, randomFileSuffix } = await import("@/server/lib/gcs");

                const fileRequest = {
                    file: imageFile,
                    fileName: `LINKEDIN_POST_${id}_${randomFileSuffix()}`,
                    folderPath: `jobs/${id}/linkedin-posts`,
                };

                const fileResponse = await uploadFile(fileRequest);
                imageUrl = fileResponse.url;
                console.log("Image uploaded to GCS:", imageUrl);
            }
        } catch (formError) {
            console.error("Error processing form data:", formError);
            return BaseResponse.badRequest(new Error("Error processing image upload. Please try again."));
        }

        // Post to LinkedIn
        const options = { imageUrl, content };
        const result = await linkedInService.postJobToLinkedIn(id, userId, options);

        return BaseResponse.success(
            HttpStatusCode.Ok,
            "Job posted to LinkedIn successfully",
            result
        );
    },
});
