import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { jobService } from "@/server/services";
import { SimilarJobsValidator } from "@/server/validators/jobs";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    validators: {
        queryValidator: SimilarJobsValidator,
    },
    async handler({ params, query }) {
        const { id } = params;

        const result = await jobService.getSimilarJobs(id, query);

        if (result.total === 0) {
            return BaseResponse.success(
                HttpStatusCode.Ok,
                'No similar jobs found',
                result
            );
        }

        return BaseResponse.success(
            HttpStatusCode.Ok,
            'Similar jobs fetched successfully',
            result
        );
    }
});
