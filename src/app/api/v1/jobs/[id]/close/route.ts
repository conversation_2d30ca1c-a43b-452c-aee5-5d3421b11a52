import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { jobService } from "@/server/services";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const PATCH = BaseRequest<PathParams>({
    allowRoles: UserScope.RECRUITER,
    async handler({ params }) {
        const { id } = params;
        const result = await jobService.closeJob(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Job closed successfully', result);
    }
})
