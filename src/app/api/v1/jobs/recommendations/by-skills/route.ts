import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { jobService } from "@/server/services";
import { SkillBasedRecommendationsValidator } from "@/server/validators/jobs";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    validators: {
        queryValidator: SkillBasedRecommendationsValidator,
    },
    async handler({ query }) {
        try {
            const result = await jobService.getRecommendationsBySkills(query);
            
            if (result.total === 0) {
                return BaseResponse.success(
                    HttpStatusCode.Ok, 
                    'No job recommendations found for the provided skills', 
                    result
                );
            }
            
            return BaseResponse.success(
                HttpStatusCode.Ok, 
                'Job recommendations fetched successfully', 
                result
            );
        } catch (error) {
            console.error('Error fetching skill-based recommendations:', error);
            throw error;
        }
    }
});
