import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { jobService } from "@/server/services";
import { CandidateRecommendationsValidator } from "@/server/validators/jobs";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    validators: {
        queryValidator: CandidateRecommendationsValidator,
    },
    async handler({ params, query }) {
        const { id: candidateId } = params;
        
        try {
            const result = await jobService.getRecommendationsForCandidate(candidateId, query);
            
            if (result.total === 0) {
                return BaseResponse.success(
                    HttpStatusCode.Ok, 
                    'No job recommendations found for this candidate', 
                    result
                );
            }
            
            return BaseResponse.success(
                HttpStatusCode.Ok, 
                'Job recommendations fetched successfully', 
                result
            );
        } catch (error) {
            if (error instanceof Error && error.message === 'Candidate not found') {
                return BaseResponse.error('Candidate not found', HttpStatusCode.NotFound);
            }
            console.error('Error fetching candidate recommendations:', error);
            throw error;
        }
    }
});
