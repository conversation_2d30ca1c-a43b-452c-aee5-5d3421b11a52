import { HttpStatusCode } from "axios";
import { C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GetJobsValidator } from "@/server/validators/jobs";
import BaseResponse from "@/server/core/BaseResponse";
import { jobService } from "@/server/services";
import { BaseRequest } from "@/server/core/BaseRequest";
import { UserScope } from "@/server/enums";
import { getUserProfileId } from "@/server/utils/requestUtils";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetJobsValidator,
    },
    async handler({ query }) {
        if (query.skills) {
            query.skills = query.skills.split(',');
        }
        const result = await jobService.getAll(query);
        return BaseResponse.success(HttpStatusCode.Ok, 'Jobs fetched successfully', result);
    }
})

export const POST = BaseRequest({
    allowRoles: UserScope.RECRUITER,
    validators: {
        bodyValidator: CreateJobValidator,
    },
    async handler({ request, body }) {
        const profileId = getUserProfileId(request);
        body.posted_by = profileId;

        const result = await jobService.create(body);
        return BaseResponse.success(HttpStatusCode.Created, 'Job created successfully', result);
    }
})