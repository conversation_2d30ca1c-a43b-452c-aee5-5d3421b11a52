import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { jobService } from "@/server/services";
import { SearchJobValidator } from "@/server/validators/jobs";
import { HttpStatusCode } from "axios";

export const POST = BaseRequest({
    validators: {
        bodyValidator: SearchJobValidator,
    },
    async handler({ body }) {
        const jobs = await jobService.search(body);
        if (!jobs) {
            return BaseResponse.error('No jobs found');
        }
        return BaseResponse.success(HttpStatusCode.Ok, 'Job search successfull', jobs)
    }
})