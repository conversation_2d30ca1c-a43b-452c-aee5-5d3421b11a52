import { HttpStatusCode } from "axios";
import { CreateExternalJobValidator, GetExternalJobsValidator } from "@/server/validators/external-jobs";
import BaseResponse from "@/server/core/BaseResponse";
import { externalJobService } from "@/server/services";
import { BaseRequest } from "@/server/core/BaseRequest";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetExternalJobsValidator,
    },
    async handler({ query }) {
        const result = await externalJobService.getAll(query);
        return BaseResponse.success(HttpStatusCode.Ok, 'External jobs fetched successfully', result);
    }
})

export const POST = BaseRequest({
    validators: {
        bodyValidator: CreateExternalJobValidator,
    },
    async handler({ body }) {
        const result = await externalJobService.create(body);
        return BaseResponse.success(HttpStatusCode.Created, 'External job created successfully', result);
    }
})
