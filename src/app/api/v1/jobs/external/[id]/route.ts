import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { externalJobService } from "@/server/services";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const result = await externalJobService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'External job fetched successfully', result);
    }
})
