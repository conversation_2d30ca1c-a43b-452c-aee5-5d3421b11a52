import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { userService } from "@/server/services";
import { UpdateUserValidator } from "@/server/validators/users";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const user = await userService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'User fetched successfully', user);
    },
})

export const PATCH = BaseRequest<PathParams>({
    validators: {
        bodyValidator: UpdateUserValidator,
    },
    async handler({ params, body }) {
        const { id } = await params;
        const user = await userService.update(id, body);
        return BaseResponse.success(HttpStatusCode.Ok, 'User updated successfully', user);
    },
})

export const DELETE = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const user = await userService.delete(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'User deleted successfully', user);
    },
})