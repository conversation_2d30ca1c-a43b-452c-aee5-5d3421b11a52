import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { userService } from "@/server/services";
import { CreateUserValidator, GetUsersValidator } from "@/server/validators/users";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetUsersValidator,
    },
    async handler({ query }) {
        const result = await userService.getAll(query);
        return BaseResponse.success(HttpStatusCode.Ok, 'Users fetched successfully', result);
    }
})

export const POST = BaseRequest({
    validators: {
        bodyValidator: CreateUserValidator,
    },
    async handler({ body }) {
        const user = await userService.create(body);
        return BaseResponse.success(HttpStatusCode.Created, 'User created successfully', user);
    }
})