import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { candidateService } from "@/server/services";
import { UpdateCandidateValidator } from "@/server/validators/candidate";
import { HttpStatusCode } from "axios";

type PathParams = { id: string; }

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const candidate = await candidateService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Candidate fetched successfully', candidate);
    }
})

export const PATCH = BaseRequest<PathParams>({
    validators: {
        bodyValidator: UpdateCandidateValidator,
    },
    async handler({ params, body }) {
        const { id } = params;
        try {
            const candidate = await candidateService.update(id, body);
            return BaseResponse.success(HttpStatusCode.Ok, 'Candidate updated successfully', candidate);
        } catch (error) {
            return BaseResponse.error(error);
        }
    }
})

export const DELETE = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const candidate = await candidateService.delete(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Candidate deleted successfully', candidate);
    }
})