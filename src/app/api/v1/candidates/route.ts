import BaseResponse from "@/server/core/BaseResponse";
import { CreateCandidateValidator, GetCandidatesValidator } from "@/server/validators/candidate";
import { HttpStatusCode } from 'axios';
import { candidateService } from "@/server/services";
import { BaseRequest } from "@/server/core/BaseRequest";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetCandidatesValidator,
    },
    async handler({ query }) {
        const result = await candidateService.getAll(query);
        return BaseResponse.success(HttpStatusCode.Ok, 'Candidates fetched successfully', result);
    }
})

export const POST = BaseRequest({
    validators: {
        bodyValidator: CreateCandidateValidator
    },
    async handler({ body }) {
        const candidate = await candidateService.create(body);
        return BaseResponse.success(HttpStatusCode.Created, 'Candidate created successfully', candidate);
    }
})