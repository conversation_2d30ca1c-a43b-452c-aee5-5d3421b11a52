import { GenerateJobDescription } from "@/server/ai";
import BaseResponse from "@/server/core/BaseResponse";
import { NextRequest } from "next/server";

/**
 * Test endpoint for the enhanced job description generator
 * This endpoint is for testing purposes only and should be removed in production
 */
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        
        // Use provided hint or one of the test hints
        let hint = body.hint;
        
        if (!hint) {
            // Sample test hints
            const testHints = [
                "Looking for a skilled React developer with 5+ years of experience for a remote position. Must know TypeScript, Redux, and have experience with Next.js. Salary range $120-150k.",
                "Need a Java Developer in New York City. Hybrid role, 3 days in office. Skills: Spring Boot, Microservices, AWS, Docker. 8+ years experience required. Competitive salary based on experience.",
                "Senior Python Engineer needed for our Chicago office. On-site position. Must have experience with Django, Flask, and data science libraries. Machine learning experience a plus. $140-160k DOE.",
                "Remote DevOps Engineer position available. Skills needed: Kubernetes, Terraform, AWS, CI/CD pipelines. Minimum 4 years of experience. $130-150k annual salary."
            ];
            
            // Use the hint from the request or a random test hint
            const hintIndex = body.hintIndex || Math.floor(Math.random() * testHints.length);
            hint = testHints[hintIndex];
        }
        
        console.log(`[TEST] Processing job description hint: ${hint}`);
        
        const stream = await GenerateJobDescription({ hint });
        console.log(`[TEST] Stream created successfully, returning to client`);
        return BaseResponse.stream(stream);
    } catch (error: any) {
        console.error("[TEST] Error in test endpoint:", error);
        return BaseResponse.error(error);
    }
}

export async function GET(request: NextRequest) {
    // For SSE connections
    const url = new URL(request.url);
    const hint = url.searchParams.get('hint') || "Looking for a skilled React developer with 5+ years of experience for a remote position. Must know TypeScript, Redux, and have experience with Next.js. Salary range $120-150k.";
    
    console.log(`[TEST-GET] Processing job description hint: ${hint}`);
    
    try {
        const stream = await GenerateJobDescription({ hint });
        console.log(`[TEST-GET] Stream created successfully, returning to client`);
        return BaseResponse.stream(stream);
    } catch (error: any) {
        console.error("[TEST-GET] Error in test endpoint:", error);
        return BaseResponse.error(error);
    }
}
