import { GenerateJobDescription } from "@/server/ai";
import BaseResponse from "@/server/core/BaseResponse";
import { GenerateJobDescriptionValidator } from "@/server/validators/gen-ai";
import { NextRequest } from "next/server";

export async function POST(request: NextRequest) {
    const body = await request.json();

    const { value, error } = GenerateJobDescriptionValidator.validate(body);
    if (error) {
        return BaseResponse.badRequest(error);
    }

    try {
        const stream = await GenerateJobDescription(value);
        return BaseResponse.stream(stream);
    } catch (error) {
        return BaseResponse.error(error);
    }
}