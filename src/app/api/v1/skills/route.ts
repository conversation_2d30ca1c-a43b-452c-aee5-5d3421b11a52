import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { skillService } from "@/server/services";
import { CreateSkillValidator, GetSkillsValidator } from "@/server/validators/skills";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetSkillsValidator,
    },
    async handler({ query }) {
        const result = await skillService.getAll(query);
        return BaseResponse.success(HttpStatusCode.Ok, 'Skills fetched successfully', result);
    }
})

export const POST = BaseRequest({
    validators: {
        bodyValidator: CreateSkillValidator
    },
    async handler({ body }) {
        const skill = await skillService.create(body);
        return BaseResponse.success(HttpStatusCode.Created, 'Skill created successfully', skill);
    }
})