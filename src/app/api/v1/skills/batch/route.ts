import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { skillService } from "@/server/services";
import { BatchCreateSkillsValidator } from "@/server/validators/skills";
import { HttpStatusCode } from "axios";

export const POST = BaseRequest({
    validators: {
        bodyValidator: BatchCreateSkillsValidator
    },
    async handler({body}) {
        const result = await skillService.batchCreate(body);
        return BaseResponse.success(HttpStatusCode.Created, 'Skills created successfully', result);
    }
});