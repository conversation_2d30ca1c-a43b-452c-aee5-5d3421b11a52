import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { skillService } from "@/server/services";
import { UpdateSkillValidator } from "@/server/validators/skills";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const skill = await skillService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Skills fetched successfully', skill);
    }
})

export const PATCH = BaseRequest<PathParams>({
    validators: {
        bodyValidator: UpdateSkillValidator,
    },
    async handler({ params, body }) {
        const { id } = params;
        const skill = await skillService.update(id, body);
        return BaseResponse.success(HttpStatusCode.Ok, 'Skill updated successfully', skill);
    }
})

export const DELETE = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const skill = await skillService.delete(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Skills deleted successfully', skill);
    }
})