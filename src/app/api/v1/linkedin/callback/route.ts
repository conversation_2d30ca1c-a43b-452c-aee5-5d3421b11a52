import { Account } from "@/server/models";
import { NextRequest, NextResponse } from "next/server";
import { Types } from "mongoose";
import connectToDb from "@/server/lib/db";

// LinkedIn OAuth configuration - using existing environment variables
const LINKEDIN_CLIENT_ID = process.env.LINKEDIN_CLIENT_ID;
const LINKEDIN_CLIENT_SECRET = process.env.LINKEDIN_CLIENT_SECRET;
const LINKEDIN_REDIRECT_URI = `${process.env.NEXTAUTH_URL}/api/v1/linkedin/callback`;

// Helper function to create redirect URL with error/success parameters
function createRedirectUrl(returnUrl: string | null, params: string): string {
    const redirectUrl = returnUrl || '/dashboard/settings';
    const baseUrl = process.env.NEXTAUTH_URL;

    if (redirectUrl.startsWith('http')) {
        return `${redirectUrl}${redirectUrl.includes('?') ? '&' : '?'}${params}`;
    } else {
        return `${baseUrl}${redirectUrl}${redirectUrl.includes('?') ? '&' : '?'}${params}`;
    }
}

export async function GET(request: NextRequest) {
    try {
        const url = new URL(request.url);
        const code = url.searchParams.get('code');
        const state = url.searchParams.get('state');
        const error = url.searchParams.get('error');
        const errorDescription = url.searchParams.get('error_description');

        // Check for errors from LinkedIn
        if (error) {
            console.error(`LinkedIn OAuth error: ${error} - ${errorDescription}`);
            return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard/settings?error=linkedin_auth_failed`);
        }

        // Decode the state parameter to get userId and optional return URL
        let userId;
        let returnUrl;
        try {
            if (!state) {
                throw new Error("State parameter is missing");
            }

            const decodedState = JSON.parse(Buffer.from(state, 'base64').toString());

            // Handle the simplified state object format
            userId = decodedState.u || decodedState.userId;
            returnUrl = decodedState.returnUrl;
        } catch (error) {
            console.error("Error decoding state parameter:", error);
            return NextResponse.redirect(createRedirectUrl(null, 'error=invalid_state'));
        }

        if (!userId) {
            console.error('User ID not found in state parameter');
            return NextResponse.redirect(createRedirectUrl(null, 'error=user_not_found'));
        }

        // Exchange code for access token
        const tokenResponse = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                grant_type: 'authorization_code',
                code: code as string,
                redirect_uri: LINKEDIN_REDIRECT_URI,
                client_id: LINKEDIN_CLIENT_ID as string,
                client_secret: LINKEDIN_CLIENT_SECRET as string,
            }),
        });

        if (!tokenResponse.ok) {
            const errorData = await tokenResponse.text();
            console.error('Error exchanging code for token:', errorData);
            return NextResponse.redirect(createRedirectUrl(returnUrl, 'error=token_exchange_failed'));
        }

        const tokenData = await tokenResponse.json();

        // Get LinkedIn profile information using the latest API endpoints
        const profileResponse = await fetch('https://api.linkedin.com/v2/userinfo', {
            headers: {
                Authorization: `Bearer ${tokenData.access_token}`,
            },
        });

        if (!profileResponse.ok) {
            console.error('Error fetching LinkedIn profile');
            return NextResponse.redirect(createRedirectUrl(returnUrl, 'error=profile_fetch_failed'));
        }

        const profileData = await profileResponse.json();

        // Connect to database
        await connectToDb();

        // Use personal profile scopes only
        const scope = 'openid profile email w_member_social';

        // Save or update account in database
        // Use Types.ObjectId.createFromHexString to avoid deprecated constructor
        const userIdObj = Types.ObjectId.createFromHexString(userId);
        const existingAccount = await Account.findOne({
            provider: 'linkedin',
            userId: userIdObj,
        });

        if (existingAccount) {
            // Update existing account with refreshed token and profile information
            existingAccount.access_token = tokenData.access_token;
            existingAccount.expires_at = Math.floor(Date.now() / 1000) + tokenData.expires_in;
            existingAccount.scope = scope;
            existingAccount.token_type = tokenData.token_type;

            // Update profile information with data from userinfo endpoint
            existingAccount.profile = {
                name: profileData.name || '',
                email: profileData.email || existingAccount.profile?.email || null,
                linkedinId: profileData.sub,
                accountType: 'personal'
            };

            if (tokenData.refresh_token) {
                existingAccount.refresh_token = tokenData.refresh_token;
            }

            await existingAccount.save();
        } else {
            // Create new account with additional profile information
            const newAccount = new Account({
                userId: userIdObj, // Use the already created ObjectId
                type: 'oauth',
                provider: 'linkedin',
                providerAccountId: profileData.sub,
                access_token: tokenData.access_token,
                expires_at: Math.floor(Date.now() / 1000) + tokenData.expires_in,
                token_type: tokenData.token_type,
                scope: scope,
                refresh_token: tokenData.refresh_token || null,
                profile: {
                    name: profileData.name || '',
                    email: profileData.email || null,
                    linkedinId: profileData.sub,
                    accountType: 'personal'
                }
            });

            await newAccount.save();
        }

        // Redirect back to the specified return URL or default to settings page with success message
        return NextResponse.redirect(createRedirectUrl(returnUrl, 'success=linkedin_connected'));
    } catch (error) {
        console.error('Error in LinkedIn callback:', error);
        return NextResponse.redirect(createRedirectUrl(null, 'error=unexpected_error'));
    }
}
