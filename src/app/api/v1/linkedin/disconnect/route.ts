import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { Account } from "@/server/models";
import { getUserId } from "@/server/utils/requestUtils";
import { HttpStatusCode } from "axios";
import connectToDb from "@/server/lib/db";
import { Types } from "mongoose";

export const DELETE = BaseRequest({
    allowRoles: [UserScope.RECRUITER],
    async handler({ request }) {
        try {
            await connectToDb();
            const userId = getUserId(request);
            
            // Find and delete LinkedIn account for this user
            const result = await Account.deleteOne({
                userId: new Types.ObjectId(userId),
                provider: 'linkedin',
            });
            
            if (result.deletedCount === 0) {
                return BaseResponse.success(
                    HttpStatusCode.NotFound, 
                    'No LinkedIn account found to disconnect',
                    { success: false }
                );
            }
            
            return BaseResponse.success(
                HttpStatusCode.Ok, 
                'LinkedIn account disconnected successfully',
                { success: true }
            );
        } catch (error) {
            console.error('Error disconnecting LinkedIn account:', error);
            return BaseResponse.error(error as Error);
        }
    }
});
