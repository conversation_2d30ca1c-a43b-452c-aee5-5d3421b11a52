import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { getUserId } from "@/server/utils/requestUtils";
import { HttpStatusCode } from "axios";
import { randomBytes } from "crypto";

// LinkedIn OAuth configuration - using existing environment variables
const LINKEDIN_CLIENT_ID = process.env.LINKEDIN_CLIENT_ID;
const LINKEDIN_REDIRECT_URI = `${process.env.NEXTAUTH_URL}/api/v1/linkedin/callback`;

// Define LinkedIn scopes for personal profile
const LINKEDIN_SCOPES = [
    'openid',
    'profile',
    'email',
    'w_member_social'
];

export const GET = BaseRequest({
    allowRoles: [UserScope.RECRUITER],
    async handler({ request }) {
        try {
            const userId = getUserId(request);

            if (!userId) {
                console.error("User ID is missing in the request");
                return BaseResponse.unauthorized("User ID is missing");
            }

            // Get optional return URL from query parameters
            const url = new URL(request.url);
            const returnUrl = url.searchParams.get('returnUrl');

            // Generate state parameter for security
            const state = randomBytes(16).toString('hex');

            // Use LinkedIn scopes for personal profile only
            const scopes = LINKEDIN_SCOPES;

            // Check if client ID is available
            if (!LINKEDIN_CLIENT_ID) {
                console.error("LinkedIn client ID is missing");
                return BaseResponse.error(new Error("LinkedIn client ID is missing"));
            }

            // Create a simpler state object to avoid tracking issues
            const stateObj = {
                r: state.substring(0, 8), // Random part (shortened)
                u: userId,                 // User ID
                ...(returnUrl && { returnUrl }) // Include return URL if provided
            };
            const encodedState = Buffer.from(JSON.stringify(stateObj)).toString('base64');

            // Build the authorization URL according to LinkedIn documentation
            const scopeString = scopes.join(' ');
            const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${LINKEDIN_CLIENT_ID}&redirect_uri=${encodeURIComponent(LINKEDIN_REDIRECT_URI)}&state=${encodedState}&scope=${encodeURIComponent(scopeString)}`;

            return BaseResponse.success(HttpStatusCode.Ok, 'LinkedIn authorization URL', {
                authUrl: authUrl,
            });
        } catch (error) {
            console.error('Error generating LinkedIn auth URL:', error);
            return BaseResponse.error(error as Error);
        }
    }
});
