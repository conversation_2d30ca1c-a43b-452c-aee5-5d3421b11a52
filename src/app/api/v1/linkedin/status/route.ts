import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { Account } from "@/server/models";
import { getUserId } from "@/server/utils/requestUtils";
import { HttpStatusCode } from "axios";
import connectToDb from "@/server/lib/db";
import { Types } from "mongoose";

export const GET = BaseRequest({
    allowRoles: [UserScope.RECRUITER],
    async handler({ request }) {
        try {
            const userId = getUserId(request);
            if (!userId) {
                console.error("User ID is missing in the request");
                return BaseResponse.unauthorized("User ID is missing");
            }

            // Convert userId to ObjectId if it's a string
            const userIdObj = typeof userId === 'string' ? Types.ObjectId.createFromHexString(userId) : userId;

            await connectToDb();

            // Find LinkedIn account for this user
            const linkedInAccount = await Account.findOne({
                userId: userIdObj,
                provider: 'linkedin',
            });

            if (!linkedInAccount) {
                return BaseResponse.success(HttpStatusCode.Ok, 'LinkedIn status', {
                    isConnected: false,
                });
            }

            // Check if token is still valid
            const currentTime = Math.floor(Date.now() / 1000);
            const isExpired = linkedInAccount.expires_at &&
                linkedInAccount.expires_at < currentTime;

            // Get scope information and check for required scope
            const scope = linkedInAccount.scope || '';
            const hasRequiredScope = scope.includes('w_member_social');

            if (!hasRequiredScope) {
                return BaseResponse.success(HttpStatusCode.Ok, 'LinkedIn status', {
                    isConnected: false,
                    missingScope: true,
                    requiredScope: 'w_member_social'
                });
            }

            // Include profile information in the response
            return BaseResponse.success(HttpStatusCode.Ok, 'LinkedIn status', {
                isConnected: !isExpired,
                accountType: 'personal', // Always personal since we removed organization support
                scope,
                profile: linkedInAccount.profile || null,
                expiresAt: linkedInAccount.expires_at,
            });
        } catch (error) {
            console.error('Error checking LinkedIn status:', error);
            return BaseResponse.error(error as Error);
        }
    }
});
