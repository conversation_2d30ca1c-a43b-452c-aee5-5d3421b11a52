import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { QueryResumeParams } from "@/server/repositories";
import { resumeService } from "@/server/services";
import { getUserProfileId } from "@/server/utils/requestUtils";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    allowRoles: UserScope.CANDIDATE,
    async handler({ request }) {
        const userId = getUserProfileId(request);
        const resumes = await resumeService.getAll({ user: userId } as QueryResumeParams);
        return BaseResponse.success(HttpStatusCode.Ok, "Resumes fetched successfully", resumes);
    },
})