import { BaseRequest } from "@/server/core/BaseRequest"
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { IFileRequest, randomFileSuffix, uploadFile } from "@/server/lib/gcs";
import { IResume } from "@/server/models";
import { resumeService } from "@/server/services";
import { getUserProfileId } from "@/server/utils/requestUtils";
import { HttpStatusCode } from "axios";
import { Types } from "mongoose";

type PathParams = { id: string }

export const PATCH = BaseRequest<PathParams>({
    allowRoles: UserScope.CANDIDATE,
    async handler({ request, params }) {
        const { id } = params;
        const formData = await request.formData();

        const file = formData.get('file') as File;
        const label = formData.get('label') as string;

        if (!file && !label) {
            return BaseResponse.badRequest(
                new Error('No data found to update'),
            );
        }

        if (file && file.type !== 'application/pdf') {
            return BaseResponse.badRequest(
                new Error('Invalid file type. Only PDF files are allowed'),
            );
        }

        const dto = {} as IResume;
        if (label && label.trim() === '') {
            return BaseResponse.badRequest(new Error('Label is required'));
        }
        dto.label = label;

        const profileId = getUserProfileId(request);

        if (file) {
            const folderPath = `candidates/${profileId}/resumes`;
            const fileName = `RESUME_${profileId}_${randomFileSuffix()}`;

            const fileRequest: IFileRequest = {
                file: file,
                fileName: fileName,
                folderPath: folderPath,
            };

            const fileResponse = await uploadFile(fileRequest);
            dto.file = fileResponse;
        }

        dto.user = new Types.ObjectId(profileId);

        const resume = await resumeService.update(id, dto as IResume);
        return BaseResponse.success(HttpStatusCode.Ok, 'Resume updated successfully', resume);
    }
})

export const DELETE = BaseRequest<PathParams>({
    allowRoles: UserScope.CANDIDATE,
    async handler({ params }) {
        const { id } = params;
        const resume = await resumeService.delete(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Resume deleted successfully', resume);
    }
})