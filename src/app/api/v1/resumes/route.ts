import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { IFileRequest, randomFileSuffix, uploadFile } from "@/server/lib/gcs";
import { IResume } from "@/server/models";
import { resumeService } from "@/server/services";
import { getUserProfileId } from "@/server/utils/requestUtils";
import { HttpStatusCode } from "axios";
import { Types } from "mongoose";

export const POST = BaseRequest({
    allowRoles: UserScope.CANDIDATE,
    async handler({ request }) {
        const formData = await request.formData();
        const file = formData.get('file') as File;
        if (!file) {
            return BaseResponse.badRequest(new Error('File not found'));
        }

        //  Disabled temporarily
        // if (file.type !== 'application/pdf') {
        //     return BaseResponse.badRequest(
        //         new Error('Invalid file type. Only PDF files are allowed'),
        //     );
        // }

        const label = formData.get('label') as string;
        if (!label || label.trim() === '') {
            return BaseResponse.badRequest(new Error('Label is required'));
        }

        const profileId = getUserProfileId(request);
        const folderPath = `candidates/${profileId}/resumes`;
        const fileName = `RESUME_${profileId}_${randomFileSuffix()}`;

        const fileRequest: IFileRequest = {
            file: file,
            fileName: fileName,
            folderPath: folderPath,
        };

        const fileResponse = await uploadFile(fileRequest);
        const dto = {
            user: new Types.ObjectId(profileId),
            file: fileResponse,
            label: label,
        };

        const resume = await resumeService.create(dto as IResume);
        return BaseResponse.success(HttpStatusCode.Created, 'Resume uploaded successfully', resume);
    }
});