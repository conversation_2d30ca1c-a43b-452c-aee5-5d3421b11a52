import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { jobApplicationService } from "@/server/services";
import { UpdateStatusValidator } from "@/server/validators/job-applications";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const PATCH = BaseRequest<PathParams>({
    allowRoles: [UserScope.RECRUITER],
    validators: {
        bodyValidator: UpdateStatusValidator,
    },
    async handler({ params, body }) {
        const { id } = params;
        const { status } = body;

        const updatedApplication = await jobApplicationService.updateStatus(id, status);

        return BaseResponse.success(
            HttpStatusCode.Ok,
            `Job application status updated to ${status}`,
            updatedApplication
        );
    }
});
