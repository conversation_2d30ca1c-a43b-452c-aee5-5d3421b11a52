import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { jobApplicationService } from "@/server/services";
import { HttpStatusCode } from "axios";

type PathParams = { id: string }

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const jobApplication = await jobApplicationService.getById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Job application fetched successfully', jobApplication);
    }
});
