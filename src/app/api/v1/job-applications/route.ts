import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { IJobApplication } from "@/server/models";
import { jobApplicationService } from "@/server/services";
import { getUserProfileId } from "@/server/utils/requestUtils";
import { CreateJobApplicationValidator, GetJobApplicationsValidator } from "@/server/validators/job-applications";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    validators: {
        queryValidator: GetJobApplicationsValidator,
    },
    async handler({ query }) {
        const result = await jobApplicationService.getAll(query);
        return BaseResponse.success(HttpStatusCode.Ok, 'Job applications fetched successfully', result);
    }
});

export const POST = BaseRequest({
    allowRoles: UserScope.CANDIDATE,
    validators: {
        bodyValidator: CreateJobApplicationValidator,
    },
    async handler({ request, body }) {
        const profileId = getUserProfileId(request);
        body = { 
            ...body,
            applicant: profileId,
        } as IJobApplication;
        
        const jobApplication = await jobApplicationService.create(body);
        return BaseResponse.success(HttpStatusCode.Created, 'Job application created successfully', jobApplication);
    }
});