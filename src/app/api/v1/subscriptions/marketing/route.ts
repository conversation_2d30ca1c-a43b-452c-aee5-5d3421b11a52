import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { subscriptionService } from "@/server/services";
import { getUserEmail } from "@/server/utils/requestUtils";
import { CreateSubscriptionValidator } from "@/server/validators/subscriptions";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    allowRoles: [UserScope.CANDIDATE, UserScope.RECRUITER],
    async handler({ request }) {
        try {
            // Get the current user's email from the request headers
            const email = getUserEmail(request);

            // Find the subscription by email
            const subscription = await subscriptionService.findByEmail(email);

            if (!subscription) {
                return BaseResponse.success(
                    HttpStatusCode.NotFound,
                    'No subscription found for this user',
                    null
                );
            }

            return BaseResponse.success(
                HttpStatusCode.Ok,
                'Subscription fetched successfully',
                subscription
            );
        } catch (error) {
            console.error("Error getting email from request:", error);
            return BaseResponse.badRequest(new Error('Unable to identify user'));
        }
    }
});

export const POST = BaseRequest({
    validators: {
        bodyValidator: CreateSubscriptionValidator,
    },
    async handler({ body }) {
        if (!body.email) {
            return BaseResponse.badRequest(new Error('Email is required'));
        }

        const email = body.email;
        const marketing = body.marketing !== undefined ? body.marketing : true;

        const subscription = await subscriptionService.createOrUpdate(email, marketing);
        return BaseResponse.success(
            HttpStatusCode.Created,
            'Subscription created successfully',
            subscription
        );
    }
});
