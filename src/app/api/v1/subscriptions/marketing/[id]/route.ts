import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { subscriptionService } from "@/server/services";
import { getUserEmail } from "@/server/utils/requestUtils";
import { UpdateSubscriptionValidator } from "@/server/validators/subscriptions";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const PATCH = BaseRequest<PathParams>({
    allowRoles: [UserScope.CANDIDATE, UserScope.RECRUITER],
    validators: {
        bodyValidator: UpdateSubscriptionValidator,
    },
    async handler({ request, params, body }) {
        const { id } = params;

        // Get the subscription
        const subscription = await subscriptionService.getById(id);

        // Verify the user is updating their own subscription
        try {
            const userEmail = getUserEmail(request);
            if (subscription.email !== userEmail) {
                return BaseResponse.forbidden(
                    new Error('You can only update your own subscription'),
                );
            }
        } catch (error) {
            return BaseResponse.unauthorized('Authentication required');
        }

        // Update the subscription
        const updatedSubscription = await subscriptionService.update(id, {
            ...subscription,
            ...body,
        });

        return BaseResponse.success(HttpStatusCode.Ok, 'Subscription updated successfully', updatedSubscription);
    }
});
