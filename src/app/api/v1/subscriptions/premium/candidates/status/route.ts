import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { premiumSubscriptionService } from "@/server/services";
import { getUserId } from "@/server/utils/requestUtils";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    allowRoles: [UserScope.CANDIDATE],
    async handler({ request }) {
        try {
            // Get the current user's ID from the request headers
            const userId = getUserId(request);

            // Find the user's subscription
            const subscription = await premiumSubscriptionService.findByUserIdAndType(
                userId,
                UserScope.CANDIDATE
            );

            if (!subscription) {
                return BaseResponse.success(
                    HttpStatusCode.Ok,
                    'No subscription found',
                    {
                        hasSubscription: false,
                        subscription: null
                    }
                );
            }

            // Check if subscription is active
            const isActive = await premiumSubscriptionService.hasActiveSubscription(
                userId,
                UserScope.CANDIDATE
            );

            return BaseResponse.success(
                HttpStatusCode.Ok,
                'Subscription status retrieved successfully',
                {
                    hasSubscription: true,
                    isActive,
                    subscription: {
                        id: subscription._id,
                        status: subscription.status,
                        planType: subscription.planType,
                        currentPeriodStart: subscription.currentPeriodStart,
                        currentPeriodEnd: subscription.currentPeriodEnd,
                        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
                        createdAt: subscription.createdAt,
                        updatedAt: subscription.updatedAt
                    }
                }
            );
        } catch (error) {
            console.error("Error getting subscription status:", error);
            return BaseResponse.badRequest(error as Error);
        }
    }
});
