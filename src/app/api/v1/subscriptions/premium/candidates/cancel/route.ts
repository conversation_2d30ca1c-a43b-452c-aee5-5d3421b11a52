import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { premiumSubscriptionService, stripeService } from "@/server/services";
import { getUserId } from "@/server/utils/requestUtils";
import { CancelSubscriptionValidator } from "@/server/validators/premiumSubscriptions";
import { HttpStatusCode } from "axios";

export const POST = BaseRequest({
    allowRoles: [UserScope.CANDIDATE],
    validators: {
        bodyValidator: CancelSubscriptionValidator,
    },
    async handler({ request, body }) {
        try {
            // Get the current user's ID from the request headers
            const userId = getUserId(request);

            // Find the user's subscription
            const subscription = await premiumSubscriptionService.findByUserIdAndType(
                userId,
                UserScope.CANDIDATE
            );

            if (!subscription) {
                return BaseResponse.notFound(new Error('No active subscription found'));
            }

            // Cancel the subscription in Stripe (will cancel at period end)
            const stripeSubscription = await stripeService.cancelSubscription(
                subscription.stripeSubscriptionId
            );

            // Update our local subscription record
            const updatedSubscription = await premiumSubscriptionService.update(
                subscription._id.toString(),
                {
                    cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
                }
            );

            return BaseResponse.success(
                HttpStatusCode.Ok,
                'Subscription cancelled successfully. It will remain active until the end of the current billing period.',
                {
                    subscription: {
                        id: updatedSubscription._id,
                        status: updatedSubscription.status,
                        planType: updatedSubscription.planType,
                        currentPeriodEnd: updatedSubscription.currentPeriodEnd,
                        cancelAtPeriodEnd: updatedSubscription.cancelAtPeriodEnd
                    }
                }
            );
        } catch (error) {
            console.error("Error cancelling subscription:", error);
            return BaseResponse.badRequest(error as Error);
        }
    }
});
