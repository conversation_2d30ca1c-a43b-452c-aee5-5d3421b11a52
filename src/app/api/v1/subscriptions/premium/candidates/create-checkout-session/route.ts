import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { premiumSubscriptionService } from "@/server/services";
import { getUserProfileId } from "@/server/utils/requestUtils";
import { CreateCheckoutSessionValidator } from "@/server/validators/premiumSubscriptions";
import { HttpStatusCode } from "axios";

export const POST = BaseRequest({
    allowRoles: [UserScope.CANDIDATE],
    validators: {
        bodyValidator: CreateCheckoutSessionValidator,
    },
    async handler({ request, body }) {
        try {
            // Get the current candidate's profile ID from the request headers
            const candidateId = getUserProfileId(request);

            const { planType, successUrl, cancelUrl } = body;

            // Create checkout session
            const { sessionUrl, sessionId } = await premiumSubscriptionService.createCandidateCheckoutSession(
                candidateId,
                planType,
                successUrl,
                cancelUrl
            );

            return BaseResponse.success(
                HttpStatusCode.Created,
                'Checkout session created successfully',
                {
                    sessionUrl,
                    sessionId
                }
            );
        } catch (error) {
            console.error("Error creating checkout session:", error);
            return BaseResponse.badRequest(error as Error);
        }
    }
});
