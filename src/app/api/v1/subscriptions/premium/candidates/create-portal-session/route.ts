import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { premiumSubscriptionService } from "@/server/services";
import { getUserId } from "@/server/utils/requestUtils";
import { CreatePortalSessionValidator } from "@/server/validators/premiumSubscriptions";
import { HttpStatusCode } from "axios";

export const POST = BaseRequest({
    allowRoles: [UserScope.CANDIDATE],
    validators: {
        bodyValidator: CreatePortalSessionValidator,
    },
    async handler({ request, body }) {
        try {
            // Get the current user's ID from the request headers
            const userId = getUserId(request);

            const { returnUrl } = body;

            // Create portal session
            const portalUrl = await premiumSubscriptionService.createPortalSession(
                userId,
                UserScope.CANDIDATE,
                returnUrl
            );

            return BaseResponse.success(
                HttpStatusCode.Created,
                'Portal session created successfully',
                {
                    portalUrl
                }
            );
        } catch (error) {
            console.error("Error creating portal session:", error);
            return BaseResponse.badRequest(error as Error);
        }
    }
});
