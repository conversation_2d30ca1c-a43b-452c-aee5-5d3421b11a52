import { NextRequest, NextResponse } from "next/server";
import { premiumSubscriptionService, stripeService } from "@/server/services";
import Stripe from "stripe";

export async function POST(request: NextRequest) {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
        console.error('Missing stripe-signature header');
        return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }

    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
        console.error('STRIPE_WEBHOOK_SECRET not configured');
        return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 });
    }

    let event: Stripe.Event;

    try {
        // Verify webhook signature
        event = stripeService.constructWebhookEvent(body, signature, webhookSecret);
    } catch (error) {
        console.error('Webhook signature verification failed:', error);
        return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log(`Received webhook event: ${event.type}`);

    try {
        switch (event.type) {
            case 'customer.subscription.created':
                await premiumSubscriptionService.handleSubscriptionCreated(
                    event.data.object as Stripe.Subscription
                );
                break;

            case 'customer.subscription.updated':
                await premiumSubscriptionService.handleSubscriptionUpdated(
                    event.data.object as Stripe.Subscription
                );
                break;

            case 'customer.subscription.deleted':
                await premiumSubscriptionService.handleSubscriptionDeleted(
                    event.data.object as Stripe.Subscription
                );
                break;

            case 'invoice.payment_succeeded':
                // Handle successful payment
                console.log('Payment succeeded for subscription');
                break;

            case 'invoice.payment_failed':
                // Handle failed payment
                console.log('Payment failed for subscription');
                break;

            default:
                console.log(`Unhandled event type: ${event.type}`);
        }

        return NextResponse.json({ received: true });
    } catch (error) {
        console.error(`Error handling webhook event ${event.type}:`, error);
        return NextResponse.json(
            { error: 'Webhook handler failed' },
            { status: 500 }
        );
    }
}
