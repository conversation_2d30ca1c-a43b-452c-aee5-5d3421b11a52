import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { emailService } from "@/server/services";
import { SendEmailValidator } from "@/server/validators/email";
import { HttpStatusCode } from "axios";

export const POST = BaseRequest({
    validators: {
        bodyValidator: SendEmailValidator,
    },
    allowRoles: [UserScope.RECRUITER],
    async handler({ body }) {
        try {
            const result = await emailService.sendEmail(body);
            return BaseResponse.success(
                HttpStatusCode.Ok,
                'Email sent successfully',
                { messageId: result.messageId }
            );
        } catch (error) {
            console.error('Error sending email:', error);
            return BaseResponse.error(error as Error);
        }
    }
});
