import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import { emailQueueService } from "@/server/utils/emailQueue";
import { HttpStatusCode } from "axios";

// Get queue statistics
export const GET = BaseRequest({
    async handler({}) {
        try {
            const stats = emailQueueService.getStats();
            return BaseResponse.success(
                HttpStatusCode.Ok, 
                'Email queue statistics retrieved successfully', 
                stats
            );
        } catch (error) {
            console.error('Error retrieving email queue statistics:', error);
            return BaseResponse.error(error as Error);
        }
    }
});

// Control the queue (pause, resume, clear)
export const POST = BaseRequest({
    async handler({ body }) {
        try {
            const { action } = body;
            
            switch (action) {
                case 'pause':
                    emailQueueService.pause();
                    return BaseResponse.success(
                        HttpStatusCode.Ok, 
                        'Email queue paused successfully', 
                        { status: 'paused' }
                    );
                
                case 'resume':
                    emailQueueService.resume();
                    return BaseResponse.success(
                        HttpStatusCode.Ok, 
                        'Email queue resumed successfully', 
                        { status: 'running' }
                    );
                
                case 'clear':
                    emailQueueService.clear();
                    return BaseResponse.success(
                        HttpStatusCode.Ok, 
                        'Email queue cleared successfully', 
                        { status: 'cleared' }
                    );
                
                default:
                    return BaseResponse.badRequest(
                        new Error('Invalid action. Supported actions: pause, resume, clear')
                    );
            }
        } catch (error) {
            console.error('Error controlling email queue:', error);
            return BaseResponse.error(error as Error);
        }
    }
});
