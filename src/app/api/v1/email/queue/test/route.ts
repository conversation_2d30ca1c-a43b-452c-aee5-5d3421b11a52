import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { sendWelcomeEmail } from "@/server/utils/emailUtils";
import { HttpStatusCode } from "axios";

// Test the email queue by sending multiple emails
export const GET = BaseRequest({
    async handler({ request }) {
        try {
            const url = new URL(request.url);
            const count = parseInt(url.searchParams.get('count') || '5', 10);
            const limit = Math.min(count, 20); // Limit to 20 emails max

            // Send multiple test emails to demonstrate queue functionality
            const promises = [];
            for (let i = 0; i < limit; i++) {
                promises.push(
                    sendWelcomeEmail(
                        `Test User ${i + 1}`,
                        `test${i + 1}@example.com`,
                        i % 2 === 0 ? 'candidate' : 'recruiter' // Alternate between candidate and recruiter
                    )
                );
            }

            // Wait for all emails to be queued (not necessarily sent)
            await Promise.all(promises);

            return BaseResponse.success(
                HttpStatusCode.Ok,
                `${limit} test emails added to queue successfully`,
                { count: limit }
            );
        } catch (error) {
            console.error('Error queuing test emails:', error);
            return BaseResponse.error(error as Error);
        }
    }
});
