import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { UserScope } from "@/server/enums";
import {
    sendJobApplicationConfirmationEmail,
    sendWelcomeEmail,
    sendJobApplicationNotificationToRecruiter,
    sendApplicationViewedNotification,
    sendApplicationRejectedNotification,
    sendApplicationShortlistedNotification,
    sendApplicationHiredNotification,
    sendJobClosedNotification,
    sendCandidateNewsletter,
    sendNewExternalJobNotification,
    sendNewC2CJobNotification
} from "@/server/utils/emailUtils";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    async handler({ request }) {
        try {
            const url = new URL(request.url);
            const type = url.searchParams.get('type') || 'welcome-candidate';
            const email = url.searchParams.get('email') || '<EMAIL>';

            let message;

            const jobId = '67fe3afd8eebc034c88390b6';
            const applicantId = '67e27e39bc296d56f18abde7';
            const candidateApplicationUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/my-applications/applications?job_id=${jobId}`;
            const recruiterApplciationUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/dashboard/job/${jobId}?applicant_id=${applicantId}`;

            switch (type) {
                case 'welcome-candidate':
                    await sendWelcomeEmail(
                        'Test Candidate',
                        email,
                        UserScope.CANDIDATE
                    );
                    message = 'Welcome candidate email sent successfully';
                    break;

                case 'welcome-recruiter':
                    await sendWelcomeEmail(
                        'Test Recruiter',
                        email,
                        UserScope.RECRUITER
                    );
                    message = 'Welcome recruiter email sent successfully';
                    break;

                case 'job-application':
                    await sendJobApplicationConfirmationEmail(
                        'Test Applicant',
                        email,
                        'Senior Software Engineer',
                        'TechCorp Solutions',
                        candidateApplicationUrl,
                        {
                            location: 'Remote, USA',
                            jobType: 'Contract (C2C)'
                        }
                    );
                    message = 'Job application confirmation email sent successfully';
                    break;

                case 'application-notification-recruiter':
                    await sendJobApplicationNotificationToRecruiter(
                        'Test Recruiter',
                        email,
                        'John Doe',
                        'Java Developer Urgent',
                        new Date(),
                        recruiterApplciationUrl,
                    );
                    message = 'Job application notification email to recruiter sent successfully';
                    break;

                case 'application-viewed':
                    await sendApplicationViewedNotification(
                        'Test Candidate',
                        email,
                        'Senior Software Engineer',
                        'TechCorp Solutions',
                        '12345',
                        candidateApplicationUrl,
                    );
                    message = 'Application viewed notification email sent successfully';
                    break;

                case 'application-rejected':
                    await sendApplicationRejectedNotification(
                        'Test Candidate',
                        email,
                        'Senior Software Engineer',
                        'TechCorp Solutions',
                        'javascript,react,node',
                        5
                    );
                    message = 'Application rejected notification email sent successfully';
                    break;

                case 'application-shortlisted':
                    await sendApplicationShortlistedNotification(
                        'Test Candidate',
                        email,
                        'Senior Software Engineer',
                        'TechCorp Solutions',
                        '12345',
                        candidateApplicationUrl,
                    );
                    message = 'Application shortlisted notification email sent successfully';
                    break;

                case 'application-hired':
                    await sendApplicationHiredNotification(
                        'Test Candidate',
                        email,
                        'Senior Software Engineer',
                        'TechCorp Solutions',
                        '12345',
                        candidateApplicationUrl,
                    );
                    message = 'Application hired notification email sent successfully';
                    break;

                case 'job-closed':
                    await sendJobClosedNotification(
                        'Test Candidate',
                        email,
                        'Senior Software Engineer',
                        'TechCorp Solutions'
                    );
                    message = 'Job closed notification email sent successfully';
                    break;

                case 'candidate-newsletter':
                    await sendCandidateNewsletter(
                        'Test Candidate',
                        email,
                        5, // Number of matching jobs
                        { bcc: ['<EMAIL>', '<EMAIL>'] }
                    );
                    message = 'Candidate newsletter email sent successfully';
                    break;

                case 'new-external-job':
                    await sendNewExternalJobNotification(
                        'Test Candidate',
                        email,
                        'Senior Full Stack Developer',
                        '<EMAIL>',
                        'https://techcorp.com/careers/senior-fullstack-developer',
                        'We are looking for an experienced Full Stack Developer to join our dynamic team. The ideal candidate should have 5+ years of experience with React, Node.js, and cloud technologies. This is a remote position with competitive compensation and excellent benefits.'
                    );
                    message = 'New external job notification email sent successfully';
                    break;

                case 'new-c2c-job':
                    await sendNewC2CJobNotification(
                        'Test Candidate',
                        email,
                        'Senior React Developer',
                        'TechCorp Solutions',
                        'Austin, TX',
                        'CONTRACT',
                        'C2C-12345',
                        `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/c2c-contract-senior-react-developer-in-austin-tx-for-techcorp-solutions-jobid-c2c-12345`,
                        'We are seeking a highly skilled Senior React Developer to join our innovative team. The ideal candidate will have 5+ years of experience with React, Redux, TypeScript, and modern frontend development practices. This is a contract-to-contract opportunity with competitive hourly rates.',
                        'John Smith',
                        {
                            hourlyRate: '$75-85/hour',
                            skills: 'React, Redux, TypeScript, JavaScript, HTML5, CSS3'
                        }
                    );
                    message = 'New C2C job notification email sent successfully';
                    break;

                default:
                    return BaseResponse.badRequest(
                        new Error('Invalid email type. Supported types: welcome-candidate, welcome-recruiter, job-application, application-notification-recruiter, application-viewed, application-rejected, application-shortlisted, application-hired, job-closed, candidate-newsletter, new-external-job, new-c2c-job')
                    );
            }

            return BaseResponse.success(
                HttpStatusCode.Ok,
                message,
                { type, email }
            );
        } catch (error) {
            console.error('Error sending test email:', error);
            return BaseResponse.error(error as Error);
        }
    }
});
