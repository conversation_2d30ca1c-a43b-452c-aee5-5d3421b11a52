import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { oldJobService } from "@/server/services";
import { HttpStatusCode } from "axios";

type PathParams = { id: string };

export const GET = BaseRequest<PathParams>({
    async handler({ params }) {
        const { id } = params;
        const result = await oldJobService.getOldJobById(id);
        return BaseResponse.success(HttpStatusCode.Ok, 'Old job fetched successfully', result);
    }
});
