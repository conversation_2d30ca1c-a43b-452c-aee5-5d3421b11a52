import { BaseRequest } from "@/server/core/BaseRequest";
import BaseResponse from "@/server/core/BaseResponse";
import { oldJobService } from "@/server/services";
import { HttpStatusCode } from "axios";

export const GET = BaseRequest({
    async handler() {
        const result = await oldJobService.getOldJobs();
        return BaseResponse.success(HttpStatusCode.Ok, 'Old jobs fetched successfully', result);
    }
});
