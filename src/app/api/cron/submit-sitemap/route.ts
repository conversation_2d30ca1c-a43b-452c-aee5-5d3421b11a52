import { NextRequest, NextResponse } from 'next/server';
import { submitAllSitemapsToGoogle } from '@/server/utils/sitemapUtils';
import BaseResponse from '@/server/core/BaseResponse';
import { verifyApiKey } from '@/server/utils/apiKeyUtils';
import { Environment } from '@/server/enums';

/**
 * API route to submit sitemaps to Google Search Console
 * This can be triggered manually or via a cron job
 */
export async function POST(request: NextRequest) {
  try {
    const environment = process.env.ENV;
    if (environment !== Environment.PRODUCTION) {
      return BaseResponse.unauthorized('Sitemap submission is only allowed in production.');
    }

    const providedApiKey = request.headers.get('x-c2c-api-key');
    if (!providedApiKey) {
      return BaseResponse.unauthorized('Missing API key');
    }

    const API_KEY = process.env.X_CRON_API_KEY as string;
    if (!API_KEY) {
      return BaseResponse.error(new Error('X_CRON_API_KEY environment variable not configured'));
    }

    if (!(await verifyApi<PERSON>ey(providedApiKey, API_KEY))) {
      return BaseResponse.unauthorized('Invalid API key');
    }

    // Submit all sitemaps to Google
    const result = await submitAllSitemapsToGoogle();

    // Return the result
    return NextResponse.json(result, {
      status: result.success ? 200 : 500,
    });
  } catch (error) {
    console.error('Error submitting sitemaps to Google:', error);
    return NextResponse.json(
      {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      },
      { status: 500 }
    );
  }
}
