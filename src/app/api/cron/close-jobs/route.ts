import { NextRequest, NextResponse } from 'next/server';
import { jobService } from '@/server/services';
import BaseResponse from '@/server/core/BaseResponse';
import { verifyApiKey } from '@/server/utils/apiKeyUtils';
import { Environment } from '@/server/enums';

/**
 * API route to close jobs older than 3 days
 * This can be triggered manually or via a cron job
 */
export async function POST(request: NextRequest) {
  try {
    const providedApiKey = request.headers.get('x-c2c-api-key');
    if (!providedApiKey) {
      return BaseResponse.unauthorized('Missing API key');
    }

    const API_KEY = process.env.X_CRON_API_KEY as string;
    if (!API_KEY) {
      return BaseResponse.error(new Error('X_CRON_API_KEY environment variable not configured'));
    }

    if (!(await verifyApiKey(providedApiKey, API_KEY))) {
      return BaseResponse.unauthorized('Invalid API key');
    }

    // Parse request body to get optional days parameter
    let days = 3; // Default to 3 days
    try {
      const body = await request.json();
      if (body.days && typeof body.days === 'number' && body.days > 0) {
        days = body.days;
      }
    } catch (error) {
      // If no body or invalid JSON, use default days
      console.log('No valid body provided, using default 3 days');
    }

    console.log(`Starting job closure process for jobs older than ${days} days`);

    // Close old jobs
    const result = await jobService.closeJobsOlderThan(days);

    const responseData = {
      success: true,
      message: `Job closure process completed`,
      data: {
        closedCount: result.closedCount,
        daysThreshold: days,
        errors: result.errors,
        hasErrors: result.errors.length > 0
      }
    };

    console.log('Job closure process completed:', responseData.data);

    // Return success even if there were some errors, but include error details
    return NextResponse.json(responseData, {
      status: 200,
    });
  } catch (error) {
    console.error('Error in job closure process:', error);
    return NextResponse.json(
      {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      },
      { status: 500 }
    );
  }
}
