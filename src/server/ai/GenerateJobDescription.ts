import { JobType } from "@/server/enums";
import { ILocality, ISkill, IWorkAuthorization, Locality, WorkAuthorization } from "@/server/models";
import { localityService, skillService } from "@/server/services";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { streamText } from "ai";
import connectToDb from "@/server/lib/db";

export type GenerateJobDescriptionParams = {
    hint: string;
}

export interface ExtractedJobMetadata {
    role?: string;
    jobType?: JobType;
    skills?: string[];
    location?: {
        city: string;
        state: string;
    };
    salary?: string;
    work_authorizations?: string[];
    work_authorization_remark?: string;
    contract_duration?: string;
    experience_required?: string;
    client?: string;
}

export interface JobDescriptionResponse {
    description: string;
    metadata: {
        role?: string;
        jobType?: JobType;
        skills?: ISkill[];
        location?: ILocality;
        salary?: string;
        work_authorizations?: IWorkAuthorization[];
        work_authorization_remark?: string;
        contract_duration?: string;
        experience_required?: string;
        client?: string;
    };
}

async function buildPrompt(hint: string): Promise<string> {

    let prompt = `You are tasked with two things:
1. Extract structured job details from the hint
2. Generate a professional job description in markdown format

For the structured data extraction, carefully identify the following from the hint ONLY IF CLEARLY MENTIONED:
- Job role/title (e.g., "Software Engineer", "Project Manager")
- Job type (must be EXACTLY one of: REMOTE, HYBRID, or ONSITE - default to REMOTE if unclear)
- Required skills (extract as individual skills, e.g., ["React", "Node.js", "Python"] NOT ["React, Node.js, Python"])
- Location (city and state in full form, e.g., "New York, New York" NOT "NY")
- Salary/rate information (if present)
- Work authorization requirements (extract as an array of specific visa/authorization types using EXACT standard names, e.g., ["H-1B Specialty Occupation", "L-1 Intracompany Transferee", "EB-1 First Preference", "Green Card", "US Citizen", "OPT", "EAD", "TN Visa", "E-3 Australian Professional", "O-1 Extraordinary Ability"])
- Work authorization remarks (any specific statements about visa sponsorship or restrictions, e.g., "No H1B sponsorship available")
- Contract duration (e.g., "6 months", "1 year") ONLY if mentioned
- Minimum required experience (e.g., "3+ years", "5 years" NOT ">3 years")
- Client name (if mentioned)

Do NOT include any field if it's not clearly mentioned in the hint. Leave those fields out of the response entirely.

For the job description:
- Create a professional, detailed description in markdown format
- Include sections for responsibilities, requirements, benefits if possible
- Format with proper markdown headings, bullet points, etc.
- Keep it concise but comprehensive`;

    prompt += `\n\nHint: ${hint}\n\nRespond in the following JSON format ONLY (no additional text before or after):\n{
  "extracted": {
    "role": "Job title/role",
    "jobType": "REMOTE|HYBRID|ONSITE",
    "skills": ["Skill1", "Skill2", ...],
    "location": {
      "city": "City name",
      "state": "State name"
    },
    "salary": "Salary/rate information (if available)",
    "work_authorizations": ["H-1B Specialty Occupation", "L-1 Intracompany Transferee", "EB-1 First Preference", "Green Card", "US Citizen", ...],
    "work_authorization_remark": "Any specific statements about visa sponsorship or restrictions",
    "contract_duration": "Only include if a specific time period is mentioned (e.g., '6 months', '1 year'). Do not include 'C2C' or 'Contract' here",
    "experience_required": "Minimum required experience",
    "client": "Client name if mentioned"
  },
  "description": "Your markdown job description here"
}`;

    return prompt;
}

/**
 * Validates and persists work authorizations from the AI response
 * @param workAuthorizations Array of work authorization names extracted from AI
 * @returns Array of validated and persisted IWorkAuthorization objects
 */
async function validateAndPersistWorkAuthorizations(workAuthorizations: string[]): Promise<IWorkAuthorization[]> {
    if (!workAuthorizations || !Array.isArray(workAuthorizations) || workAuthorizations.length === 0) {
        return [];
    }

    const result: IWorkAuthorization[] = [];

    // Get all work authorizations from the database first
    await connectToDb();
    const allWorkAuthorizations = await WorkAuthorization.find({});
    console.log(`Found ${allWorkAuthorizations.length} work authorizations in the database`);

    // Process each work authorization from the AI
    for (const authName of workAuthorizations) {
        try {
            // Normalize the authorization name
            const normalizedName = authName.trim();
            if (!normalizedName) continue;

            // Try to find an exact match first
            const exactMatch = allWorkAuthorizations.find(auth =>
                auth.name.toLowerCase() === normalizedName.toLowerCase());

            if (exactMatch) {
                console.log(`Found exact match for work authorization: ${exactMatch.name}`);
                result.push(exactMatch);
                continue;
            }

            // Try to find a partial match
            const partialMatches = allWorkAuthorizations.filter(auth => {
                // Check if the authorization name contains the normalized name or vice versa
                return auth.name.toLowerCase().includes(normalizedName.toLowerCase()) ||
                    normalizedName.toLowerCase().includes(auth.name.toLowerCase());
            });

            if (partialMatches.length > 0) {
                // Sort by closest match (shortest name difference)
                partialMatches.sort((a, b) => {
                    const aDiff = Math.abs(a.name.length - normalizedName.length);
                    const bDiff = Math.abs(b.name.length - normalizedName.length);
                    return aDiff - bDiff;
                });

                console.log(`Found partial match for work authorization: ${normalizedName} -> ${partialMatches[0].name}`);
                result.push(partialMatches[0]);
                continue;
            }

            // Try to find a match based on keywords
            const keywords = normalizedName.toLowerCase().split(/\s+/);
            const keywordMatches = allWorkAuthorizations.filter(auth => {
                const authNameLower = auth.name.toLowerCase();
                return keywords.some(keyword =>
                    keyword.length > 2 && authNameLower.includes(keyword));
            });

            if (keywordMatches.length > 0) {
                console.log(`Found keyword match for work authorization: ${normalizedName} -> ${keywordMatches[0].name}`);
                result.push(keywordMatches[0]);
            } else {
                console.log(`No match found for work authorization: ${normalizedName}`);
            }
        } catch (e) {
            console.error(`Error finding work authorization: ${authName}`, e);
        }
    }

    // Remove duplicates by ID
    const uniqueResults = result.filter((auth, index, self) =>
        index === self.findIndex(a => a._id.toString() === auth._id.toString())
    );

    return uniqueResults;
}

/**
 * Validates and persists extracted skills from the AI response
 * @param skills Array of skill names extracted from AI
 * @returns Array of validated and persisted ISkill objects
 */
async function validateAndPersistSkills(skills: string[]): Promise<ISkill[]> {
    if (!skills || !Array.isArray(skills) || skills.length === 0) {
        return [];
    }

    const result: ISkill[] = [];
    const skillsToCreate: any[] = [];

    // First, check for existing skills
    for (const skillName of skills) {
        try {
            // Normalize the skill name
            const normalizedName = skillName.trim();
            if (!normalizedName) continue;

            // @ts-ignore - The getAll method accepts partial query params
            const results = await skillService.getAll({ q: normalizedName, page: 1, limit: 5, category: "" });

            // Find exact or close match
            const exactMatch = results.items.find(item =>
                item.name.toLowerCase() === normalizedName.toLowerCase());

            if (exactMatch) {
                // Use the existing skill
                result.push(exactMatch);
            } else if (results.items.length > 0) {
                // If no exact match but similar skills exist, use the first one
                result.push(results.items[0]);
            } else {
                // No match found, add to creation list
                skillsToCreate.push({
                    name: normalizedName,
                    description: "",
                    category: "Technical" // Default category
                });
            }
        } catch (e) {
            console.error(`Error finding skill: ${skillName}`, e);
        }
    }

    // Create new skills if needed
    if (skillsToCreate.length > 0) {
        try {
            // @ts-ignore - The batchCreate method accepts partial skill objects
            const newSkills = await skillService.batchCreate(skillsToCreate);
            result.push(...newSkills);
        } catch (error) {
            console.error("Error creating skills:", error);
        }
    }

    return result;
}

/**
 * Validates and persists extracted location from the AI response
 * @param location Location object with city and state
 * @returns Validated and persisted ILocality object
 */
async function validateAndPersistLocation(location: { city: string, state: string }): Promise<ILocality> {
    // Normalize location data
    const normalizedCity = location.city.trim();
    const normalizedState = location.state.trim();

    // Escape special characters for RegExp
    const escapeRegExp = (str: string): string => {
        return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };

    const escapedCity = escapeRegExp(normalizedCity);
    const escapedState = escapeRegExp(normalizedState);

    console.log(`Searching for locality: ${normalizedCity}, ${normalizedState}`);

    // Use direct MongoDB query to find exact match with case-insensitive search
    try {
        // Connect to the database and use the Locality model directly
        await connectToDb();

        // First try to find an exact match with case-insensitive search
        const exactMatch = await Locality.findOne({
            city: { $regex: new RegExp(`^${escapedCity}$`, 'i') },
            state: { $regex: new RegExp(`^${escapedState}$`, 'i') }
        });

        if (exactMatch) {
            console.log(`Found exact locality match: ${exactMatch.city}, ${exactMatch.state} (ID: ${exactMatch._id})`);
            return exactMatch;
        }

        // If no exact match, try a more flexible search for the city
        const cityMatches = await Locality.find({
            city: { $regex: new RegExp(`^${escapedCity}$`, 'i') }
        }).limit(5);

        // Check if any city match also has a matching state
        for (const loc of cityMatches) {
            if (loc.state.toLowerCase() === normalizedState.toLowerCase()) {
                console.log(`Found city-state match: ${loc.city}, ${loc.state} (ID: ${loc._id})`);
                return loc;
            }
        }

        // Try an even more flexible search
        const flexibleMatches = await Locality.find({
            $or: [
                { city: { $regex: new RegExp(escapedCity, 'i') } },
                { state: { $regex: new RegExp(escapedState, 'i') } }
            ]
        }).limit(10);

        // Look for the best match
        for (const loc of flexibleMatches) {
            // Check for partial matches
            if (loc.city.toLowerCase().includes(normalizedCity.toLowerCase()) &&
                loc.state.toLowerCase().includes(normalizedState.toLowerCase())) {
                console.log(`Found flexible match: ${loc.city}, ${loc.state} (ID: ${loc._id})`);
                return loc;
            }
        }

        console.log(`No locality match found for: ${normalizedCity}, ${normalizedState}`);
    } catch (error) {
        console.error('Error searching for locality:', error);
    }

    // Create a new location if no matches found
    const newLocation = {
        city: normalizedCity,
        state: normalizedState,
        country: "USA",
        zip: "",
        lat: 0,
        lng: 0
    };

    try {
        // Double-check one more time before creating
        await connectToDb();

        // Final check with case-insensitive matching
        const finalCheck = await Locality.findOne({
            city: { $regex: new RegExp(`^${escapedCity}$`, 'i') },
            state: { $regex: new RegExp(`^${escapedState}$`, 'i') }
        });

        if (finalCheck) {
            console.log(`Found locality in final check: ${finalCheck.city}, ${finalCheck.state} (ID: ${finalCheck._id})`);
            return finalCheck;
        }

        // Try to find existing localities with similar names using text search
        const textSearchResults = await Locality.find(
            { $text: { $search: `${normalizedCity} ${normalizedState}` } },
            { score: { $meta: "textScore" } }
        ).sort({ score: { $meta: "textScore" } }).limit(3);

        if (textSearchResults.length > 0) {
            console.log(`Found locality via text search: ${textSearchResults[0].city}, ${textSearchResults[0].state} (ID: ${textSearchResults[0]._id})`);
            return textSearchResults[0];
        }

        // Create new locality as a last resort
        console.log(`Creating new locality: ${normalizedCity}, ${normalizedState}`);
        // @ts-ignore - The create method accepts partial locality objects
        const createdLocality = await localityService.create(newLocation);
        console.log(`Created new locality with ID: ${createdLocality._id}`);
        return createdLocality;
    } catch (error) {
        console.error("Error creating location:", error);
        throw new Error("Failed to create location");
    }
}

/**
 * Validates the job type against the JobType enum
 * @param jobType The job type string from AI response
 * @returns A valid JobType enum value
 */
function validateJobType(jobType: string): JobType {
    const upperJobType = jobType?.toUpperCase();

    if (upperJobType === JobType.REMOTE ||
        upperJobType === JobType.HYBRID ||
        upperJobType === JobType.ONSITE) {
        return upperJobType as JobType;
    }

    // Default to REMOTE if invalid
    return JobType.REMOTE;
}

/**
 * Parses and validates the AI response to extract job metadata
 * @param responseText The full response text from the AI
 * @returns Parsed and validated job description and metadata
 */
async function parseAIResponse(responseText: string): Promise<JobDescriptionResponse> {
    try {
        // Extract the JSON part from the response
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error("Could not extract JSON from AI response");
        }

        let parsedResponse;
        try {
            parsedResponse = JSON.parse(jsonMatch[0]);
        } catch (jsonError: any) {
            console.error("JSON parse error:", jsonError);
            // Try to clean up the JSON string and parse again
            const cleanedJson = jsonMatch[0].replace(/\\n/g, '\n').replace(/\\r/g, '');
            parsedResponse = JSON.parse(cleanedJson);
        }

        if (!parsedResponse.extracted || !parsedResponse.description) {
            throw new Error("Invalid AI response format: missing extracted data or description");
        }

        const extracted = parsedResponse.extracted;

        // All fields are now optional, so we don't need to validate required fields

        // Validate job type if present
        const validatedJobType = extracted.jobType ? validateJobType(extracted.jobType) : undefined;

        //  Validate skills, location, work authorizations asynchronously
        const [validatedSkills, validatedLocation, validatedWorkAuthorizations] = await Promise.all([
            // Validate and persist skills if present
            extracted.skills ? validateAndPersistSkills(extracted.skills) : undefined,

            // Validate and persist location if present
            extracted.location ? validateAndPersistLocation(extracted.location) : undefined,

            // Find matching work authorizations from the database (no creation of new ones)
            extracted.work_authorizations ?
                validateAndPersistWorkAuthorizations(extracted.work_authorizations) : undefined,
        ]);

        return {
            description: parsedResponse.description,
            metadata: {
                role: extracted.role,
                jobType: validatedJobType,
                skills: validatedSkills,
                location: validatedLocation,
                salary: extracted.salary,
                work_authorizations: validatedWorkAuthorizations,
                work_authorization_remark: extracted.work_authorization_remark,
                contract_duration: extracted.contract_duration,
                experience_required: extracted.experience_required,
                client: extracted.client
            }
        };
    } catch (error: any) {
        console.error("Error parsing AI response:", error);
        throw new Error(`Failed to parse AI response: ${error?.message || 'Unknown error'}`);
    }
}

export async function GenerateJobDescription(params: GenerateJobDescriptionParams): Promise<ReadableStream> {
    const google = createGoogleGenerativeAI({
        apiKey: process.env.GOOGLE_GENAI_API_KEY,
    });

    const model = google('gemini-2.5-flash-preview-04-17');
    const prompt = await buildPrompt(params.hint);
    const system = 'You are a Hiring Manager who is supposed to draft Job Description for recruiting candidates.';

    const { textStream } = streamText({ model, system, prompt });

    const encoder = new TextEncoder();

    let buffer = '';
    return new ReadableStream({
        async start(controller) {
            // Send start event
            const startEvent = encoder.encode('event: start\ndata: {}\n\n');
            controller.enqueue(startEvent);

            // Stream the text chunks as they come in
            for await (const chunk of textStream) {
                buffer += chunk;
                const data = JSON.stringify({ chunk });
                const event = `event: message\ndata: ${data}\n\n`;
                controller.enqueue(encoder.encode(event));
            }

            try {
                // Parse the complete response to extract metadata
                const parsedResponse = await parseAIResponse(buffer);

                // Send the final description
                const descriptionData = JSON.stringify({
                    chunk: parsedResponse.description,
                    type: "description"
                });
                controller.enqueue(encoder.encode(`event: description\ndata: ${descriptionData}\n\n`));

                // Send the extracted metadata
                const metadata: any = {};

                // Only include fields that are present
                if (parsedResponse.metadata.role) metadata.role = parsedResponse.metadata.role;
                if (parsedResponse.metadata.jobType) metadata.jobType = parsedResponse.metadata.jobType;

                if (parsedResponse.metadata.skills && parsedResponse.metadata.skills.length > 0) {
                    metadata.skills = parsedResponse.metadata.skills.map(skill => ({
                        id: skill._id,
                        name: skill.name,
                        category: skill.category
                    }));
                }

                if (parsedResponse.metadata.location) {
                    metadata.location = {
                        id: parsedResponse.metadata.location._id,
                        city: parsedResponse.metadata.location.city,
                        state: parsedResponse.metadata.location.state
                    };
                }

                if (parsedResponse.metadata.salary) metadata.salary = parsedResponse.metadata.salary;

                if (parsedResponse.metadata.work_authorizations && parsedResponse.metadata.work_authorizations.length > 0) {
                    metadata.work_authorizations = parsedResponse.metadata.work_authorizations.map(auth => ({
                        id: auth._id,
                        name: auth.name,
                        category: auth.category
                    }));
                }

                if (parsedResponse.metadata.work_authorization_remark) {
                    metadata.work_authorization_remark = parsedResponse.metadata.work_authorization_remark;
                }

                if (parsedResponse.metadata.contract_duration) {
                    metadata.contract_duration = parsedResponse.metadata.contract_duration;
                }

                if (parsedResponse.metadata.experience_required) {
                    metadata.experience_required = parsedResponse.metadata.experience_required;
                }

                if (parsedResponse.metadata.client) {
                    metadata.client = parsedResponse.metadata.client;
                }

                const metadataData = JSON.stringify({ metadata });
                controller.enqueue(encoder.encode(`event: metadata\ndata: ${metadataData}\n\n`));

                // Send end event
                const finalData = JSON.stringify({
                    complete: true,
                    description: parsedResponse.description,
                    metadata: parsedResponse.metadata
                });
                controller.enqueue(encoder.encode(`event: end\ndata: ${finalData}\n\n`));
            } catch (error: any) {
                // Send error event if parsing fails
                const errorData = JSON.stringify({ error: error?.message || 'Unknown error' });
                controller.enqueue(encoder.encode(`event: error\ndata: ${errorData}\n\n`));

                // Still send the raw buffer as the description
                const finalData = JSON.stringify({ chunk: buffer });
                controller.enqueue(encoder.encode(`event: end\ndata: ${finalData}\n\n`));
            }

            controller.close();
        },
    });
}