import Joi from "joi";
import { PageQueryValidator, SearchQueryValidator } from "./common";

export const GetSkillsValidator = SearchQueryValidator
    .concat(PageQueryValidator)
    .append({
        id: Joi.string().optional(),
        category: Joi.string().optional(),
    });

export const CreateSkillValidator = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().optional(),
    category: Joi.string().required(),
});

export const BatchCreateSkillsValidator = Joi.array().items(CreateSkillValidator);

export const UpdateSkillValidator = Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    category: Joi.string().optional(),
});