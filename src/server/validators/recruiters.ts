import Joi from "joi";
import { SearchQueryValidator, PageQueryValidator } from "./common";

export const GetRecruitersValidator = SearchQueryValidator
    .concat(PageQueryValidator)
    .append({
        organization: Joi.string().optional(),
    });

export const CreateRecruiterValidator = Joi.object({
    phone: Joi.string().required(),
    organization: Joi.string().optional(),
    user: Joi.string().required(),
});

export const UpdateRecruiterValidator = Joi.object({
    phone: Joi.string().optional(),
    organization: Joi.string().optional(),
});