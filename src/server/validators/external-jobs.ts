import Joi from "joi";
import { PageQueryValidator, SortQueryValidator } from "./common";

export const GetExternalJobsValidator = PageQueryValidator
    .concat(SortQueryValidator)
    .append({
        recruiter_email: Joi.string().optional(),
        role: Joi.string().optional(),
    });

export const CreateExternalJobValidator = Joi.object({
    recruiter_email: Joi.string().email().required(),
    job_link: Joi.string().uri().required(),
    role: Joi.string().required(),
    job_description: Joi.string().required(),
});

export const UpdateExternalJobValidator = Joi.object({
    recruiter_email: Joi.string().email().optional(),
    job_link: Joi.string().uri().optional(),
    role: Joi.string().optional(),
    job_description: Joi.string().optional(),
});
