import Joi from 'joi';

export const SendEmailValidator = Joi.object({
    to: Joi.alternatives().try(
        Joi.string().email().required(),
        Joi.array().items(Joi.string().email()).min(1).required()
    ).required(),
    cc: Joi.alternatives().try(
        Joi.string().email(),
        Joi.array().items(Joi.string().email()).min(1)
    ).optional(),
    bcc: Joi.alternatives().try(
        Joi.string().email(),
        Joi.array().items(Joi.string().email()).min(1)
    ).optional(),
    subject: Joi.string().when('template', {
        is: Joi.exist(),
        then: Joi.optional(),
        otherwise: Joi.required()
    }),
    html: Joi.string().when('template', {
        is: Joi.exist(),
        then: Joi.optional(),
        otherwise: Joi.required()
    }),
    text: Joi.string().optional(),
    template: Joi.string().optional(),
    context: Joi.object().optional(),
    attachments: Joi.array().items(
        Joi.object({
            filename: Joi.string().required(),
            content: Joi.alternatives().try(
                Joi.string(),
                Joi.binary()
            ).required(),
            contentType: Joi.string().optional()
        })
    ).optional()
});
