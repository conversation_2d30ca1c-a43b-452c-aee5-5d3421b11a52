import Joi from "joi";

export const PageQueryValidator = Joi.object({
    page: Joi.number().integer().optional(),
    limit: Joi.number().integer().optional().default(0),
});

export const SearchQueryValidator = Joi.object({
    q: Joi.string().optional(),
});

export const SortQueryValidator = Joi.object({
    created_at: Joi.string().valid('asc', 'desc').optional(),
    updated_at: Joi.string().valid('asc', 'desc').optional(),
});