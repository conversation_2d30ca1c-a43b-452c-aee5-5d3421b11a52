import Joi from "joi";
import { PageQueryValidator, SortQueryValidator } from "./common";
import { JOB_TYPE_VALUES } from "@/server/enums";

export const GetJobsValidator = PageQueryValidator
    .concat(SortQueryValidator)
    .append({
        job_id: Joi.string().optional(),
        organization: Joi.string().optional(),
        posted_by: Joi.string().optional(),
        verified: Joi.boolean().optional(),
        active: Joi.boolean().optional(),
    });

export const SearchJobValidator = PageQueryValidator.append({
    query: Joi.string().required(),
    fuzzy: Joi.boolean().optional().default(true),
    filter: Joi.object({
        organization: Joi.string().optional(),
        posted_by: Joi.string().optional(),
        locality: Joi.string().optional(),
        job_type: Joi.array()
            .items(
                Joi.string().valid(...JOB_TYPE_VALUES)
            ).optional(),
        verified: Joi.boolean().optional(),
        active: Joi.boolean().optional(),
    }).optional(),
});

export const CreateJobValidator = Joi.object({
    description: Joi.string().required(),
    description_html: Joi.string().optional(),
    role: Joi.string().required(),
    skills: Joi.array().items(Joi.string()).optional(),

    organization: Joi.string().required(),
    phone: Joi.string().optional(),
    email: Joi.string().email().required(),
    locality: Joi.string().required(),

    job_type: Joi.string().valid(...JOB_TYPE_VALUES).required(),
    hourly_rate: Joi.string().optional(),
});

export const UpdateJobValidator = Joi.object({
    description: Joi.string().optional(),
    description_html: Joi.string().optional(),
    role: Joi.string().optional(),
    skills: Joi.array().items(Joi.string()).optional(),

    company: Joi.string().optional(),
    phone: Joi.string().optional(),
    email: Joi.string().email().optional(),
    locality: Joi.string().optional(),

    job_type: Joi.string().valid(...JOB_TYPE_VALUES).optional(),
    hourly_rate: Joi.string().optional(),
    active: Joi.boolean().optional(),
    verified: Joi.boolean().optional(),
});

export const SimilarJobsValidator = PageQueryValidator.append({
    mmp: Joi.number().min(0).max(100).optional().default(30), // min_match_preference
    lp: Joi.boolean().optional().default(false), // location_preference
    verified: Joi.boolean().optional(), // verified jobs filter (undefined = ignore filter)
});

export const SkillBasedRecommendationsValidator = PageQueryValidator.append({
    skills: Joi.string().optional().default(''), // comma-separated skill names or IDs (empty = latest jobs)
    mmp: Joi.number().min(0).max(100).optional().default(20), // min_match_preference
    lp: Joi.string().optional(), // location_preference (city,state format)
    jt: Joi.string().valid(...JOB_TYPE_VALUES).optional(), // job_type
    verified: Joi.boolean().optional(), // verified jobs filter (undefined = ignore filter)
});

export const CandidateRecommendationsValidator = PageQueryValidator.append({
    mmp: Joi.number().min(0).max(100).optional().default(25), // min_match_preference
    lp: Joi.boolean().optional().default(false), // location_preference (prioritize candidate's location)
    jt: Joi.string().valid(...JOB_TYPE_VALUES).optional(), // job_type
    verified: Joi.boolean().optional(), // verified jobs filter (undefined = ignore filter)
});