import Joi from "joi";
import { PageQueryValidator, SearchQueryValidator } from "./common";

export const GetSubscriptionsValidator = SearchQueryValidator
    .concat(PageQueryValidator)
    .append({
        email: Joi.string().email().optional(),
    });

export const CreateSubscriptionValidator = Joi.object({
    email: Joi.string().email().required(),
    marketing: Joi.boolean().default(true),
});

export const UpdateSubscriptionValidator = Joi.object({
    marketing: Joi.boolean().required(),
});
