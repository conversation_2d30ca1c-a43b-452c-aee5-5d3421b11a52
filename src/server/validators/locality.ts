import Joi from "joi";
import { SearchQueryValidator, PageQueryValidator } from "./common";

export const GetLocalityValidator = SearchQueryValidator
    .concat(PageQueryValidator)
    .append({
        city: Joi.string().optional(),
        state: Joi.string().optional(),
        country: Joi.string().optional(),
    });

export const CreateLocalityValidator = Joi.object({
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().required(),
    zip: Joi.string().optional(),
    lat: Joi.number().optional(),
    lng: Joi.number().optional(),
});

export const BatchCreateLocalitiesValidator = Joi.array().items(CreateLocalityValidator);

export const UpdateLocalityValidator = Joi.object({
    city: Joi.string().optional(),
    state: Joi.string().optional(),
    country: Joi.string().optional(),
    zip: Joi.string().optional(),
    lat: Joi.number().optional(),
    lng: Joi.number().optional(),
});