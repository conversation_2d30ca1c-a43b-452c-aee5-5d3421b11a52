import Joi from "joi";
import { PageQueryValidator, SearchQueryValidator } from "./common";
import { PlanType, SubscriptionStatus, UserScope } from "@/server/enums";

export const GetPremiumSubscriptionsValidator = SearchQueryValidator
    .concat(PageQueryValidator)
    .append({
        userId: Joi.string().optional(),
        userType: Joi.string().valid(...Object.values(UserScope)).optional(),
        status: Joi.string().valid(...Object.values(SubscriptionStatus)).optional(),
        stripeCustomerId: Joi.string().optional(),
        stripeSubscriptionId: Joi.string().optional(),
    });

export const CreateCheckoutSessionValidator = Joi.object({
    planType: Joi.string().valid(...Object.values(PlanType)).required(),
    successUrl: Joi.string().uri().required(),
    cancelUrl: Joi.string().uri().required(),
});

export const CreatePortalSessionValidator = Joi.object({
    returnUrl: Joi.string().uri().required(),
});

export const WebhookValidator = Joi.object({
    type: Joi.string().required(),
    data: Joi.object().required(),
});

export const CancelSubscriptionValidator = Joi.object({
    reason: Joi.string().optional(),
});
