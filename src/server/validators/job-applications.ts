import Joi from "joi";
import { PageQueryValidator, SortQueryValidator } from './common';
import { JobApplicationStatus } from "@/server/enums/job_application_status";

export const GetJobApplicationsValidator = PageQueryValidator
    .concat(SortQueryValidator)
    .append({
        recruiter: Joi.string().optional(),
        organization: Joi.string().optional(),
        job: Joi.string().optional(),
        applicant: Joi.string().optional(),
    });

export const CreateJobApplicationValidator = Joi.object({
    job: Joi.string().required(),
    resume: Joi.string().required(),
});

/**
 * Validator for job application status updates
 */
export const UpdateStatusValidator = Joi.object({
    status: Joi.string()
        .valid(...Object.values(JobApplicationStatus))
        .required()
        .messages({
            'any.required': 'Status is required',
            'any.only': 'Status must be one of: PENDING, READ, REJECTED, SHORTLISTED, HIRED'
        })
});