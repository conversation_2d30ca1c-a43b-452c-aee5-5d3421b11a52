import Joi from "joi";
import { SearchQueryValidator, PageQueryValidator } from "./common";

export const GetOrganizationsValidator = SearchQueryValidator
    .concat(PageQueryValidator)
    .append({
        name: Joi.string().optional(),
        locality: Joi.string().optional(),
    })

export const CreateOrganizationValidator = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().optional(),
    locality: Joi.string().optional(),
    phone: Joi.string().optional(),
    email: Joi.string().optional(),
    website: Joi.string().optional(),
    logo: Joi.string().optional(),
});

export const UpdateOrganizationValidator = Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    locality: Joi.string().optional(),
    phone: Joi.string().optional(),
    email: Joi.string().optional(),
    website: Joi.string().optional(),
    logo: Joi.string().optional(),
});