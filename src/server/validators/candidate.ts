import Joi from "joi";
import { PageQueryValidator, SearchQueryValidator } from "./common";

export const GetCandidatesValidator = SearchQueryValidator.concat(PageQueryValidator);

export const CreateCandidateValidator = Joi.object({
    phone: Joi.string().optional(),
    role: Joi.string().optional(),
    skills: Joi.array().items(Joi.string()).optional(),
    user: Joi.string().required(),
});

export const UpdateCandidateValidator = Joi.object({
    phone: Joi.string().optional(),
    role: Joi.string().optional(),
    skills: Joi.array().items(Joi.string()).optional(),
});