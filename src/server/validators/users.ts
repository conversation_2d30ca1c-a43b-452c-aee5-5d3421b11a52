import Joi from "joi";
import { PageQueryValidator, SearchQueryValidator } from "./common";

export const GetUsersValidator = SearchQueryValidator
    .concat(PageQueryValidator)
    .append({
        email: Joi.string().optional(),
    });

export const CreateUserValidator = Joi.object({
    name: Joi.string().required(),
    email: Joi.string().required(),
});

export const UpdateUserValidator = Joi.object({
    name: Joi.string().optional(),
    email: Joi.string().optional(),
});