import { Model, PopulateOptions } from "mongoose";
import { calculatePage, getQueryObject, PaginationParams, SearchQueryParams } from "./BaseQueryParams";
import { AlreadyExistsError, ErrorNotFound } from "./Errors";
import { IBaseDocument } from "./IBaseDocument";
import connectToDb from "@/server/lib/db";

export interface QueryResult<T> {
    items: T[];
    total: number;
    count: number;
    page: number;
}

export type PopulateParam = PopulateOptions | PopulateOptions[];

export default abstract class BaseRepository<
    T extends IBaseDocument,
    Q extends SearchQueryParams | PaginationParams | undefined> {

    constructor(protected model: Model<T>) { }

    protected populate: PopulateParam = [];

    async buildQueryParams(queryParams?: Q): Promise<any> {
        return getQueryObject(queryParams as SearchQueryParams | undefined);
    }

    async buildSortParams(queryParams?: Q): Promise<any> {
        return {};
    }

    async getAll(queryParams?: Q): Promise<QueryResult<T>> {
        const pageParams = queryParams as PaginationParams | undefined;
        const skip = calculatePage(pageParams);
        const query = await this.buildQueryParams(queryParams);
        const limit = pageParams?.limit ?? 0;
        const sort = await this.buildSortParams(queryParams);

        await connectToDb();

        const [items, total] = await Promise.all([
            this.model.find(query).skip(skip).limit(limit).sort(sort).populate(this.populate),
            this.model.find(query).countDocuments()
        ]);

        console.log(`Fetched ${items.length} ${this.model.modelName} items}`);

        return {
            items,
            total,
            count: items.length,
            page: pageParams?.page ?? 1,
        };
    }

    async getById(id: string): Promise<T> {
        await connectToDb();

        const found = await this.model.findById(id).populate(this.populate);
        if (!found) {
            throw new ErrorNotFound(`${this.model.modelName}(${id}) not found`);
        }
        return found;
    }

    async create(dto: T): Promise<T> {
        await connectToDb();

        try {
            const created = await (await this.model.create(dto)).populate(this.populate);
            if (!created) {
                throw new Error(`Failed to create ${this.model.modelName}`);
            }
            return created;
        } catch (error) {
            throw new AlreadyExistsError(`${this.model.modelName} already exists`);
        }
    }

    async update(id: string, dto: T): Promise<T> {
        await connectToDb();

        const options = {
            new: true,
            populate: this.populate
        };
        const updated = await this.model.findByIdAndUpdate(id, dto, options);
        if (!updated) {
            throw new ErrorNotFound(`${this.model.modelName}(${id}) not found`);
        }
        return updated;
    }

    async delete(id: string): Promise<T> {
        await connectToDb();

        const deleted = await this.model.findByIdAndDelete(id);
        if (!deleted) {
            throw new ErrorNotFound(`${this.model.modelName}(${id}) not found`);
        }
        return deleted;
    }
}