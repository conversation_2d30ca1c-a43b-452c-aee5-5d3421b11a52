import { UserScope } from "@/server/enums";
import { getUserProfileId, getUserScope } from "@/server/utils/requestUtils";
import { NextRequest, NextResponse } from "next/server";
import BaseResponse from "./BaseResponse";
import <PERSON><PERSON> from "joi";

export type Params = object;

/**
 * Type for ease of access to request path parameters
 */
export type RequestParams<P extends Params> = { params: Promise<P> };

export type RequestValidator = {
    /**
     * Joi Validator for request body
     */
    bodyValidator?: Joi.ObjectSchema | Joi.ArraySchema,

    /**
     * Joi Validator for request query parameters
     */
    queryValidator?: Joi.ObjectSchema,
}

export type BaseRequestOptions<P extends Params> = {
    /**
     * Allowed roles for the request
     * 
     * IMPORTANT: While adding roles, the route is included for middleware.
     */
    allowRoles?: UserScope | UserScope[],

    /**
     * Populate created_by and updated_by fields
     */
    populateCreatedAndUpdatedBy?: boolean,

    /**
     * Joi Validator for request
     * 
     * If validators are provided, use `body`/`query` to retrieve in {@link BaseRequestOptions.handler}
     */
    validators?: RequestValidator,

    /**
     * Implementation for request Handler
     */
    handler: NextRequestHandler<P>,
}

/**
 * Generalised Next Request Handler
 */
type NextRequestHandler<P extends Params> = (params: { request: NextRequest, params: P, body: any, query: any }) => Promise<NextResponse>;

function assertRole(request: NextRequest, roles: UserScope | UserScope[]): Error | undefined {
    const scope = getUserScope(request);
    if (!scope) {
        return new Error('Unauthorized');
    }

    if (!Array.isArray(roles)) {
        roles = [roles];
    }

    if (!roles.includes(scope as UserScope)) {
        return new Error(`Access Denied (${scope})`);
    }
}

async function runValidators(
    request: NextRequest,
    options: RequestValidator
): Promise<{ queryValue?: any, bodyValue?: any, error?: Error }> {
    let queryValue, bodyValue;
    if (options) {
        const { bodyValidator, queryValidator } = options;
        if (bodyValidator) {
            const { value, error } = bodyValidator.validate(await request.json());
            if (error) {
                return { error };
            }
            bodyValue = value;
        }

        if (queryValidator) {
            const queryParams = Object.fromEntries(request.nextUrl.searchParams);
            const { value, error } = queryValidator.validate(queryParams);
            if (error) {
                return { error };
            }
            queryValue = value;
        }
    }

    return { queryValue, bodyValue };
}

/**
 * Base Request Handler
 * 
 * Wrapper function to handle request operations globally
 * 
 * @param options BaseRequestOptions
 * @returns Next Request Handler {@link NextRequestHandler}
 */
export function BaseRequest<P extends Params>(options: BaseRequestOptions<P>) {
    return async (request: NextRequest, pathParams: RequestParams<P>) => {
        let roles = options.allowRoles;

        if (roles) {
            const error = assertRole(request, roles);
            if (error) {
                return BaseResponse.unauthorized(error.message);
            }
        }

        let query, body;
        if (options.validators) {
            const { queryValue, bodyValue, error } = await runValidators(request, options.validators);
            if (error) {
                return BaseResponse.badRequest(error);
            }

            if (options.populateCreatedAndUpdatedBy === true) {
                const profileId = getUserProfileId(request);
                if (request.method === 'POST') {
                    bodyValue.created_by = profileId;
                }
                bodyValue.updated_by = profileId;
            }

            query = queryValue;
            body = bodyValue;
        }

        const params = await pathParams.params;
        return options.handler({ request, params, body, query })
            .catch(
                (error) => BaseResponse.error(error)
            );
    }
}