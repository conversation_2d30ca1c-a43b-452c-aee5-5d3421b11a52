import { HttpStatusCode } from "axios";
import { NextResponse } from "next/server";
import { ErrorCodes } from "./Errors";

function getCodeFromErrorCause(error: Error): number {
    switch (error.cause) {
        case ErrorCodes.NOT_FOUND:
            return HttpStatusCode.NotFound;
        case ErrorCodes.ALREADY_EXISTS:
            return HttpStatusCode.Conflict;
        case ErrorCodes.IMMUTABLE_OBJ:
            return HttpStatusCode.BadRequest;
        case ErrorCodes.UNAUTHORIZED:
            return HttpStatusCode.Unauthorized;
    }

    return HttpStatusCode.InternalServerError;
}

export default class BaseResponse {
    static success(status: number, message: string, data: any): NextResponse {
        return NextResponse.json({
            status,
            message,
            data,
        }, { status: status });
    }

    static badRequest(error?: Error): NextResponse {
        return NextResponse.json({
            status: HttpStatusCode.BadRequest,
            message: 'Bad request',
            error: (error as Error)?.message,
        }, { status: HttpStatusCode.BadRequest });
    }

    static unauthorized(message?: string): NextResponse {
        return NextResponse.json({
            status: HttpStatusCode.Unauthorized,
            message: message ?? 'Unauthorized',
        }, { status: HttpStatusCode.Unauthorized });
    }

    static forbidden(error?: Error): NextResponse {
        return NextResponse.json({
            status: HttpStatusCode.Forbidden,
            message: 'Forbidden',
            error: error?.message,
        }, { status: HttpStatusCode.Forbidden });
    }

    static error(error?: any): NextResponse {
        const status = getCodeFromErrorCause(error);
        return NextResponse.json({
            status: status,
            message: error?.message ?? 'Internal server error',
            error: (error as Error)?.cause,
        }, { status: status });
    }

    static stream(stream: ReadableStream) {
        const headers = {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
        };
        return new NextResponse(stream, { headers });
    }
}