export type PaginationParams = {
    page: number;
    limit: number;
};

export type SearchQueryParams = {
    q: string;
}

export enum SortOrder {
    ASC = 'asc',
    DESC = 'desc',
}

export type SortQueryParams = {
    created_at: SortOrder;
    updated_at: SortOrder;
}

export type SearchPaginationParams = PaginationParams & SearchQueryParams;

export function calculatePage(params?: PaginationParams) {
    if (!params) return 0;
    return (params.page - 1) * params.limit;
}

export function getQueryObject(params?: SearchQueryParams) {
    if (params?.q == undefined) return {};
    return {
        $text: {
            $search: new RegExp(`${params!.q}`, 'i')
        }
    }
}