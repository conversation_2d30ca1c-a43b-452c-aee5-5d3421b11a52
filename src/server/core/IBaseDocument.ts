import { Schema, Types } from "mongoose";

export type Reference<T> = T | Types.ObjectId | Schema.Types.ObjectId;

export interface IBaseDocument {
    _id: Types.ObjectId;
    created_by?: string;
    modified_by?: string;
    active?: boolean;
    created_at?: Date;
    updated_at?: Date;
}

//  BaseDocumentSchema is used to define the common fields that are present in all the models.
// 
//  Timestamps should be added within the schema explicitly as follows. 
//  Schema(...schemaObject, { timestamps: true })
export const BaseDocumentSchema = {
    created_by: { type: Schema.Types.ObjectId, default: null },
    modified_by: { type: Schema.Types.ObjectId, default: null },

    active: { type: Boolean, default: true },
}