import { PaginationParams, SearchQueryParams } from "@/server/core/BaseQueryParams";
import { IBaseDocument } from "@/server/core/IBaseDocument";
import BaseRepository, { QueryResult } from "./BaseRepository";

export default abstract class BaseService<
    R extends BaseRepository<T, Q>,
    T extends IBaseDocument,
    Q extends SearchQueryParams | PaginationParams | undefined> {

    constructor(protected repository: R) { }

    async getAll(queryParams?: Q): Promise<QueryResult<T>> {
        return this.repository.getAll(queryParams);
    }

    async getById(id: string): Promise<T> {
        return this.repository.getById(id);
    }

    async create(data: T): Promise<T> {
        return this.repository.create(data);
    }

    async update(id: string, data: T): Promise<T> {
        return this.repository.update(id, data);
    }

    async delete(id: string): Promise<T> {
        return this.repository.delete(id);
    }
}