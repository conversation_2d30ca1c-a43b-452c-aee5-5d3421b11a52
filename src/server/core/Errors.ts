export const ErrorCodes = {
    NOT_FOUND: 'NOT_FOUND',
    ALREADY_EXISTS: 'ALREADY_EXISTS',
    IMMUTABLE_OBJ: 'IMMUTABLE_OBJ',
    UNAUTHORIZED: 'UNAUTHORIZED',
};

export class ErrorNotFound extends Error {
    constructor(message: string) {
        super(message);
        this.cause = ErrorCodes.NOT_FOUND;
    }
}

export class AlreadyExistsError extends Error {
    constructor(message: string) {
        super(message);
        this.cause = ErrorCodes.ALREADY_EXISTS;
    }
}

export class ImmutableObjectError extends Error {
    constructor(message: string) {
        super(message);
        this.cause = ErrorCodes.IMMUTABLE_OBJ;
    }
}

export class UnauthorizedError extends Error {
    constructor(message: string) {
        super(message);
        this.cause = ErrorCodes.UNAUTHORIZED;
    }
}