import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IUser } from "@/server/models";
import { jwtVerify, SignJWT, importJW<PERSON> } from "jose";
import { cookies } from "next/headers";
import { NextRequest } from "next/server";

const PRIVATE_JWK = process.env.PRIVATE_JWK;
const PUBLIC_JWK = process.env.PUBLIC_JWK;

export interface IUserPayload {
    /**
     * Subject - Identifier for the User object
     */
    sub: string;

    /**
     * Scope - User type of values {@link UserScope}
     */
    scope: string;

    /**
     * Profile ID - Identifier for the Profile object from {@link UserScope}
     */
    profile_id: string;
    email: string;
    name: string;
}

/**
 * Gets token from Request Header otherwise from Cookie
 * @param request 
 * @returns token if found
 */
export async function getTokenFromCookieOrRequest(request: NextRequest): Promise<string | undefined> {
    return request.headers.get('authorization')?.split(' ')[1]
        || (await cookies()).get('x-c2c-token')?.value;
}

/**
 * Generate a JWT token with `jose`
 * @param profile
 * @param scope
 * @returns 
 */
export async function generateJwtToken(profile: ICandidate | IRecruiter, scope: string): Promise<string> {
    if (!PRIVATE_JWK) {
        throw new Error('PRIVATE_JWK is not set in environment variables');
    }

    const alg = 'RS256';
    const jwk = JSON.parse(PRIVATE_JWK as string);
    const privateKey = await importJWK(jwk, alg);

    const user = profile.user as IUser;
    const payload: IUserPayload = {
        sub: user._id.toString(),
        name: user.name || '',
        email: user.email,
        profile_id: profile._id.toString(),
        scope: scope,
    }

    const jwt = await new SignJWT({ ...payload })
        .setProtectedHeader({ alg })
        .setIssuedAt()
        .setExpirationTime('7d')
        .sign(privateKey);

    return jwt;
}

/**
 * Verify the token and return the payload is verfification was successful
 * @param token 
 * @returns 
 */
export async function verifyJwtToken(token: string): Promise<IUserPayload | undefined> {
    try {
        if (!PUBLIC_JWK) {
            throw new Error('PUBLIC_JWK is not set in environment variables');
        }

        const jwk = JSON.parse(PUBLIC_JWK as string);
        const { payload } = await jwtVerify<IUserPayload>(token, jwk);
        return payload;
    } catch (error) {
        console.log(error);
    }
}