import { ObjectId } from "mongodb";
import { Adapter } from "next-auth/adapters";
import { Account, Session, User, VerificationToken } from "@/server/models";
import connectToDb from "@/server/lib/db";

export function MongoDBAdapter(): Adapter {
  return {
    async createUser(userData) {
      await connectToDb();

      const user = new User({
        name: userData.name,
        email: userData.email,
        image: userData.image,
        emailVerified: userData.emailVerified,
      });

      await user.save();

      return {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
      };
    },

    async getUser(id) {
      await connectToDb();

      const user = await User.findById(id);

      if (!user) return null;

      return {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
      };
    },

    async getUserByEmail(email) {
      await connectToDb();

      const user = await User.findOne({ email });

      if (!user) return null;

      return {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
      };
    },

    async getUserByAccount({ provider, providerAccountId }) {
      await connectToDb();

      const account = await Account.findOne({ provider, providerAccountId });

      if (!account) return null;

      const user = await User.findById(account.userId);

      if (!user) return null;

      return {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
      };
    },

    async updateUser(userData) {
      await connectToDb();

      const user = await User.findByIdAndUpdate(
        userData.id,
        {
          name: userData.name,
          email: userData.email,
          image: userData.image,
          emailVerified: userData.emailVerified,
        },
        { new: true }
      );

      if (!user) throw new Error("User not found");

      return {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
      };
    },

    async deleteUser(userId) {
      await connectToDb();

      // Delete user's accounts, sessions, and the user
      await Promise.all([
        Account.deleteMany({ userId: new ObjectId(userId) }),
        Session.deleteMany({ userId: new ObjectId(userId) }),
        User.findByIdAndDelete(userId),
      ]);
    },

    async linkAccount(accountData) {
      await connectToDb();

      const existingAccount = await Account.findOne({
        provider: accountData.provider,
        providerAccountId: accountData.providerAccountId,
      });

      if (existingAccount) {
        // Delete the existing account and try again
        await Account.deleteOne({
          provider: accountData.provider,
          providerAccountId: accountData.providerAccountId
        });
      }

      const account = new Account({
        userId: new ObjectId(accountData.userId),
        type: accountData.type,
        provider: accountData.provider,
        providerAccountId: accountData.providerAccountId,
        refresh_token: accountData.refresh_token,
        access_token: accountData.access_token,
        expires_at: accountData.expires_at,
        token_type: accountData.token_type,
        scope: accountData.scope,
        id_token: accountData.id_token,
        session_state: accountData.session_state,
      });

      await account.save();
      return account as any;
    },

    async unlinkAccount({ provider, providerAccountId }) {
      await connectToDb();

      await Account.findOneAndDelete({
        provider,
        providerAccountId,
      });
    },

    async createSession(sessionData) {
      await connectToDb();

      const session = new Session({
        userId: new ObjectId(sessionData.userId),
        expires: sessionData.expires,
        sessionToken: sessionData.sessionToken,
      });

      await session.save();

      return {
        id: session._id.toString(),
        userId: session.userId.toString(),
        expires: session.expires,
        sessionToken: session.sessionToken,
      };
    },

    async getSessionAndUser(sessionToken) {
      await connectToDb();

      const session = await Session.findOne({ sessionToken });

      if (!session) return null;

      const user = await User.findById(session.userId);

      if (!user) return null;

      // Fetch candidate and recruiter profiles if they exist
      const { Candidate, Recruiter } = await import('@/server/models');
      const candidate = await Candidate.findOne({ user: user._id });
      const recruiter = await Recruiter.findOne({ user: user._id });

      // Create a user object with profile information
      const userWithProfiles = {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
        candidateProfile: candidate ? {
          id: candidate._id.toString(),
          phone: candidate.phone,
          role: candidate.role,
          skills: candidate.skills,
        } : null,
        recruiterProfile: recruiter ? {
          id: recruiter._id.toString(),
          phone: recruiter.phone,
          organization: recruiter.organization,
        } : null,
      };

      return {
        session: {
          id: session._id.toString(),
          userId: session.userId.toString(),
          expires: session.expires,
          sessionToken: session.sessionToken,
        },
        user: userWithProfiles,
      };
    },

    async updateSession(sessionData) {
      await connectToDb();

      const session = await Session.findOneAndUpdate(
        { sessionToken: sessionData.sessionToken },
        {
          expires: sessionData.expires,
        },
        { new: true }
      );

      if (!session) return null;

      return {
        id: session._id.toString(),
        userId: session.userId.toString(),
        expires: session.expires,
        sessionToken: session.sessionToken,
      };
    },

    async deleteSession(sessionToken) {
      await connectToDb();

      await Session.findOneAndDelete({ sessionToken });
    },

    async createVerificationToken(verificationTokenData) {
      await connectToDb();

      const verificationToken = new VerificationToken({
        identifier: verificationTokenData.identifier,
        token: verificationTokenData.token,
        expires: verificationTokenData.expires,
      });

      await verificationToken.save();

      return {
        identifier: verificationToken.identifier,
        token: verificationToken.token,
        expires: verificationToken.expires,
      };
    },

    async useVerificationToken({ identifier, token }) {
      await connectToDb();

      const verificationToken = await VerificationToken.findOneAndDelete({
        identifier,
        token,
      });

      if (!verificationToken) return null;

      return {
        identifier: verificationToken.identifier,
        token: verificationToken.token,
        expires: verificationToken.expires,
      };
    },
  };
}
