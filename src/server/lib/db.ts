import { connect, connection } from "mongoose";

const MONGO_URI = process.env.MONGO_URI as string;

// Track connection state
let isConnected = false;

export default async function connectToDb() {
    // If already connected, return the existing connection
    if (isConnected) {
        return connection;
    }

    try {
        // Set connection options
        const conn = await connect(MONGO_URI, {
            // These options help with connection stability
            maxPoolSize: 10,
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
        });

        isConnected = true;
        console.log(`MongoDB connected on ${process.env.ENV} environment`);

        // Handle connection errors
        connection.on('error', (err) => {
            console.error('MongoDB connection error:', err);
            isConnected = false;
        });

        // Handle disconnection
        connection.on('disconnected', () => {
            console.log('MongoDB disconnected');
            isConnected = false;
        });

        return conn;
    } catch (error) {
        console.error('Error connecting to MongoDB:', error);
        isConnected = false;
        throw error;
    }
}