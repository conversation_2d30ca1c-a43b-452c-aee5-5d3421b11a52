import path from 'path';
import { Storage } from '@google-cloud/storage';
import heicConvert from 'heic-convert';
import { IBaseGcsFile } from '@/server/core/IBaseGcsFile';

export interface IFileRequest {
    /**
     * File to upload
     */
    file: File;

    /**
     * Path of file without file name
     * 
     * E.g., /path/to/resume
     */
    folderPath: string;

    /**
     * Name of file
     * 
     * E.g., resume.pdf
     */
    fileName: string;
}

/**
 * 
 * @returns Random 10 character string
 */
export const randomFileSuffix = () => Math.random().toString(36).substring(2, 12);

/**
 * Upload multiple files to Google Cloud Storage
 * 
 * @param file as {@link IFileRequest}
 * @returns response as {@link IBaseGcsFile}
 */
export async function uploadFile(file: IFileRequest): Promise<IBaseGcsFile> {
    return uploadSingleFile(file);
}

/**
 * Upload multiple files to Google Cloud Storage
 * NOTE: By default, all files created will be public
 * 
 * @param files as array of {@link IFileRequest}
 * @returns response as array of {@link IBaseGcsFile}
 */
export async function uploadFiles(files: IFileRequest[]): Promise<IBaseGcsFile[]> {
    try {
        const filePromises = files.map(
            file => uploadSingleFile(file)
        );
        return Promise.all(filePromises);
    } catch (error) {
        throw new Error('Failed to upload files');
    }
}

/**
 * Wrapper function to upload a file to Google Cloud Storage
 * 
 * @param fileInfo 
 * @returns 
 */
async function uploadSingleFile(fileInfo: IFileRequest): Promise<IBaseGcsFile> {
    let { buffer, contentType, extension } = await prepareFile(fileInfo.file);

    let { folderPath, fileName } = fileInfo;
    if (!fileName) {
        fileName = `ONLYC2C_SECURED_${randomFileSuffix()}`;
    }
    const destination = `${folderPath}/${fileName}${extension}`;
    return upload(buffer, contentType, destination);
}

/**
 * Prepare the file for upload
 * Handles HEIC/HEIF conversion to JPEG
 * 
 * @param file 
 * @returns 
 */
async function prepareFile(file: File) {
    let fileBuffer = await file.arrayBuffer();
    let mimeType = file.type;
    let extension = path.extname(file.name); // Extract the original file extension

    if (mimeType === 'image/heic' || mimeType === 'image/heif') {
        // Check if the file is HEIC and convert it to JPEG
        fileBuffer = await heicConvert({
            buffer: await file.arrayBuffer(),
            format: 'JPEG'
        });
        mimeType = 'image/jpeg';
        extension = '.jpeg'; // Change extension to `.jpeg` after conversion
    }

    const buffer = new Uint8Array(fileBuffer);
    return { buffer, contentType: mimeType, extension };
}

/**
 * Upload the file to Google Cloud Storage
 * 
 * @param buffer File buffer as {@link Uint8Array}
 * @param mimetype 
 * @param destination 
 * @returns 
 */
async function upload(buffer: Uint8Array, mimetype: string, destination: string): Promise<IBaseGcsFile> {
    const storage = new Storage({
        projectId: process.env.GOOGLE_CLOUD_PROJECT,
        keyFilename: path.resolve(process.env.GOOGLE_SERVICE_ACCOUNT_KEY as string)
    });

    const bucket = storage.bucket(process.env.GCS_BUCKET_NAME as string);

    const blob = bucket.file(destination);
    const blobStream = blob.createWriteStream({
        resumable: false,
        contentType: mimetype
        // Removed predefinedAcl: 'publicRead'
    });

    blobStream.end(buffer);

    return new Promise((resolve, reject) => {
        blobStream.on('finish', async () => {
            try {
                await blob.makePublic();
                const publicUrl = `https://storage.googleapis.com/${bucket.name}/${blob.name}`;

                console.info(`------\nFile Uploaded successfully!\nFile: ${destination}\n------`);

                resolve({
                    url: publicUrl,
                    key: blob.name
                });
            } catch (error) {
                reject(error);
            }
        }).on('error', (err) => { reject(err) });
    });
}

/**
 * Delete a file from Google Cloud Storage
 * 
 * @param key Key referenced in GCS bucket
 */
export async function deleteFile(key: string): Promise<void> {
    try {
        const storage = new Storage({
            projectId: process.env.GOOGLE_CLOUD_PROJECT,
            keyFilename: path.resolve(process.env.GOOGLE_SERVICE_ACCOUNT_KEY as string)
        });

        const bucket = storage.bucket(process.env.GCS_BUCKET_NAME as string);
        const file = bucket.file(key);
        await file.delete();
        console.info(`------\nFile deleted successfully!\nKey: ${key}\n------`);
    } catch (error) {
        console.log(error);
    }
}