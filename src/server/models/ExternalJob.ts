import { model, models, Schema } from 'mongoose';
import { BaseDocumentSchema, IBaseDocument } from '@/server/core/IBaseDocument';
import { SchemaRef } from '.';

export interface IExternalJob extends IBaseDocument {
    recruiter_email: string;
    job_link: string;
    role: string;
    job_description: string;
}

const schema = new Schema<IExternalJob>({
    ...BaseDocumentSchema,

    recruiter_email: { 
        type: String, 
        required: true,
        index: true 
    },
    job_link: { 
        type: String, 
        required: true 
    },
    role: { 
        type: String, 
        required: true,
        index: true 
    },
    job_description: { 
        type: String, 
        required: true 
    },
}, { timestamps: true });

// Create text index for search functionality
schema.index({
    role: 'text',
    job_description: 'text',
    recruiter_email: 'text',
});

export const ExternalJob = models.external_jobs || model<IExternalJob>(SchemaRef.external_jobs, schema);
