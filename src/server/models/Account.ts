import { BaseDocumentSchema, IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { IUser, SchemaRef } from ".";

export type AdapterAccountType = "oauth" | "oidc" | "email" | "webauthn";

export interface IAccount extends IBaseDocument {
    userId: Reference<IUser>;
    type: AdapterAccountType;
    provider: string;
    providerAccountId: string;
    refresh_token?: string;
    access_token?: string;
    expires_at?: number;
    token_type?: string;
    scope?: string;
    id_token?: string;
    session_state?: string;
}

const schema = new Schema<IAccount>({
    ...BaseDocumentSchema,

    userId: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.users,
        required: true,
    },
    type: {
        type: String,
        required: true,
    },
    provider: {
        type: String,
        required: true,
    },
    providerAccountId: {
        type: String,
        required: true,
    },
    refresh_token: { type: String },
    access_token: { type: String },
    expires_at: { type: Number },
    token_type: { type: String },
    scope: { type: String },
    id_token: { type: String },
    session_state: { type: String },
}, { timestamps: true });

// Create a compound index to ensure uniqueness of provider + providerAccountId
schema.index({ provider: 1, providerAccountId: 1 }, { unique: true });

export const Account = models.accounts || model<IAccount>(SchemaRef.accounts, schema);
