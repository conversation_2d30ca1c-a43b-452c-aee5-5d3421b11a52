import { model, models, Schema } from "mongoose";

export interface IVerificationToken {
    identifier: string;
    token: string;
    expires: Date;
}

const schema = new Schema<IVerificationToken>({
    identifier: {
        type: String,
        required: true,
    },
    token: {
        type: String,
        required: true,
    },
    expires: {
        type: Date,
        required: true,
    },
});

// Create a compound index to ensure uniqueness of identifier + token
schema.index({ identifier: 1, token: 1 }, { unique: true });

export const VerificationToken = models.verification_tokens || model<IVerificationToken>("verification_tokens", schema);
