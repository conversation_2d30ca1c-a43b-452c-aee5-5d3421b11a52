import { BaseDocumentSchema, IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { IOrganization, IUser, SchemaRef } from ".";

export interface IRecruiter extends IBaseDocument {
    user: Reference<IUser>;
    phone: string;
    organization: Reference<IOrganization>;
}

const schema = new Schema<IRecruiter>({
    ...BaseDocumentSchema,

    user: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.users,
        required: true,
        unique: true,
    },
    phone: { type: String, required: false },
    organization: {
        type: Schema.Types.ObjectId,
        required: false,
        ref: SchemaRef.organizations,
        index: true,
    },
}, { timestamps: true });

export const Recruiter = models.recruiters || model<IRecruiter>(SchemaRef.recruiters, schema);