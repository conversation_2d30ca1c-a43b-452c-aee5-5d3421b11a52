import { BaseDocumentSchema, IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { IUser, SchemaRef } from ".";
import { SubscriptionStatus, PlanType, UserScope } from "@/server/enums";

export interface IPremiumSubscription extends IBaseDocument {
    userId: Reference<IUser>;
    userType: UserScope;
    stripeCustomerId: string;
    stripeSubscriptionId: string;
    status: SubscriptionStatus;
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    cancelAtPeriodEnd: boolean;
    planType: PlanType;
    priceId: string;
}

const schema = new Schema<IPremiumSubscription>({
    ...BaseDocumentSchema,

    userId: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.users,
        required: true,
        index: true
    },
    userType: {
        type: String,
        enum: Object.values(UserScope),
        required: true,
        index: true
    },
    stripeCustomerId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    stripeSubscriptionId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    status: {
        type: String,
        enum: Object.values(SubscriptionStatus),
        required: true,
        index: true
    },
    currentPeriodStart: {
        type: Date,
        required: true
    },
    currentPeriodEnd: {
        type: Date,
        required: true,
        index: true
    },
    cancelAtPeriodEnd: {
        type: Boolean,
        default: false
    },
    planType: {
        type: String,
        enum: Object.values(PlanType),
        required: true,
        index: true
    },
    priceId: {
        type: String,
        required: true
    }
}, { timestamps: true });

// Create compound index for efficient queries
schema.index({ userId: 1, userType: 1 }, { unique: true });
schema.index({ status: 1, currentPeriodEnd: 1 });

export const PremiumSubscription = models.premiumSubscriptions || model<IPremiumSubscription>(SchemaRef.premiumSubscriptions, schema);
