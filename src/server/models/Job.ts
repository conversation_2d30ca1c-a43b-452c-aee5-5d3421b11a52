import { model, models, Schema } from 'mongoose';
import { BaseDocumentSchema, IBaseDocument, Reference } from '@/server/core/IBaseDocument';
import { ILocality, IOrganization, IRecruiter, ISkill, SchemaRef } from '.';
import { JobType } from '@/server/enums';

const DOMAIN_NAME = process.env.DOMAIN_NAME as string;

export interface IJob extends IBaseDocument {
    title: string;
    description: string;
    description_html: string;
    role: string;
    skills: ISkill[] | string[];

    phone: string;
    email: string;
    locality: ILocality | string;
    organization: Reference<IOrganization>;

    job_id: string;
    job_type: JobType;
    hourly_rate: string;
    posted_by: Reference<IRecruiter>;

    verified: boolean;

    seo_link: string;
    meta_description: string;
    meta_keywords: string[];
    no_of_applicants: number;
}

const schema = new Schema<IJob>({
    ...BaseDocumentSchema,

    title: { type: String, required: true },
    description: { type: String, required: true },
    description_html: { type: String, required: true },
    role: { type: String, required: true, },
    skills: [{
        type: {
            name: { type: String, required: true },
            description: { type: String, default: null },
            category: { type: String, required: true },
        },
        default: [],
        index: true,
    }],

    phone: { type: String, default: null },
    email: { type: String, required: true },
    locality: {
        type: {
            city: { type: String, required: true },
            state: { type: String, required: true },
            country: { type: String, default: "USA" },
            zip: { type: String },
            lat: { type: Number },
            lng: { type: Number },
        },
        required: true,
    },
    organization: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.organizations,
        required: true,
        index: true
    },

    job_id: {
        type: String,
        required: true,
        index: true,
        unique: true
    },
    job_type: {
        type: String,
        enum: JobType,
        required: true,
        index: true
    },
    hourly_rate: {
        type: String,
        required: false,
        default: null
    },
    posted_by: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.recruiters,
        required: true,
        index: true,
    },

    verified: { type: Boolean, default: false, index: true },

    meta_description: { type: String, required: true },
    meta_keywords: [{ type: String, default: null }],
    seo_link: { type: String, required: true },
    no_of_applicants: { type: Number, required: true, default: 0 },
}, { timestamps: true });

export const Job = models.jobs || model<IJob>(SchemaRef.jobs, schema);