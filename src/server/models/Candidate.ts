import { BaseDocumentSchema, IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { ISkill, IUser, SchemaRef } from ".";

export interface ICandidate extends IBaseDocument {
    user: Reference<IUser>;
    phone?: string;
    role?: string;
    skills?: Reference<ISkill>[];
}

const schema = new Schema<ICandidate>({
    ...BaseDocumentSchema,
    
    user: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.users,
        required: true,
        unique: true,
    },
    phone: { type: String, default: null },
    role: { type: String, default: null },
    skills: [{
        type: Schema.Types.ObjectId,
        ref: SchemaRef.skills,
        default: [],
    }],
}, { timestamps: true });

export const Candidate = models?.candidates || model<ICandidate>(SchemaRef.candidates, schema);