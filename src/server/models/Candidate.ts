import { BaseDocumentSchema, IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { ISkill, IUser, IPremiumSubscription, SchemaRef } from ".";

export interface ICandidate extends IBaseDocument {
    user: Reference<IUser>;
    phone?: string;
    role?: string;
    skills?: Reference<ISkill>[];
    isPremium?: boolean;
    subscriptionId?: Reference<IPremiumSubscription>;
}

const schema = new Schema<ICandidate>({
    ...BaseDocumentSchema,
    
    user: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.users,
        required: true,
        unique: true,
    },
    phone: { type: String, default: null },
    role: { type: String, default: null },
    skills: [{
        type: Schema.Types.ObjectId,
        ref: SchemaRef.skills,
        default: [],
    }],
    isPremium: {
        type: Boolean,
        default: false,
        index: true
    },
    subscriptionId: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.premiumSubscriptions,
        default: null
    },
}, { timestamps: true });

export const Candidate = models?.candidates || model<ICandidate>(SchemaRef.candidates, schema);