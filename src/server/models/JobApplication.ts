import { IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { ICandidate, IJob, IOrganization, IRecruiter, IResume, SchemaRef } from ".";
import { JobApplicationStatus } from "@/server/enums";

export interface IJobApplication extends IBaseDocument {
    job: Reference<IJob>;
    resume: Reference<IResume>;
    applicant: Reference<ICandidate>;
    recruiter: Reference<IRecruiter>;
    organization?: Reference<IOrganization>;
    status: JobApplicationStatus;
}

const schema = new Schema<IJobApplication>({
    job: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.jobs,
        required: true,
    },
    resume: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.resumes,
        required: true,
    },
    applicant: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.candidates,
        required: true,
    },
    organization: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.organizations,
        required: false,
        default: null,
    },
    recruiter: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.recruiters,
        required: true,
    },
    status: {
        type: String,
        enum: JobApplicationStatus,
        default: JobApplicationStatus.PENDING,
    },
}, { timestamps: true });

export const JobApplication = models.job_applications || model<IJobApplication>(SchemaRef.job_applications, schema);