import { BaseDocumentSchema, IBaseDocument } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { SchemaRef } from ".";

export interface ILocality extends IBaseDocument {
    city: string;
    state: string;
    country: string;
    zip: string;
    lat: number;
    lng: number;
}

const schema = new Schema<ILocality>({
    ...BaseDocumentSchema,

    city: { type: String, required: true },
    state: { type: String, required: true },
    country: { type: String, default: "USA" },
    zip: { type: String, default: null },
    lat: { type: Number, default: null },
    lng: { type: Number, default: null },
}, { timestamps: true });

schema.index({
    city: 'text',
    state: 'text',
    country: 'text',
});

export const Locality = models.localities || model(SchemaRef.localities, schema);