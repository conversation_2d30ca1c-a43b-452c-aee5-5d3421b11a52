import { BaseDocumentSchema, IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { ILocality, SchemaRef } from ".";
import { BaseGcsFileSchema, IBaseGcsFile } from "@/server/core/IBaseGcsFile";

export interface IOrganization extends IBaseDocument {
    name: string;
    description?: string;
    locality?: Reference<ILocality>;
    phone?: string;
    email?: string;
    website?: string;
    logo?: IBaseGcsFile;
}

const schema = new Schema<IOrganization>({
    ...BaseDocumentSchema,

    name: { type: String, required: true },
    description: { type: String, default: null },
    locality: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.localities,
        required: false
    },
    phone: { type: String, required: false },
    email: { type: String, required: false },
    website: { type: String, default: null },
    logo: {
        type: BaseGcsFileSchema,
        required: false,
        default: null,
    },
}, { timestamps: true });


schema.index({
    name: 'text',
    locality: 'text',
});

export const Organization = models.organizations || model<IOrganization>(SchemaRef.organizations, schema);