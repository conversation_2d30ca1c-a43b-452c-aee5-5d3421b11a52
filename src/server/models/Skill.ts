import { BaseDocumentSchema, IBaseDocument } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { SchemaRef } from ".";

export interface ISkill extends IBaseDocument {
    name: string;
    description: string;
    category: string;
}

const schema = new Schema<ISkill>({
    ...BaseDocumentSchema,

    name: {
        type: String,
        required: true,
    },
    description: { type: String, default: null },
    category: {
        type: String,
        required: true,
        index: 'text'
    },
}, { timestamps: true });

export const Skill = models.skills || model<ISkill>(SchemaRef.skills, schema);