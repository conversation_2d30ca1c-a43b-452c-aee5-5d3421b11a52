import { BaseDocumentSchema, IBaseDocument } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { SchemaRef } from ".";

export interface ISubscription extends IBaseDocument {
    email: string;
    marketing: boolean;
}

const schema = new Schema<ISubscription>({
    ...BaseDocumentSchema,

    email: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    marketing: {
        type: Boolean,
        default: true
    },
}, { timestamps: true });

export const Subscription = models.subscriptions || model<ISubscription>(SchemaRef.subscriptions, schema);
