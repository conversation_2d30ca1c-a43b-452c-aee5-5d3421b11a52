import { model, models, Schema } from "mongoose";
import { SchemaRef } from "./SchemaRef";

export interface IOldJob {
    _id: string;
    created_by: string;
    modified_by: string;
    active: boolean;
    role: string;
    company_name: string;
    hourly_rate: number;
    description: string;
    description_html: string;
    skills: string[];
    no_of_applicants: number;
    email: string;
    phone_number: string;
    job_type: string;
    city: string;
    state: string;
    job_id: string;
    seo_link: string[];
    title: string;
    meta_description: string;
    meta_keywords: string[];
    created_at: Date;
    updated_at: Date;
}

const schema = new Schema<IOldJob>({
    created_by: { type: String, required: true },
    modified_by: { type: String, required: true },
    active: { type: Boolean, default: false },
    role: { type: String, required: true },
    company_name: { type: String, required: true },
    hourly_rate: { type: Number, default: 0 },
    description: { type: String, required: true },
    description_html: { type: String, required: true },
    skills: [{ type: String }],
    no_of_applicants: { type: Number, default: 0 },
    email: { type: String, required: true },
    phone_number: { type: String, required: true },
    job_type: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    job_id: { type: String, required: true },
    seo_link: [{ type: String }],
    title: { type: String, required: true },
    meta_description: { type: String, required: true },
    meta_keywords: [{ type: String }]
}, { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } });

export const OldJob = models.old_jobs || model<IOldJob>(SchemaRef.old_jobs, schema);