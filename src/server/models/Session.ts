import { BaseDocumentSchema, IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { IUser, SchemaRef } from ".";

export interface ISession extends IBaseDocument {
    userId: Reference<IUser>;
    expires: Date;
    sessionToken: string;
}

const schema = new Schema<ISession>({
    ...BaseDocumentSchema,

    userId: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.users,
        required: true,
    },
    expires: {
        type: Date,
        required: true,
    },
    sessionToken: {
        type: String,
        required: true,
        unique: true,
    },
}, { timestamps: true });

export const Session = models.sessions || model<ISession>("sessions", schema);
