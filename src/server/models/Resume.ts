import { BaseDocumentSchema, IBaseDocument, Reference } from "@/server/core/IBaseDocument";
import { BaseGcsFileSchema, IBaseGcsFile } from "@/server/core/IBaseGcsFile";
import { model, models, Schema } from "mongoose";
import { ICandidate, SchemaRef } from ".";

export interface IResume extends IBaseDocument {
    label: string;
    user: Reference<ICandidate>;
    file: IBaseGcsFile;
}

const schema = new Schema<IResume>({
    ...BaseDocumentSchema,

    label: { type: String, required: true },
    user: {
        type: Schema.Types.ObjectId,
        ref: SchemaRef.users,
        required: true
    },
    file: { type: BaseGcsFileSchema, required: true },
}, { timestamps: true });

export const Resume = models.resumes || model<IResume>(SchemaRef.resumes, schema);