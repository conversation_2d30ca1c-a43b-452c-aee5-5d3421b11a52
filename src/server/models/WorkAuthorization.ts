import { BaseDocumentSchema, IBaseDocument } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { SchemaRef } from ".";
import { WorkAuthorizationCategory } from "@/server/enums";

export interface IWorkAuthorization extends IBaseDocument {
    name: string;
    description: string;
    category: WorkAuthorizationCategory;
}

const schema = new Schema<IWorkAuthorization>({
    ...BaseDocumentSchema,

    name: {
        type: String,
        required: true,
        unique: true,
    },
    description: {
        type: String,
        default: null
    },
    category: {
        type: String,
        enum: WorkAuthorizationCategory,
        default: WorkAuthorizationCategory.OTHER,
    },
}, { timestamps: true });

schema.index({
    name: 'text',
    description: 'text',
    category: 1
});

export const WorkAuthorization = models.work_authorizations || model<IWorkAuthorization>(SchemaRef.work_authorizations, schema);
