import { BaseDocumentSchema, IBaseDocument } from "@/server/core/IBaseDocument";
import { model, models, Schema } from "mongoose";
import { SchemaRef } from ".";

export interface IUser extends IBaseDocument {
    name?: string | null;
    email: string;
    emailVerified?: Date | null;
    image?: string | null;
}

const schema = new Schema<IUser>({
    ...BaseDocumentSchema,

    name: { type: String, default: null },
    email: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    emailVerified: { type: Date, default: null },
    image: { type: String, default: null },
}, { timestamps: true });

export const User = models.users || model<IUser>(SchemaRef.users, schema);