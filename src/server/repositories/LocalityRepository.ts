import { SearchPaginationParams } from "@/server/core/BaseQueryParams";
import BaseRepository from "@/server/core/BaseRepository";
import { ILocality, Locality } from "@/server/models";

export type QueryLocalityParams = SearchPaginationParams & {
    city: string;
    state: string;
    country: string;
}

export class LocalityRepository extends BaseRepository<ILocality, QueryLocalityParams> {
    async buildQueryParams(queryParams: QueryLocalityParams): Promise<any> {
        const query = await super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.city ? { city: queryParams.city } : {},
            ...queryParams?.state ? { state: queryParams.state } : {},
            ...queryParams?.country ? { country: queryParams.country } : {},
        }
    }

    async batchCreate(localities: ILocality[]): Promise<ILocality[]> {
        try {
            return await Locality.insertMany(localities);
        } catch (error) {
            throw new Error(`${this.model.modelName} already exists`);
        }

    }
}

export const localityRepository = new LocalityRepository(Locality);