import { PaginationParams, SortQueryParams } from "@/server/core/BaseQueryParams";
import BaseRepository from "@/server/core/BaseRepository";
import { IExternalJob, ExternalJob } from "@/server/models/ExternalJob";

export type QueryExternalJobParams = PaginationParams & SortQueryParams & {
    recruiter_email?: string;
    role?: string;
}

export class ExternalJobRepository extends BaseRepository<IExternalJob, QueryExternalJobParams> {
    constructor() {
        super(ExternalJob);
    }

    async buildQueryParams(queryParams?: QueryExternalJobParams): Promise<any> {
        const query = await super.buildQueryParams(queryParams);

        if (queryParams?.recruiter_email) {
            query.recruiter_email = { $regex: queryParams.recruiter_email, $options: 'i' };
        }

        if (queryParams?.role) {
            query.role = { $regex: queryParams.role, $options: 'i' };
        }

        return query;
    }

    async buildSortParams(queryParams?: QueryExternalJobParams): Promise<any> {
        const params = queryParams as SortQueryParams | undefined;
        if (!params) {
            // Default sort by creation date (newest first)
            return { createdAt: -1 };
        }

        return {
            ...params.created_at ? { createdAt: params.created_at === 'asc' ? 1 : -1 } : {},
            ...params.updated_at ? { updatedAt: params.updated_at === 'asc' ? 1 : -1 } : {},
        }
    }
}

export const externalJobRepository = new ExternalJobRepository();
