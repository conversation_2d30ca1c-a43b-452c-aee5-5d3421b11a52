import { SearchPaginationParams } from '@/server/core/BaseQueryParams';
import BaseRepository from "@/server/core/BaseRepository";
import { AlreadyExistsError, ErrorNotFound } from '@/server/core/Errors';
import connectToDb from '@/server/lib/db';
import { ISkill, Skill } from '@/server/models';

export type QuerySkillsParams = SearchPaginationParams & {
    category: string
}

export class SkillRepository extends BaseRepository<ISkill, QuerySkillsParams> {
    async buildQueryParams(queryParams: QuerySkillsParams): Promise<any> {
        let query = {};
        if (queryParams?.q) {
            const text = decodeURIComponent(queryParams?.q);
            const arr = text.split(/\s|-/).map((e) => e.toLowerCase()).filter((item) => item.length > 0);
            const regex = `(.*${arr.join(".*)|(.*")}.*)`;
            query = {
                name: {
                    $regex: regex,
                    $options: "i"
                }
            }
        }

        return {
            ...queryParams?.category ? {
                category: {
                    $regex: queryParams.category,
                    $options: "i"
                }
            } : {},
            ...query,
        }
    }

    async getAllOfIds(id: string[]): Promise<ISkill[]> {
        await connectToDb();
        const found = await Skill.find({ _id: { $in: id } });
        if (!found) {
            throw new ErrorNotFound(`${this.model.modelName}(${id}) not found`);
        }
        return found;
    }

    async batchCreate(dto: ISkill[]): Promise<ISkill[]> {
        await connectToDb();

        try {
            return await Skill.insertMany(dto);
        } catch (error) {
            throw new AlreadyExistsError(`${this.model.modelName} already exists`);
        }
    }
}

export const skillRepository = new SkillRepository(Skill);