import { PaginationParams } from "@/server/core/BaseQueryParams";
import BaseRepository, { PopulateParam } from "@/server/core/BaseRepository";
import { Candidate, ICandidate } from "@/server/models";

export type QueryCandidateParams = PaginationParams & {
    email: string;
}

export class CandidateRepository extends BaseRepository<ICandidate, QueryCandidateParams> {
    async buildQueryParams(queryParams?: QueryCandidateParams | undefined): Promise<any> {
        const query = await super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.email ? { email: queryParams.email } : {},
        }
    }

    protected populate: PopulateParam = [
        {
            path: 'skills',
            select: 'name category',
        },
        {
            path: 'user',
            select: 'name email',
        },
    ];

    async getByUserId(userId: string): Promise<ICandidate | undefined> {
        return await Candidate.findOne({ user: userId }).populate(this.populate);
    }

    async getAllCandidatesWithEmails(): Promise<ICandidate[]> {
        return await Candidate.find({}).populate(this.populate);
    }
}

export const candidateRepository = new CandidateRepository(Candidate);