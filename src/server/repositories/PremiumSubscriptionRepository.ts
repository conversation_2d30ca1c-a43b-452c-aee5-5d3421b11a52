import { PaginationParams } from "@/server/core/BaseQueryParams";
import BaseRepository from "@/server/core/BaseRepository";
import connectToDb from "@/server/lib/db";
import { IPremiumSubscription, PremiumSubscription } from "@/server/models";
import { SubscriptionStatus, UserScope } from "@/server/enums";

export type QueryPremiumSubscriptionParams = PaginationParams & {
    userId?: string;
    userType?: UserScope;
    status?: SubscriptionStatus;
    stripeCustomerId?: string;
    stripeSubscriptionId?: string;
}

export class PremiumSubscriptionRepository extends BaseRepository<IPremiumSubscription, QueryPremiumSubscriptionParams> {
    async buildQueryParams(queryParams?: QueryPremiumSubscriptionParams): Promise<any> {
        const query = await super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.userId ? { userId: queryParams.userId } : {},
            ...queryParams?.userType ? { userType: queryParams.userType } : {},
            ...queryParams?.status ? { status: queryParams.status } : {},
            ...queryParams?.stripeCustomerId ? { stripeCustomerId: queryParams.stripeCustomerId } : {},
            ...queryParams?.stripeSubscriptionId ? { stripeSubscriptionId: queryParams.stripeSubscriptionId } : {},
        }
    }

    /**
     * Find subscription by user ID and type
     */
    async findByUserIdAndType(userId: string, userType: UserScope): Promise<IPremiumSubscription | null> {
        await connectToDb();
        return PremiumSubscription.findOne({ userId, userType });
    }

    /**
     * Find subscription by Stripe customer ID
     */
    async findByStripeCustomerId(stripeCustomerId: string): Promise<IPremiumSubscription | null> {
        await connectToDb();
        return PremiumSubscription.findOne({ stripeCustomerId });
    }

    /**
     * Find subscription by Stripe subscription ID
     */
    async findByStripeSubscriptionId(stripeSubscriptionId: string): Promise<IPremiumSubscription | null> {
        await connectToDb();
        return PremiumSubscription.findOne({ stripeSubscriptionId });
    }

    /**
     * Find active subscription by user ID and type
     */
    async findActiveByUserIdAndType(userId: string, userType: UserScope): Promise<IPremiumSubscription | null> {
        await connectToDb();
        return PremiumSubscription.findOne({ 
            userId, 
            userType, 
            status: SubscriptionStatus.ACTIVE,
            currentPeriodEnd: { $gt: new Date() }
        });
    }

    /**
     * Find expiring subscriptions
     */
    async findExpiring(daysFromNow: number = 7): Promise<IPremiumSubscription[]> {
        await connectToDb();
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + daysFromNow);
        
        return PremiumSubscription.find({
            status: SubscriptionStatus.ACTIVE,
            currentPeriodEnd: { 
                $gte: new Date(),
                $lte: expirationDate 
            }
        }).populate('userId');
    }

    /**
     * Get subscription analytics
     */
    async getAnalytics(): Promise<{
        totalSubscriptions: number;
        activeSubscriptions: number;
        canceledSubscriptions: number;
        monthlyRevenue: number;
        subscriptionsByPlan: Record<string, number>;
    }> {
        await connectToDb();
        
        const [
            totalSubscriptions,
            activeSubscriptions,
            canceledSubscriptions,
            subscriptionsByPlan
        ] = await Promise.all([
            PremiumSubscription.countDocuments(),
            PremiumSubscription.countDocuments({ status: SubscriptionStatus.ACTIVE }),
            PremiumSubscription.countDocuments({ status: SubscriptionStatus.CANCELED }),
            PremiumSubscription.aggregate([
                { $group: { _id: '$planType', count: { $sum: 1 } } }
            ])
        ]);

        const planCounts = subscriptionsByPlan.reduce((acc: Record<string, number>, item: any) => {
            acc[item._id] = item.count;
            return acc;
        }, {});

        return {
            totalSubscriptions,
            activeSubscriptions,
            canceledSubscriptions,
            monthlyRevenue: 0, // TODO: Calculate from Stripe data
            subscriptionsByPlan: planCounts
        };
    }
}

export const premiumSubscriptionRepository = new PremiumSubscriptionRepository(PremiumSubscription);
