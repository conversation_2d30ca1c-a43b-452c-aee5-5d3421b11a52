import { SearchPaginationParams, SortQueryParams } from "@/server/core/BaseQueryParams";
import BaseRepository, { PopulateParam } from "@/server/core/BaseRepository";
import { ErrorNotFound, ImmutableObjectError } from "@/server/core/Errors";
import { IJobApplication, IOrganization, IRecruiter, JobApplication } from "@/server/models";
import { jobService } from "@/server/services";

export type QueryJobApplicationParams = SearchPaginationParams & SortQueryParams & {
    job: string;
    applicant: string;
    recruiter: string;
    organization: string;
};

export class JobApplicationRepository extends BaseRepository<IJobApplication, QueryJobApplicationParams> {
    protected populate: PopulateParam = [
        {
            path: 'organization',
            select: 'name logo website',
        },
        {
            path: 'job',
            select: 'title role locality',
        },
        {
            path: 'resume',
            select: 'file',
        },
        {
            path: 'applicant',
            select: 'phone user',
            populate: {
                path: 'user',
                select: 'name email',
            },
        },
        {
            path: 'recruiter',
            select: 'phone user',
            populate: {
                path: 'user',
                select: 'name email',
            },
        },
    ]

    buildQueryParams(queryParams?: QueryJobApplicationParams): Promise<QueryJobApplicationParams> {
        const query = super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.job ? { job: queryParams.job } : {},
            ...queryParams?.applicant ? { applicant: queryParams.applicant } : {},
            ...queryParams?.recruiter ? { recruiter: queryParams.recruiter } : {},
            ...queryParams?.organization ? { organization: queryParams.organization } : {},
        };
    }

    async create(dto: IJobApplication): Promise<IJobApplication> {
        const job = await jobService.getById(dto.job.toString());
        if (!job) {
            throw new ErrorNotFound("Job not found");
        }

        dto = {
            ...dto,
            organization: (job.organization as IOrganization)._id,
            recruiter: (job.posted_by as IRecruiter)._id,
        } as IJobApplication;

        return super.create(dto);
    }

    async update(id: string, data: Partial<IJobApplication>): Promise<IJobApplication> {
        // Only allow status updates
        if (Object.keys(data).some(key => key !== 'status')) {
            throw new ImmutableObjectError("Only job application status can be updated");
        }

        const application = await JobApplication.findByIdAndUpdate(
            id,
            { status: data.status },
            { new: true }
        ).populate(this.populate);

        if (!application) {
            throw new ErrorNotFound(`Job application(${id}) not found`);
        }

        return application;
    }

    async delete(_id: string): Promise<IJobApplication> {
        throw new ImmutableObjectError("Job application is immutable");
    }

    /**
     * Get all job applications for a specific job
     *
     * @param jobId Job ID
     * @returns List of job applications with populated applicant and user data
     */
    async getApplicationsByJobId(jobId: string): Promise<IJobApplication[]> {
        return await JobApplication.find({ job: jobId })
            .populate(this.populate)
            .exec();
    }
}

export const jobApplicationRepository = new JobApplicationRepository(JobApplication);