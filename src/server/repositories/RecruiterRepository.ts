import { SearchPaginationParams } from "@/server/core/BaseQueryParams";
import BaseRepository, { PopulateParam } from "@/server/core/BaseRepository";
import { IRecruiter, Recruiter } from "@/server/models";

export type QueryRecruiterParams = SearchPaginationParams & {
    organization: string;
}

export class RecruiterRepository extends BaseRepository<IRecruiter, QueryRecruiterParams> {
    async buildQueryParams(queryParams?: QueryRecruiterParams | undefined): Promise<any> {
        const query = await super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.organization ? { organization: queryParams.organization } : {},
        }
    }

    protected populate: PopulateParam = [
        {
            path: 'organization',
            select: 'name description logo locality',
            populate: {
                path: 'locality',
                select: 'city state country',
            }
        },
        {
            path: 'user',
            select: 'name email',
        },
    ];

    async getByUserId(userId: string): Promise<IRecruiter | undefined> {
        return await Recruiter.findOne({ user: userId }).populate(this.populate);
    }
}

export const recruiterRepository = new RecruiterRepository(Recruiter);