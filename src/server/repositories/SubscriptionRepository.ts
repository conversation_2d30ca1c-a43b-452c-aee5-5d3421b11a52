import { PaginationParams } from "@/server/core/BaseQueryParams";
import BaseRepository from "@/server/core/BaseRepository";
import connectToDb from "@/server/lib/db";
import { ISubscription, Subscription } from "@/server/models";

export type QuerySubscriptionParams = PaginationParams & {
    email: string;
}

export class SubscriptionRepository extends BaseRepository<ISubscription, QuerySubscriptionParams> {
    async buildQueryParams(queryParams?: QuerySubscriptionParams): Promise<any> {
        const query = await super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.email ? { email: queryParams.email } : {},
        }
    }

    /**
     * Find a subscription by email
     * 
     * @param email Email to search for
     * @returns Subscription if found, undefined otherwise
     */
    async findByEmail(email: string): Promise<ISubscription | null> {
        await connectToDb();
        return Subscription.findOne({ email });
    }
}

export const subscriptionRepository = new SubscriptionRepository(Subscription);
