import { SearchPaginationParams } from '@/server/core/BaseQueryParams';
import BaseRepository from "@/server/core/BaseRepository";
import { IWorkAuthorization, WorkAuthorization } from '@/server/models';

export type QueryWorkAuthorizationParams = SearchPaginationParams & {
    name: string;
}

export class WorkAuthorizationRepository extends BaseRepository<IWorkAuthorization, QueryWorkAuthorizationParams> {
}

export const workAuthorizationRepository = new WorkAuthorizationRepository(WorkAuthorization);
