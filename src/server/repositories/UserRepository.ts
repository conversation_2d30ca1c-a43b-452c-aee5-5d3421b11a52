import { PaginationParams } from "@/server/core/BaseQueryParams";
import BaseRepository from "@/server/core/BaseRepository";
import { IUser, User } from "@/server/models";

export type QueryUserParams = PaginationParams & {
    email: string;
}

export class UserRepository extends BaseRepository<IUser, QueryUserParams> {
    async buildQueryParams(queryParams: QueryUserParams): Promise<any> {
        const query: any = await super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.email ? { email: queryParams.email } : {},
        }
    }
}

export const userRepository = new UserRepository(User);