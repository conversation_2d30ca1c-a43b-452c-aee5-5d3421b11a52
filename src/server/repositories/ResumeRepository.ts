import { SearchPaginationParams } from "@/server/core/BaseQueryParams";
import BaseRepository from "@/server/core/BaseRepository";
import { IResume, Resume } from "@/server/models";

export type QueryResumeParams = SearchPaginationParams & {
    user: string
}

export class ResumeRepository extends BaseRepository<IResume, QueryResumeParams> {
    async buildQueryParams(queryParams: QueryResumeParams): Promise<any> {
        const query = await super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.user ? { user: queryParams.user } : {},
        }
    }
}

export const resumeRepository = new ResumeRepository(Resume);