import { IO<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/server/models";
import connectToDb from "@/server/lib/db";

export class OldJobRepository {
    async getOldJobs(): Promise<IOldJob[]> {
        await connectToDb();

        const jobs = await OldJob.find();
        if (!jobs) {
            throw new Error('Failed to get jobs');
        }
        return jobs;
    }

    async getOldJobById(id: string): Promise<IOldJob> {
        await connectToDb();

        const job = await OldJob.findOne({ job_id: id });
        if (!job) {
            throw new Error(`Failed to get job with id: ${id}`);
        }
        return job;
    }
}