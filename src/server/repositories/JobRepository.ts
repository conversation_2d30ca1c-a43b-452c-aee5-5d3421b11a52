import { calculatePage, PaginationParams, SortQueryParams } from "@/server/core/BaseQueryParams";
import BaseRepository, { PopulateParam, QueryResult } from "@/server/core/BaseRepository";
import { ErrorNotFound } from "@/server/core/Errors";
import connectToDb from "@/server/lib/db";
import { IJob, ILocality, ISkill, Job, Candidate, ICandidate } from "@/server/models";
import { localityService, organizationService, skillService } from "@/server/services";
import { generateJobMetadatas } from "@/server/utils/generateJobMetadata";
import { PipelineStage } from "mongoose";

export type QueryJobParams = PaginationParams & SortQueryParams & {
    job_id: string
    organization: string
    posted_by: string,
    verified: boolean,
    active: boolean,
}

export type SearchJobParams = PaginationParams & {
    query: string,
    fuzzy: boolean,
    filter: {
        organization: string,
        posted_by: string,
        job_type: string,
        locality: string,
        verified: boolean,
        active: boolean,
    }
}

export type SimilarJobsParams = PaginationParams & {
    mmp: number, // min_match_preference
    lp: boolean, // location_preference
    verified?: boolean, // verified jobs filter (undefined = ignore filter)
}

export type SkillBasedRecommendationParams = PaginationParams & {
    skills: string, // comma-separated skill names or IDs (empty = latest jobs)
    mmp: number, // min_match_preference
    lp?: string, // location_preference (city,state format)
    jt?: string, // job_type
    verified?: boolean, // verified jobs filter (undefined = ignore filter)
}

export type CandidateRecommendationParams = PaginationParams & {
    mmp: number, // min_match_preference
    lp: boolean, // location_preference (prioritize candidate's location)
    jt?: string, // job_type
    verified?: boolean, // verified jobs filter (undefined = ignore filter)
}

export class JobRepository extends BaseRepository<IJob, QueryJobParams> {
    protected populate: PopulateParam = [
        {
            path: 'organization',
            select: 'name logo website',
        },
        {
            path: 'posted_by',
            select: 'phone',
            populate: {
                path: 'user',
                select: 'name email image',
            }
        }
    ]

    async buildQueryParams(queryParams: QueryJobParams): Promise<any> {
        const query = await super.buildQueryParams(queryParams);

        return {
            ...query,
            ...queryParams?.job_id ? {
                job_id: {
                    $regex: queryParams.job_id,
                    $options: "i"
                }
            } : {},
            ...queryParams?.organization ? { organization: queryParams.organization } : {},
            ...queryParams?.posted_by ? { posted_by: queryParams.posted_by } : {},
            ...queryParams?.active != undefined ? { active: queryParams.active } : {},
            ...queryParams?.verified != undefined ? { verified: queryParams.verified } : {},
        }
    }

    async buildSortParams(queryParams?: QueryJobParams | undefined): Promise<any> {
        const params = queryParams as SortQueryParams | undefined;
        if (!params) return {};

        return {
            ...params.created_at ? { createdAt: params.created_at === 'asc' ? 1 : -1 } : {},
            ...params.updated_at ? { updatedAt: params.updated_at === 'asc' ? 1 : -1 } : {},
        }
    }

    async create(dto: IJob): Promise<IJob> {
        await connectToDb();

        if (dto.skills && dto.skills.length > 0) {
            const skills = await skillService.getAllOfIds(dto.skills as string[]);
            dto.skills = skills;
        }

        if (dto.locality) {
            const locality = await localityService.getById(dto.locality as string);
            if (!locality) {
                throw new ErrorNotFound("Locality not found");
            }
            dto.locality = locality;
        }

        const total = await Job.countDocuments();
        const job_id = `C2C-${total + 1}`;

        const organizationId = dto.organization.toString();
        const organization = await organizationService.getById(organizationId);
        if (!organization) {
            throw new ErrorNotFound('Organization not found');
        }

        const metadatas = await generateJobMetadatas(
            job_id,
            dto.role,
            dto.job_type,
            organization,
            dto.skills as ISkill[],
            dto.locality as ILocality,
        );

        const job: IJob = {
            ...dto,
            job_id,
            ...metadatas,
        }

        return super.create(job);
    }

    async update(id: string, dto: IJob): Promise<IJob> {
        await connectToDb();

        if (dto.skills && dto.skills.length > 0) {
            const skills = await skillService.getAllOfIds(dto.skills as string[]);
            dto.skills = skills;
        }

        if (dto.locality) {
            const locality = await localityService.getById(dto.locality as string);
            if (!locality) {
                throw new ErrorNotFound("Locality not found");
            }
            dto.locality = locality;
        }

        let job = await Job.findById(id);
        if (!job) {
            throw new ErrorNotFound('Job not found');
        }

        const hasRoleChanged = dto.role && dto.role != job.role;
        const hasLocalityChanged = dto.locality && dto.locality != job.locality;
        const hasOrganizationChanged = dto.organization && dto.organization != job.organization;
        const hasJobTypeChanged = dto.job_type && dto.job_type != job.job_type;

        if (hasRoleChanged || hasLocalityChanged || hasOrganizationChanged || hasJobTypeChanged) {
            const updatedJob = Object.assign(job, dto);
            const metadatas = await generateJobMetadatas(
                job.job_id,
                updatedJob.role,
                updatedJob.job_type,
                updatedJob.organization,
                updatedJob.skills,
                updatedJob.locality,
            );
            job = {
                ...metadatas,
                ...dto,
            }

        } else {
            job = dto;
        }

        return super.update(id, job);
    }

    /**
     * Updates the no_of_applicants field of the job by incrementing it by 1
     *
     * @param id
     */
    async incrementApplicantsCount(id: string) {
        await connectToDb();
        await Job.findByIdAndUpdate(id, { $inc: { no_of_applicants: 1 } });
    }

    /**
     * Closes a job by setting its active status to false
     *
     * @param id Job ID
     * @returns Updated job
     */
    async closeJob(id: string): Promise<IJob> {
        await connectToDb();

        const job = await Job.findByIdAndUpdate(
            id,
            { active: false },
            { new: true }
        ).populate(this.populate);

        if (!job) {
            throw new ErrorNotFound('Job not found');
        }

        return job;
    }

    /**
     * Finds all active jobs older than the specified number of days
     *
     * @param days Number of days to look back
     * @returns Array of jobs older than specified days
     */
    async findJobsOlderThan(days: number): Promise<IJob[]> {
        await connectToDb();

        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        const jobs = await Job.find({
            active: true,
            createdAt: { $lt: cutoffDate }
        }).populate(this.populate);

        return jobs;
    }

    private lookup(collection: string, localField: string, projection: {}): any[] {
        return [
            {
                $lookup: {
                    from: collection,
                    let: { mongoId: `$${localField}` },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ["$_id", `$$mongoId`] }
                            }
                        },
                        {
                            $project: projection
                        }
                    ],
                    as: localField,
                }
            },
            {
                $unwind: {
                    path: `$${localField}`,
                    preserveNullAndEmptyArrays: true
                }
            }
        ];
    }

    async search(params: SearchJobParams): Promise<QueryResult<IJob>> {
        await connectToDb();

        const searchStage = {
            $search: {
                index: "default",
                text: {
                    query: params.query,
                    path: "meta_keywords",
                    ...(params.fuzzy === true ? { fuzzy: {} } : {}),
                }
            }
        };

        let matchStage: PipelineStage | undefined = undefined;
        const filter = params.filter;
        if (filter) {
            matchStage = {
                $match: {
                    $and: [
                        ...filter.organization ? [{ organization: filter.organization }] : [],
                        ...filter.posted_by ? [{ posted_by: filter.posted_by }] : [],
                        ...filter.locality ? [{ locality: filter.locality }] : [],
                        ...filter.job_type ? [{ job_type: filter.job_type }] : [],
                        ...filter.verified ? [{ verified: filter.verified }] : [],
                        ...filter.active ? [{ active: filter.active }] : [],
                    ]
                }
            }
        }

        let limit = params.limit || 20;
        const page = calculatePage(params) || 0;
        const facetStage = {
            $facet: {
                total: [
                    { $count: "count" }
                ],
                data: [
                    { $limit: limit },
                    { $skip: page }
                ]
            }
        };

        const populateStage = [
            ...this.lookup('organizations', 'organization', {
                name: 1,
                logo: 1,
                website: 1
            }),
            ...this.lookup('recruiters', 'posted_by', {
                phone: 1,
                user: 1
            }),
            ...this.lookup('users', 'posted_by.user', {
                name: 1,
                email: 1,
                image: 1
            }),
        ]

        const pipeline = [
            searchStage,
            ...(matchStage ? [matchStage] : []),
            ...populateStage,
            facetStage,
        ];

        const result = await Job.aggregate(pipeline);
        return {
            total: result[0].total.count,
            items: result[0].data,
            page: page,
            count: limit,
        }
    }

    async findSimilarJobs(jobId: string, params: SimilarJobsParams): Promise<QueryResult<any>> {
        await connectToDb();

        // First, get the source job to extract its skills
        const sourceJob = await Job.findById(jobId);
        if (!sourceJob) {
            throw new ErrorNotFound('Job not found');
        }

        const sourceSkills = sourceJob.skills as ISkill[];
        const sourceSkillNames = sourceSkills.map(skill => skill.name);

        if (sourceSkillNames.length === 0) {
            return {
                total: 0,
                items: [],
                page: params.page || 1,
                count: 0,
            };
        }

        const limit = params.limit || 10;
        const page = calculatePage(params) || 0;

        // Build the aggregation pipeline
        const pipeline: PipelineStage[] = [
            // Match active jobs, exclude the source job itself
            {
                $match: {
                    _id: { $ne: sourceJob._id },
                    active: true,
                    ...(params.verified !== undefined ? { verified: params.verified } : {})
                }
            },
            // Add skill matching calculations with fuzzy regex matching
            {
                $addFields: {
                    match_info: {
                        matched_skills: {
                            $filter: {
                                input: "$skills",
                                cond: {
                                    $anyElementTrue: {
                                        $map: {
                                            input: sourceSkillNames,
                                            as: "searchSkill",
                                            in: {
                                                $regexMatch: {
                                                    input: "$$this.name",
                                                    regex: "$$searchSkill",
                                                    options: "i"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $addFields: {
                    "match_info.matched_skills_count": { $size: "$match_info.matched_skills" },
                    "match_info.total_skills_count": { $size: "$skills" },
                    "match_info.match_percentage": {
                        $cond: {
                            if: { $gt: [{ $size: "$skills" }, 0] },
                            then: {
                                $multiply: [
                                    { $divide: [{ $size: "$match_info.matched_skills" }, { $size: "$skills" }] },
                                    100
                                ]
                            },
                            else: 0
                        }
                    }
                }
            },
            // Filter by minimum match percentage
            {
                $match: {
                    "match_info.match_percentage": { $gte: params.mmp }
                }
            },
            // Add location scoring if location preference is enabled
            ...(params.lp ? [{
                $addFields: {
                    "match_info.location_score": {
                        $cond: {
                            if: {
                                $and: [
                                    { $eq: ["$locality.city", sourceJob.locality.city] },
                                    { $eq: ["$locality.state", sourceJob.locality.state] }
                                ]
                            },
                            then: 10,
                            else: 0
                        }
                    }
                }
            }] : []),
            // Calculate final score
            {
                $addFields: {
                    "match_info.final_score": {
                        $add: [
                            "$match_info.match_percentage",
                            ...(params.lp ? ["$match_info.location_score"] : [0])
                        ]
                    }
                }
            },
            // Sort by final score (descending) and creation date (descending)
            {
                $sort: {
                    "match_info.final_score": -1,
                    createdAt: -1
                }
            },
            // Populate related data
            ...this.lookup('organizations', 'organization', {
                name: 1,
                logo: 1,
                website: 1
            }),
            ...this.lookup('recruiters', 'posted_by', {
                phone: 1,
                user: 1
            }),
            ...this.lookup('users', 'posted_by.user', {
                name: 1,
                email: 1,
                image: 1
            }),
            // Facet for pagination and total count
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $skip: page },
                        { $limit: limit }
                    ]
                }
            }
        ];

        const result = await Job.aggregate(pipeline);
        const totalCount = result[0].total.length > 0 ? result[0].total[0].count : 0;

        return {
            total: totalCount,
            items: result[0].data,
            page: params.page || 1,
            count: result[0].data.length,
        };
    }

    async recommendJobsBySkills(params: SkillBasedRecommendationParams): Promise<QueryResult<any>> {
        await connectToDb();

        // Parse skills from comma-separated string
        const skillNames = params.skills.split(',').map(skill => skill.trim()).filter(Boolean);

        // If no skills provided, return latest 5 jobs
        if (skillNames.length === 0) {
            const limit = 5;
            const page = calculatePage(params) || 0;

            const pipeline: PipelineStage[] = [
                {
                    $match: {
                        active: true
                    }
                },
                {
                    $sort: {
                        createdAt: -1
                    }
                },
                // Populate related data
                ...this.lookup('organizations', 'organization', {
                    name: 1,
                    logo: 1,
                    website: 1
                }),
                ...this.lookup('recruiters', 'posted_by', {
                    phone: 1,
                    user: 1
                }),
                ...this.lookup('users', 'posted_by.user', {
                    name: 1,
                    email: 1,
                    image: 1
                }),
                {
                    $facet: {
                        total: [{ $count: "count" }],
                        data: [
                            { $skip: page },
                            { $limit: limit }
                        ]
                    }
                }
            ];

            const result = await Job.aggregate(pipeline);
            const totalCount = result[0].total.length > 0 ? result[0].total[0].count : 0;

            return {
                total: totalCount,
                items: result[0].data,
                page: params.page || 1,
                count: result[0].data.length,
            };
        }

        const limit = params.limit || 20;
        const page = calculatePage(params) || 0;

        // Build match conditions - only include filters that are provided
        const matchConditions: any[] = [
            { active: true }
        ];

        // Only add verified filter if explicitly provided
        if (params.verified !== undefined) {
            matchConditions.push({ verified: params.verified });
        }

        // Add job type filter if provided
        if (params.jt) {
            matchConditions.push({ job_type: params.jt });
        }

        // Add location filter if provided
        if (params.lp) {
            const [city, state] = params.lp.split(',').map(s => s.trim());
            if (city && state) {
                matchConditions.push({
                    "locality.city": { $regex: city, $options: "i" },
                    "locality.state": { $regex: state, $options: "i" }
                });
            }
        }

        // Build the aggregation pipeline
        const pipeline: PipelineStage[] = [
            // Match active jobs with filters
            {
                $match: {
                    $and: matchConditions
                }
            },
            // Add skill matching calculations with fuzzy regex matching
            {
                $addFields: {
                    match_info: {
                        matched_skills: {
                            $filter: {
                                input: "$skills",
                                cond: {
                                    $anyElementTrue: {
                                        $map: {
                                            input: skillNames,
                                            as: "searchSkill",
                                            in: {
                                                $regexMatch: {
                                                    input: "$$this.name",
                                                    regex: "$$searchSkill",
                                                    options: "i"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $addFields: {
                    "match_info.matched_skills_count": { $size: "$match_info.matched_skills" },
                    "match_info.total_skills_count": { $size: "$skills" },
                    "match_info.match_percentage": {
                        $cond: {
                            if: { $gt: [{ $size: "$skills" }, 0] },
                            then: {
                                $multiply: [
                                    { $divide: [{ $size: "$match_info.matched_skills" }, { $size: "$skills" }] },
                                    100
                                ]
                            },
                            else: 0
                        }
                    },
                    "match_info.final_score": {
                        $cond: {
                            if: { $gt: [{ $size: "$skills" }, 0] },
                            then: {
                                $multiply: [
                                    { $divide: [{ $size: "$match_info.matched_skills" }, { $size: "$skills" }] },
                                    100
                                ]
                            },
                            else: 0
                        }
                    }
                }
            },
            // Filter by minimum match percentage
            {
                $match: {
                    "match_info.match_percentage": { $gte: params.mmp }
                }
            },
            // Sort by final score (descending) and creation date (descending)
            {
                $sort: {
                    "match_info.final_score": -1,
                    createdAt: -1
                }
            },
            // Populate related data
            ...this.lookup('organizations', 'organization', {
                name: 1,
                logo: 1,
                website: 1
            }),
            ...this.lookup('recruiters', 'posted_by', {
                phone: 1,
                user: 1
            }),
            ...this.lookup('users', 'posted_by.user', {
                name: 1,
                email: 1,
                image: 1
            }),
            // Facet for pagination and total count
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $skip: page },
                        { $limit: limit }
                    ]
                }
            }
        ];

        const result = await Job.aggregate(pipeline);
        const totalCount = result[0].total.length > 0 ? result[0].total[0].count : 0;

        return {
            total: totalCount,
            items: result[0].data,
            page: params.page || 1,
            count: result[0].data.length,
        };
    }

    async recommendJobsForCandidate(candidateId: string, params: CandidateRecommendationParams): Promise<QueryResult<any>> {
        await connectToDb();

        // First, get the candidate and their skills
        const candidate = await Candidate.findById(candidateId).populate({
            path: 'skills',
            select: 'name category'
        }).populate({
            path: 'user',
            select: 'name email'
        });

        if (!candidate) {
            throw new ErrorNotFound('Candidate not found');
        }

        const candidateSkills = candidate.skills as ISkill[];
        const candidateSkillNames = candidateSkills.map(skill => skill.name);

        // If candidate has no skills, return latest 5 jobs
        if (candidateSkillNames.length === 0) {
            const limit = 5;
            const page = calculatePage(params) || 0;

            const pipeline: PipelineStage[] = [
                {
                    $match: {
                        active: true
                    }
                },
                {
                    $sort: {
                        createdAt: -1
                    }
                },
                // Populate related data
                ...this.lookup('organizations', 'organization', {
                    name: 1,
                    logo: 1,
                    website: 1
                }),
                ...this.lookup('recruiters', 'posted_by', {
                    phone: 1,
                    user: 1
                }),
                ...this.lookup('users', 'posted_by.user', {
                    name: 1,
                    email: 1,
                    image: 1
                }),
                {
                    $facet: {
                        total: [{ $count: "count" }],
                        data: [
                            { $skip: page },
                            { $limit: limit }
                        ]
                    }
                }
            ];

            const result = await Job.aggregate(pipeline);
            const totalCount = result[0].total.length > 0 ? result[0].total[0].count : 0;

            return {
                total: totalCount,
                items: result[0].data,
                page: params.page || 1,
                count: result[0].data.length,
            };
        }

        const limit = params.limit || 20;
        const page = calculatePage(params) || 0;

        // Build match conditions - only include filters that are provided
        const matchConditions: any[] = [
            { active: true }
        ];

        // Only add verified filter if explicitly provided
        if (params.verified !== undefined) {
            matchConditions.push({ verified: params.verified });
        }

        // Add job type filter if provided
        if (params.jt) {
            matchConditions.push({ job_type: params.jt });
        }

        // Build the aggregation pipeline
        const pipeline: PipelineStage[] = [
            // Match active jobs with filters
            {
                $match: {
                    $and: matchConditions
                }
            },
            // Add skill matching calculations with fuzzy regex matching
            {
                $addFields: {
                    match_info: {
                        matched_skills: {
                            $filter: {
                                input: "$skills",
                                cond: {
                                    $anyElementTrue: {
                                        $map: {
                                            input: candidateSkillNames,
                                            as: "searchSkill",
                                            in: {
                                                $regexMatch: {
                                                    input: "$$this.name",
                                                    regex: "$$searchSkill",
                                                    options: "i"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $addFields: {
                    "match_info.matched_skills_count": { $size: "$match_info.matched_skills" },
                    "match_info.total_skills_count": { $size: "$skills" },
                    "match_info.match_percentage": {
                        $cond: {
                            if: { $gt: [{ $size: "$skills" }, 0] },
                            then: {
                                $multiply: [
                                    { $divide: [{ $size: "$match_info.matched_skills" }, { $size: "$skills" }] },
                                    100
                                ]
                            },
                            else: 0
                        }
                    }
                }
            },
            // Filter by minimum match percentage
            {
                $match: {
                    "match_info.match_percentage": { $gte: params.mmp }
                }
            },
            // Add location scoring if location preference is enabled
            ...(params.lp ? [{
                $addFields: {
                    "match_info.location_score": {
                        $cond: {
                            if: {
                                $and: [
                                    { $eq: ["$locality.city", (candidate.user as any)?.locality?.city] },
                                    { $eq: ["$locality.state", (candidate.user as any)?.locality?.state] }
                                ]
                            },
                            then: 10,
                            else: 0
                        }
                    }
                }
            }] : []),
            // Calculate final score
            {
                $addFields: {
                    "match_info.final_score": {
                        $add: [
                            "$match_info.match_percentage",
                            ...(params.lp ? ["$match_info.location_score"] : [0])
                        ]
                    }
                }
            },
            // Sort by final score (descending) and creation date (descending)
            {
                $sort: {
                    "match_info.final_score": -1,
                    createdAt: -1
                }
            },
            // Populate related data
            ...this.lookup('organizations', 'organization', {
                name: 1,
                logo: 1,
                website: 1
            }),
            ...this.lookup('recruiters', 'posted_by', {
                phone: 1,
                user: 1
            }),
            ...this.lookup('users', 'posted_by.user', {
                name: 1,
                email: 1,
                image: 1
            }),
            // Facet for pagination and total count
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $skip: page },
                        { $limit: limit }
                    ]
                }
            }
        ];

        const result = await Job.aggregate(pipeline);
        const totalCount = result[0].total.length > 0 ? result[0].total[0].count : 0;

        return {
            total: totalCount,
            items: result[0].data,
            page: params.page || 1,
            count: result[0].data.length,
        };
    }
}

export const jobRepository = new JobRepository(Job);