import { SearchPaginationParams } from "@/server/core/BaseQueryParams";
import BaseRepository, { PopulateParam } from "@/server/core/BaseRepository";
import { IOrganization, Organization } from "@/server/models";

export type QueryOrganizationParams = SearchPaginationParams & {
    name: string;
    locality: string;
}

export class OrganizationRepository extends BaseRepository<IOrganization, QueryOrganizationParams> {
    async buildQueryParams(queryParams: QueryOrganizationParams): Promise<any> {
        const query = await super.buildQueryParams(queryParams);
        return {
            ...query,
            ...queryParams?.name ? { name: queryParams.name } : {},
            ...queryParams?.locality ? { locality: queryParams.locality } : {},
        }
    }

    protected populate: PopulateParam = {
        path: 'locality',
        select: 'city state country',
    }
}

export const organizationRepository = new OrganizationRepository(Organization);