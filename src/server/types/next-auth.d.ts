import { DefaultSession, DefaultUser } from "next-auth";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, IR<PERSON>ruiter } from "@/server/models";

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      id: string;
      candidateProfile?: {
        id: string;
        phone?: string;
        role?: string;
        skills?: any[];
      } | null;
      recruiterProfile?: {
        id: string;
        phone?: string;
        organization?: any;
      } | null;
    } & DefaultSession["user"];
  }

  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User extends DefaultUser {
    candidateProfile?: {
      id: string;
      phone?: string;
      role?: string;
      skills?: any[];
    } | null;
    recruiterProfile?: {
      id: string;
      phone?: string;
      organization?: any;
    } | null;
  }
}

// Extend AdapterUser to include our custom fields
declare module "@auth/core/adapters" {
  interface AdapterUser {
    candidateProfile?: {
      id: string;
      phone?: string;
      role?: string;
      skills?: any[];
    } | null;
    recruiterProfile?: {
      id: string;
      phone?: string;
      organization?: any;
    } | null;
  }
}
