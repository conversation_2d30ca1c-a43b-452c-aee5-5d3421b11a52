import PQueue from 'p-queue';
import { EmailOptions, emailService } from '@/server/services/EmailService';
// Queue configuration
const EMAIL_CONCURRENCY = Number(process.env.EMAIL_QUEUE_CONCURRENCY) || 5;
const EMAIL_INTERVAL = Number(process.env.EMAIL_QUEUE_INTERVAL) || 1000; // 1 second
const EMAIL_RETRY_COUNT = Number(process.env.EMAIL_QUEUE_RETRY_COUNT) || 3;

// Queue statistics
interface QueueStats {
  pending: number;
  queued: number;
  completed: number;
  failed: number;
  retries: number;
}

// Email job with retry information
interface EmailJob {
  options: EmailOptions;
  retries: number;
  createdAt: Date;
}

/**
 * Email Queue Service
 *
 * Manages a queue for sending emails to prevent overwhelming the email server
 * and to handle retries for failed emails.
 */
class EmailQueueService {
  private queue: PQueue;
  private stats: QueueStats = {
    pending: 0,
    queued: 0,
    completed: 0,
    failed: 0,
    retries: 0
  };

  constructor() {
    // Initialize the queue with concurrency and interval options
    this.queue = new PQueue({
      concurrency: EMAIL_CONCURRENCY,
      interval: EMAIL_INTERVAL,
      intervalCap: EMAIL_CONCURRENCY,
      autoStart: true
    });

    // Set up event listeners for queue events
    this.setupEventListeners();
  }

  /**
   * Add an email to the queue
   *
   * @param options Email options
   * @returns Promise that resolves when the email is added to the queue
   */
  async addToQueue(options: EmailOptions): Promise<void> {
    const job: EmailJob = {
      options,
      retries: 0,
      createdAt: new Date()
    };

    this.stats.queued++;

    // Add the job to the queue
    await this.queue.add(() => this.processEmailJob(job));
  }

  /**
   * Process an email job from the queue
   *
   * @param job Email job to process
   * @returns Promise that resolves when the email is sent or fails
   */
  private async processEmailJob(job: EmailJob): Promise<void> {
    this.stats.pending++;

    try {
      // Attempt to send the email
      await emailService.sendEmail(job.options);

      // Email sent successfully
      this.stats.completed++;
    } catch (error) {
      // Handle retry logic
      if (job.retries < EMAIL_RETRY_COUNT) {
        job.retries++;
        this.stats.retries++;

        console.log(`Retrying email (${job.retries}/${EMAIL_RETRY_COUNT}):`, error);

        // Add the job back to the queue with exponential backoff
        const backoffTime = Math.pow(2, job.retries) * 1000; // 2^retries seconds

        setTimeout(() => {
          this.queue.add(() => this.processEmailJob(job));
        }, backoffTime);
      } else {
        // Max retries reached, mark as failed
        this.stats.failed++;
        console.error('Email sending failed after max retries:', error);
        console.error('Failed email:', job.options);
      }
    } finally {
      this.stats.pending--;
    }
  }

  /**
   * Get the current queue statistics
   *
   * @returns Current queue statistics
   */
  getStats(): QueueStats {
    return {
      ...this.stats,
      pending: this.queue.pending
    };
  }

  /**
   * Pause the queue
   */
  pause(): void {
    this.queue.pause();
  }

  /**
   * Resume the queue
   */
  resume(): void {
    this.queue.start();
  }

  /**
   * Clear the queue
   */
  clear(): void {
    this.queue.clear();
  }

  /**
   * Set up event listeners for queue events
   */
  private setupEventListeners(): void {
    this.queue.on('active', () => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`Working on email. Queue size: ${this.queue.size}, Pending: ${this.queue.pending}`);
      }
    });

    this.queue.on('idle', () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Email queue is idle. All emails have been processed.');
      }
    });

    this.queue.on('error', (error) => {
      console.error('Email queue error:', error);
    });
  }
}

// Create a singleton instance of the email queue service
export const emailQueueService = new EmailQueueService();

/**
 * Queue an email to be sent
 *
 * @param options Email options
 * @returns Promise that resolves when the email is added to the queue
 */
export async function queueEmail(options: EmailOptions): Promise<void> {
  return emailQueueService.addToQueue(options);
}
