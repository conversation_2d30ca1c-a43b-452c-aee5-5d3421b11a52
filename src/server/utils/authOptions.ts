import { NextAuthConfig } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import LinkedIn from "next-auth/providers/linkedin";
import { cookies } from "next/headers";
import { MongoDBAdapter } from "@/server/lib/mongodb-adapter";

const adapter = MongoDBAdapter();

export const authOptions: NextAuthConfig = {
    secret: process.env.NEXTAUTH_SECRET,
    adapter,
    providers: [
        GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            allowDangerousEmailAccountLinking: true,
        }),
        LinkedIn({
            clientId: process.env.LINKEDIN_CLIENT_ID,
            clientSecret: process.env.LINKEDIN_CLIENT_SECRET,
            allowDangerousEmailAccountLinking: true,
        })
    ],
    callbacks: {
        async session({ session, user }) {
            if (session.user) {
                session.user.id = user.id;

                // Include candidate profile if it exists
                if (user.candidateProfile) {
                    session.user.candidateProfile = user.candidateProfile;
                }

                // Include recruiter profile if it exists
                if (user.recruiterProfile) {
                    session.user.recruiterProfile = user.recruiterProfile;
                }
            }

            return session;
        },
    },
    events: {
        async signOut() {
            const c = await cookies();
            c.delete('x-c2c-token');

            if (typeof window !== 'undefined') {
                document.cookie = 'x-c2c-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            }
        },
    },
};