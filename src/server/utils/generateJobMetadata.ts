import { JobType } from "@/server/enums";
import { ILocality, IOrganization, ISkill } from "@/server/models";

const metaDescritpionTemplate = 'Find %jobType% high-paying %role% C2C (Corp-to-Corp) jobs %locality% with top companies like %company%. Required skills: %skills%. Apply now for the best contract opportunities!';

const generateJobTitle = (
    jobType: JobType,
    role: string,
    locality: ILocality,
) => {
    role = role.trim();
    if (jobType == JobType.REMOTE) {
        return `${role} | Remote C2C Job | Apply Today`;
    }

    return `${role} | ${jobType} C2C Job in ${locality.city} | Apply Now`;
}

const generateMetaDescription = (
    jobType: JobType,
    role: string,
    locality: ILocality,
    organization: IOrganization,
    skills: ISkill[],
): string => {
    const payload = {
        jobType: jobType,
        role: role,
        locality: `${locality.city}, ${locality.state}`,
        company: organization.name,
        skills: skills.map((skill) => skill.name).join(", "),
    }

    const keys = Object.keys(payload);
    let description = metaDescritpionTemplate;

    for (const key of keys) {
        const value = payload[key as keyof typeof payload];
        description = description.replace(`%${key}%`, value);
    }

    return description;
};

export async function generateJobMetadatas(
    jobId: string,
    role: string,
    jobType: JobType,
    organization: IOrganization,
    skills: ISkill[],
    locality: ILocality,
) {
    const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL;
    const { city, state } = locality;

    const title = generateJobTitle(jobType, role, locality);
    const meta_description = generateMetaDescription(jobType, role, locality, organization, skills);
    const meta_keywords = [...skills.map((e) => e.name), organization.name, role, jobType, city, state].map((e) => e.toLowerCase());

    const slug = `${jobType}-${role}-in-${city}-${state}-for-${organization.name}-jobid-${jobId}`
        .toLowerCase()
        .replace(/[^a-zA-Z0-9]+/g, '-');
    const seo_link = `${baseUrl}/c2c-${slug}`;

    return {
        title,
        meta_description,
        meta_keywords,
        seo_link,
    }
}