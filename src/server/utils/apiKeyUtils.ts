import crypto from 'crypto';

/**
 * Generate a API key for internal use
 * 
 * @returns Generated API key
 */
export function generateApiKey(): string {
    return crypto.randomBytes(32).toString('base64url');
}

/**
 * Verifies if the provided API key matches the expected API key
 * 
 * @param provided API Key that requires verification
 * @param expected API Key to be validated with
 * @returns 
 */
export async function verifyApiKey(provided: string, expected: string): Promise<boolean> {
    if (provided.length !== expected.length) {
        return false;
    }

    return crypto.timingSafeEqual(
        Buffer.from(provided),
        Buffer.from(expected)
    );
}