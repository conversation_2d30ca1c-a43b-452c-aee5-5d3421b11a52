import axios from 'axios';
import { google, webmasters_v3 } from 'googleapis';
import path from 'path';
import fs from 'fs';
import { jobService, skillService, localityService } from '@/server/services';
import { formatISO } from 'date-fns';
import { SortOrder } from '@/server/core/BaseQueryParams';
import { QueryJobParams } from '@/server/repositories';
import { JobType } from '@/server/enums';

/**
 * Generates XML for the sitemap index file
 * @param sitemaps Array of sitemap URLs to include in the index
 * @returns XML string for the sitemap index
 */
export async function generateSitemapIndex(sitemaps: string[]): Promise<string> {
  const now = formatISO(new Date());

  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  for (const sitemap of sitemaps) {
    xml += '  <sitemap>\n';
    xml += `    <loc>${sitemap}</loc>\n`;
    xml += `    <lastmod>${now}</lastmod>\n`;
    xml += '  </sitemap>\n';
  }

  xml += '</sitemapindex>';
  return xml;
}

/**
 * Generates XML for a sitemap with the provided URLs
 * @param urls Array of URL objects with loc, lastmod, changefreq, and priority
 * @returns XML string for the sitemap
 */
export function generateSitemap(urls: {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}[]): string {
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  for (const url of urls) {
    xml += '  <url>\n';
    xml += `    <loc>${url.loc}</loc>\n`;
    if (url.lastmod) {
      xml += `    <lastmod>${url.lastmod}</lastmod>\n`;
    }
    if (url.changefreq) {
      xml += `    <changefreq>${url.changefreq}</changefreq>\n`;
    }
    if (url.priority !== undefined) {
      xml += `    <priority>${url.priority.toFixed(1)}</priority>\n`;
    }
    xml += '  </url>\n';
  }

  xml += '</urlset>';
  return xml;
}

/**
 * Generates sitemap for static pages
 * @returns XML string for the static pages sitemap
 */
export async function generateStaticSitemap(): Promise<string> {
  const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'https://onlyc2c.com';
  const now = formatISO(new Date());

  // Define static pages
  const staticPages = [
    { path: '', priority: 1.0, changefreq: 'daily' as const },
    { path: 'about', priority: 0.8, changefreq: 'monthly' as const },
    { path: 'contact', priority: 0.8, changefreq: 'monthly' as const },
    { path: 'pricing', priority: 0.8, changefreq: 'monthly' as const },
    { path: 'post-job', priority: 0.9, changefreq: 'monthly' as const },
    { path: 'login', priority: 0.7, changefreq: 'monthly' as const },
    { path: 'privacy', priority: 0.5, changefreq: 'yearly' as const },
    { path: 'terms', priority: 0.5, changefreq: 'yearly' as const },
  ];

  // Map to URL objects
  const urls = staticPages.map(page => ({
    loc: `${baseUrl}/${page.path}`,
    lastmod: now,
    changefreq: page.changefreq,
    priority: page.priority,
  }));

  return generateSitemap(urls);
}

/**
 * Generates sitemap for job listings
 * @returns XML string for the jobs sitemap
 */
export async function generateJobsSitemap(): Promise<string> {
  const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'https://onlyc2c.com';

  try {
    // Fetch all jobs
    const result = await jobService.getAll({
      limit: 1000,
      page: 1,
      created_at: SortOrder.ASC,
      updated_at: SortOrder.ASC,
      active: true
    } as QueryJobParams);
    const jobs = result.items;

    // Map to URL objects
    const urls = jobs.map(job => {
      // Use the job's SEO link if available, otherwise construct one
      const jobUrl = job.seo_link || `${baseUrl}/c2c/${job._id}`;
      const modifiedTime = job.created_at ? new Date(job.created_at) : new Date();
      return {
        loc: jobUrl,
        lastmod: formatISO(modifiedTime),
        changefreq: 'daily' as const,
        priority: 0.8,
      };
    });

    return generateSitemap(urls);
  } catch (error) {
    console.error('Error generating jobs sitemap:', error);
    return generateSitemap([]);
  }
}

/**
 * Generates sitemap for location-based pages
 * @returns XML string for the locations sitemap
 */
export async function generateLocationsSitemap(): Promise<string> {
  const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'https://onlyc2c.com';
  const now = formatISO(new Date());

  try {
    // Fetch all localities
    const result = await localityService.getAll();
    const localities = result.items;

    // Map to URL objects
    const urls = localities.map(locality => {
      const locationSlug = `c2c-job-in-${locality.city.toLowerCase().replace(/\s+/g, '-')}`;
      return {
        loc: `${baseUrl}/${locationSlug}`,
        lastmod: now,
        changefreq: 'weekly' as const,
        priority: 0.7,
      };
    });

    return generateSitemap(urls);
  } catch (error) {
    console.error('Error generating locations sitemap:', error);
    return generateSitemap([]);
  }
}

/**
 * Generates sitemap for skill-based pages
 * @returns XML string for the skills sitemap
 */
export async function generateSkillsSitemap(): Promise<string> {
  const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'https://onlyc2c.com';
  const now = formatISO(new Date());

  try {
    // Fetch all skills
    const result = await skillService.getAll();
    const skills = result.items;

    // Map to URL objects
    const urls = skills.map(skill => {
      const skillSlug = `${skill.name.toLowerCase().replace(/\s+/g, '-')}-c2c-jobs`;
      return {
        loc: `${baseUrl}/${skillSlug}`,
        lastmod: now,
        changefreq: 'weekly' as const,
        priority: 0.7,
      };
    });

    return generateSitemap(urls);
  } catch (error) {
    console.error('Error generating skills sitemap:', error);
    return generateSitemap([]);
  }
}

/**
 * Generates sitemap for job type pages
 * @returns XML string for the job types sitemap
 */
export async function generateJobTypesSitemap(): Promise<string> {
  const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'https://onlyc2c.com';
  const now = formatISO(new Date());

  // Define job types
  const jobTypes = Object.values(JobType).map((e) => e.toLowerCase());

  // Map to URL objects
  const urls = jobTypes.map(type => ({
    loc: `${baseUrl}/${type}-c2c-jobs`,
    lastmod: now,
    changefreq: 'weekly' as const,
    priority: 0.7,
  }));

  return generateSitemap(urls);
}

/**
 * Submits the sitemap to Google Search Console using the Search Console API
 * @param sitemapUrl The full URL to the sitemap
 * @returns Promise that resolves to the submission result
 */
export async function submitSitemapToGoogle(sitemapUrl: string): Promise<{ success: boolean; message: string }> {
  try {
    // First try the official Google Search Console API
    const result = await submitViaSearchConsoleAPI(sitemapUrl);
    if (result.success) {
      return result;
    }

    // Fall back to the ping method if the API method fails
    throw new Error(result.message);
  } catch (error) {
    console.error('Error submitting sitemap to Google:', error);
    return {
      success: false,
      message: `Error submitting sitemap to Google: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Submits the sitemap using Google Search Console API
 */
async function submitViaSearchConsoleAPI(sitemapUrl: string): Promise<{ success: boolean; message: string }> {
  try {
    // Path to the service account key file
    const keyFilePath = path.resolve(process.cwd(), process.env.GOOGLE_SERVICE_ACCOUNT_KEY as string);
    console.log(keyFilePath);

    // Check if the key file exists
    if (!fs.existsSync(keyFilePath)) {
      return {
        success: false,
        message: 'Google API key file not found. Falling back to ping method.',
      };
    }

    // Create auth client
    const auth = new google.auth.GoogleAuth({
      keyFile: keyFilePath,
      scopes: ['https://www.googleapis.com/auth/webmasters'],
    });

    // Initialize the Search Console API client
    const webmasters = google.webmasters({
      version: 'v3',
      auth: await auth.getClient(),
    } as webmasters_v3.Options);

    // Submit the sitemap
    await webmasters.sitemaps.submit({
      siteUrl: 'sc-domain:onlyc2c.com', // Use sc-domain: prefix for domain properties
      feedpath: sitemapUrl,
    });

    return {
      success: true,
      message: 'Sitemap successfully submitted to Google Search Console API',
    };
  } catch (error) {
    console.error('Error submitting via Search Console API:', error);
    return {
      success: false,
      message: `Failed to submit via Search Console API: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Saves a sitemap to the local filesystem for testing and verification
 * @param sitemapXml The XML content of the sitemap
 * @param filename The filename to save the sitemap as
 */
export function saveSitemapLocally(sitemapXml: string, filename: string): void {
  try {
    const sitemapsDir = path.resolve(process.cwd(), 'public', 'sitemaps');

    // Create the directory if it doesn't exist
    if (!fs.existsSync(sitemapsDir)) {
      fs.mkdirSync(sitemapsDir, { recursive: true });
    }

    // Write the sitemap file
    fs.writeFileSync(path.join(sitemapsDir, filename), sitemapXml);
    console.log(`Sitemap saved locally: ${filename}`);
  } catch (error) {
    console.error(`Error saving sitemap locally: ${error}`);
  }
}

/**
 * Generates all sitemaps and saves them locally for testing
 */
export async function generateAndSaveAllSitemapsLocally(): Promise<void> {
  try {
    // Generate all sitemaps
    const staticSitemap = await generateStaticSitemap();
    const jobsSitemap = await generateJobsSitemap();
    const locationsSitemap = await generateLocationsSitemap();
    const skillsSitemap = await generateSkillsSitemap();
    const jobTypesSitemap = await generateJobTypesSitemap();

    // Save all sitemaps locally
    saveSitemapLocally(staticSitemap, 'static-sitemap.xml');
    saveSitemapLocally(jobsSitemap, 'jobs-sitemap.xml');
    saveSitemapLocally(locationsSitemap, 'locations-sitemap.xml');
    saveSitemapLocally(skillsSitemap, 'skills-sitemap.xml');
    saveSitemapLocally(jobTypesSitemap, 'job-types-sitemap.xml');

    // Generate and save the sitemap index
    const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'https://onlyc2c.com';
    const sitemaps = [
      `${baseUrl}/sitemaps/static-sitemap.xml`,
      `${baseUrl}/sitemaps/jobs-sitemap.xml`,
      `${baseUrl}/sitemaps/locations-sitemap.xml`,
      `${baseUrl}/sitemaps/skills-sitemap.xml`,
      `${baseUrl}/sitemaps/job-types-sitemap.xml`,
    ];
    const sitemapIndex = await generateSitemapIndex(sitemaps);
    saveSitemapLocally(sitemapIndex, 'sitemap.xml');

    console.log('All sitemaps generated and saved locally');

    // Print URLs for easy verification
    console.log('\nSitemap URLs for verification:');
    console.log(`${baseUrl}/sitemap.xml`);
    console.log(`${baseUrl}/api/sitemap/static`);
    console.log(`${baseUrl}/api/sitemap/jobs`);
    console.log(`${baseUrl}/api/sitemap/locations`);
    console.log(`${baseUrl}/api/sitemap/skills`);
    console.log(`${baseUrl}/api/sitemap/job-types`);
  } catch (error) {
    console.error('Error generating and saving sitemaps:', error);
  }
}

/**
 * Submits all sitemaps to Google Search Console
 */
export async function submitAllSitemapsToGoogle(): Promise<{ success: boolean; results: any[] }> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL || 'https://onlyc2c.com';
    const mainSitemapUrl = `${baseUrl}/sitemap.xml`;

    // Submit the main sitemap index
    const result = await submitSitemapToGoogle(mainSitemapUrl);

    if (result.success) {
      console.log(`Successfully submitted main sitemap: ${mainSitemapUrl}`);
      return {
        success: true,
        results: [result]
      };
    } else {
      console.error(`Failed to submit main sitemap: ${result.message}`);
      return {
        success: false,
        results: [result]
      };
    }
  } catch (error) {
    console.error('Error submitting sitemaps to Google:', error);
    return {
      success: false,
      results: [{
        success: false,
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      }]
    };
  }
}
