import { EmailOptions } from '@/server/services/EmailService';
import { queueEmail } from './emailQueue';
import { formatDate } from './dateUtils';

/**
 * Send a welcome email to a new user based on their role
 *
 * @param name User's name
 * @param email User's email
 * @param role User's role ('candidate' or 'recruiter')
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendWelcomeEmail(
  name: string,
  email: string,
  role: 'candidate' | 'recruiter',
  options?: { bcc?: string | string[] }
) {
  let template;
  let supportUrl;

  if (role === 'candidate') {
    supportUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL;
    template = 'welcome-candidate';
  } else {
    supportUrl = process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL + '/post-job';
    template = 'welcome-recruiter';
  }

  return queueEmail({
    to: email,
    bcc: options?.bcc,
    template,
    context: {
      name,
      email,
      year: new Date().getFullYear(),
      supportUrl,
    }
  });
}

/**
 * Send a job application confirmation email
 *
 * @param name Applicant's name
 * @param email Applicant's email
 * @param jobTitle Job title
 * @param companyName Company name
 * @param options Additional options (location, jobType, bcc)
 * @returns Promise with the send info
 */
export async function sendJobApplicationConfirmationEmail(
  name: string,
  email: string,
  jobTitle: string,
  companyName: string,
  applicationUrl: string,
  options?: {
    location?: string;
    jobType?: string;
    bcc?: string | string[];
  }
) {
  return queueEmail({
    to: email,
    bcc: options?.bcc,
    template: 'job-application-confirmation',
    context: {
      name,
      email,
      jobTitle,
      companyName,
      location: options?.location,
      jobType: options?.jobType,
      year: new Date().getFullYear(),
      applicationUrl,
    }
  });
}

/**
 * Send a custom email using a template
 *
 * @param options Email options
 * @returns Promise with the send info
 */
export async function sendEmail(options: EmailOptions) {
  // Add default BCC if not provided
  if (!options.bcc) {
    options.bcc = '<EMAIL>';
  }

  return queueEmail(options);
}

/**
 * Send a job application notification email to the recruiter
 *
 * @param recruiterName Recruiter's name
 * @param recruiterEmail Recruiter's email
 * @param candidateName Candidate's name
 * @param jobTitle Job title
 * @param applicationDate Application date
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendJobApplicationNotificationToRecruiter(
  recruiterName: string,
  recruiterEmail: string,
  candidateName: string,
  jobTitle: string,
  applicationDate: Date,
  applicationUrl: string,
  options?: {
    bcc?: string | string[];
  }
) {
  return queueEmail({
    to: recruiterEmail,
    bcc: options?.bcc,
    template: 'job-application-notification-recruiter',
    context: {
      recruiterName,
      candidateName,
      jobTitle,
      applicationDate: formatDate(applicationDate),
      year: new Date().getFullYear(),
      applicationUrl,
    }
  });
}

/**
 * Send an application viewed notification email to the candidate
 *
 * @param candidateName Candidate's name
 * @param candidateEmail Candidate's email
 * @param jobTitle Job title
 * @param companyName Company name
 * @param applicationId Application ID
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendApplicationViewedNotification(
  candidateName: string,
  candidateEmail: string,
  jobTitle: string,
  companyName: string,
  applicationId: string,
  applicationUrl: string,
  options?: {
    bcc?: string | string[];
  }
) {
  return queueEmail({
    to: candidateEmail,
    bcc: options?.bcc,
    template: 'application-viewed-notification',
    context: {
      candidateName,
      jobTitle,
      companyName,
      applicationId,
      year: new Date().getFullYear(),
      applicationUrl,
    }
  });
}

/**
 * Send an application rejected notification email to the candidate
 *
 * @param candidateName Candidate's name
 * @param candidateEmail Candidate's email
 * @param jobTitle Job title
 * @param companyName Company name
 * @param skills Candidate's skills (comma-separated)
 * @param recommendedJobsCount Number of recommended jobs
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendApplicationRejectedNotification(
  candidateName: string,
  candidateEmail: string,
  jobTitle: string,
  companyName: string,
  skills: string,
  recommendedJobsCount: number,
  options?: {
    bcc?: string | string[];
  }
) {
  const moreJobsUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/c2c-jobs`;
  return queueEmail({
    to: candidateEmail,
    bcc: options?.bcc,
    template: 'application-rejected-notification',
    context: {
      candidateName,
      jobTitle,
      companyName,
      skills,
      recommendedJobsCount,
      year: new Date().getFullYear(),
      moreJobsUrl,
    }
  });
}

/**
 * Send an application shortlisted notification email to the candidate
 *
 * @param candidateName Candidate's name
 * @param candidateEmail Candidate's email
 * @param jobTitle Job title
 * @param companyName Company name
 * @param applicationId Application ID
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendApplicationShortlistedNotification(
  candidateName: string,
  candidateEmail: string,
  jobTitle: string,
  companyName: string,
  applicationId: string,
  applicationUrl: string,
  options?: {
    bcc?: string | string[];
  }
) {
  return queueEmail({
    to: candidateEmail,
    bcc: options?.bcc,
    template: 'application-shortlisted-notification',
    context: {
      candidateName,
      jobTitle,
      companyName,
      applicationId,
      year: new Date().getFullYear(),
      applicationUrl,
    }
  });
}

/**
 * Send an application hired notification email to the candidate
 *
 * @param candidateName Candidate's name
 * @param candidateEmail Candidate's email
 * @param jobTitle Job title
 * @param companyName Company name
 * @param applicationId Application ID
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendApplicationHiredNotification(
  candidateName: string,
  candidateEmail: string,
  jobTitle: string,
  companyName: string,
  applicationId: string,
  applicationUrl: string,
  options?: {
    bcc?: string | string[];
  }
) {
  return queueEmail({
    to: candidateEmail,
    bcc: options?.bcc,
    template: 'application-hired-notification',
    context: {
      candidateName,
      jobTitle,
      companyName,
      applicationId,
      year: new Date().getFullYear(),
      applicationUrl,
    }
  });
}

/**
 * Send a newsletter email to candidates with job opportunities
 *
 * @param candidateName Candidate's name
 * @param candidateEmail Candidate's email
 * @param jobCount Number of matching jobs
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendCandidateNewsletter(
  candidateName: string,
  candidateEmail: string,
  jobCount: number,
  options?: {
    bcc?: string | string[];
  }
) {
  const jobsUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/c2c-jobs`;
  return queueEmail({
    to: candidateEmail,
    bcc: options?.bcc,
    template: 'candidate-newsletter',
    context: {
      candidateName,
      jobsUrl,
      year: new Date().getFullYear(),
    }
  });
}

/**
 * Send a job closed notification email to a candidate
 *
 * @param candidateName Candidate's name
 * @param candidateEmail Candidate's email
 * @param jobTitle Job title
 * @param companyName Company name
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendJobClosedNotification(
  candidateName: string,
  candidateEmail: string,
  jobTitle: string,
  companyName: string,
  options?: {
    bcc?: string | string[];
  }
) {
  const jobSearchUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}`;

  return queueEmail({
    to: candidateEmail,
    bcc: options?.bcc,
    template: 'job-closed-notification',
    context: {
      candidateName,
      jobTitle,
      companyName,
      jobSearchUrl,
      year: new Date().getFullYear(),
    }
  });
}

/**
 * Send a new external job notification email to a candidate
 *
 * @param candidateName Candidate's name
 * @param candidateEmail Candidate's email
 * @param role Job role
 * @param recruiterEmail Recruiter's email
 * @param jobLink Job application link
 * @param jobDescription Job description
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendNewExternalJobNotification(
  candidateName: string,
  candidateEmail: string,
  role: string,
  recruiterEmail: string,
  jobLink: string,
  jobDescription: string,
  options?: {
    bcc?: string | string[];
  }
) {
  return queueEmail({
    to: candidateEmail,
    bcc: options?.bcc,
    template: 'new-external-job-notification',
    context: {
      candidateName,
      role,
      recruiterEmail,
      jobLink,
      jobDescription,
      year: new Date().getFullYear(),
    }
  });
}

/**
 * Send new external job notifications to all candidates
 *
 * @param role Job role
 * @param recruiterEmail Recruiter's email
 * @param jobLink Job application link
 * @param jobDescription Job description
 * @param options Additional options (bcc)
 * @returns Promise with the send info
 */
export async function sendNewExternalJobNotificationToAllCandidates(
  role: string,
  recruiterEmail: string,
  jobLink: string,
  jobDescription: string,
  options?: {
    bcc?: string | string[];
  }
) {
  try {
    // Import candidateService here to avoid circular dependency
    const { candidateService } = await import('@/server/services');

    // Get all candidates with their user information (including emails)
    const candidates = await candidateService.getAllCandidatesWithEmails();

    // Send notification to each candidate
    const emailPromises = candidates.map(candidate => {
      const user = candidate.user as any; // Type assertion since it's populated
      const candidateName = user?.name || 'Candidate';
      const candidateEmail = user?.email;

      if (candidateEmail) {
        return sendNewExternalJobNotification(
          candidateName,
          candidateEmail,
          role,
          recruiterEmail,
          jobLink,
          jobDescription,
          options
        );
      }
      return Promise.resolve();
    });

    // Wait for all emails to be queued
    await Promise.all(emailPromises);

    console.log(`Queued external job notifications for ${candidates.length} candidates`);
  } catch (error) {
    console.error('Error sending external job notifications to all candidates:', error);
    throw error;
  }
}

/**
 * Send a new C2C job notification email to a candidate
 *
 * @param candidateName Candidate's name
 * @param candidateEmail Candidate's email
 * @param jobTitle Job title
 * @param companyName Company name
 * @param location Job location
 * @param jobType Job type
 * @param jobId Job ID
 * @param jobUrl Job application URL
 * @param jobDescription Job description
 * @param recruiterName Recruiter's name
 * @param options Additional options (hourlyRate, skills, bcc)
 * @returns Promise with the send info
 */
export async function sendNewC2CJobNotification(
  candidateName: string,
  candidateEmail: string,
  jobTitle: string,
  companyName: string,
  location: string,
  jobType: string,
  jobId: string,
  jobUrl: string,
  jobDescription: string,
  recruiterName: string,
  options?: {
    hourlyRate?: string;
    skills?: string;
    bcc?: string | string[];
  }
) {
  return queueEmail({
    to: candidateEmail,
    bcc: options?.bcc,
    template: 'new-c2c-job-notification',
    context: {
      candidateName,
      jobTitle,
      companyName,
      location,
      jobType,
      jobId,
      jobUrl,
      jobDescription,
      recruiterName,
      hourlyRate: options?.hourlyRate,
      skills: options?.skills,
      year: new Date().getFullYear(),
    }
  });
}

/**
 * Send new C2C job notifications to all candidates
 *
 * @param jobTitle Job title
 * @param companyName Company name
 * @param location Job location
 * @param jobType Job type
 * @param jobId Job ID
 * @param jobUrl Job application URL
 * @param jobDescription Job description
 * @param recruiterName Recruiter's name
 * @param options Additional options (hourlyRate, skills, bcc)
 * @returns Promise with the send info
 */
export async function sendNewC2CJobNotificationToAllCandidates(
  jobTitle: string,
  companyName: string,
  location: string,
  jobType: string,
  jobId: string,
  jobUrl: string,
  jobDescription: string,
  recruiterName: string,
  options?: {
    hourlyRate?: string;
    skills?: string;
    bcc?: string | string[];
  }
) {
  try {
    // Import candidateService here to avoid circular dependency
    const { candidateService } = await import('@/server/services');

    // Get all candidates with their user information (including emails)
    const candidates = await candidateService.getAllCandidatesWithEmails();

    // Send notification to each candidate
    const emailPromises = candidates.map(candidate => {
      const user = candidate.user as any; // Type assertion since it's populated
      const candidateName = user?.name || 'Candidate';
      const candidateEmail = user?.email;

      if (candidateEmail) {
        return sendNewC2CJobNotification(
          candidateName,
          candidateEmail,
          jobTitle,
          companyName,
          location,
          jobType,
          jobId,
          jobUrl,
          jobDescription,
          recruiterName,
          options
        );
      }
      return Promise.resolve();
    });

    // Wait for all emails to be queued
    await Promise.all(emailPromises);

    console.log(`Queued C2C job notifications for ${candidates.length} candidates`);
  } catch (error) {
    console.error('Error sending C2C job notifications to all candidates:', error);
    throw error;
  }
}
