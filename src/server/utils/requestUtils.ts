import { NextRequest } from "next/server";

export const USER_ID_HEADER = 'x-c2c-user-id';
export const USER_NAME_HEADER = 'x-c2c-user-name';
export const USER_PROFILE_ID_HEADER = 'x-c2c-user-profile-id';
export const USER_EMAIL_HEADER = 'x-c2c-user-email';
export const USER_SCOPE_HEADER = 'x-c2c-user-scope';

export const getUserId = function (request: NextRequest) {
    return request.headers.get(USER_ID_HEADER)!;
}

export const getUserName = function (request: NextRequest) {
    return request.headers.get(USER_NAME_HEADER)!;
}

export const getUserEmail = function (request: NextRequest) {
    return request.headers.get(USER_EMAIL_HEADER)!;
}

export const getUserProfileId = function (request: NextRequest) {
    return request.headers.get(USER_PROFILE_ID_HEADER)!;
}

export const getUserScope = function (request: NextRequest) {
    return request.headers.get(USER_SCOPE_HEADER)!;
}
