import BaseService from "@/server/core/BaseService";
import { IPremiumSubscription, ICandidate } from "@/server/models";
import { QueryPremiumSubscriptionParams, PremiumSubscriptionRepository, premiumSubscriptionRepository } from "@/server/repositories/PremiumSubscriptionRepository";
import { SubscriptionStatus, UserScope, PlanType } from "@/server/enums";
import { stripeService } from "./StripeService";
import { candidateService } from "./CandidateService";
import Stripe from "stripe";

export class PremiumSubscriptionService extends BaseService<PremiumSubscriptionRepository, IPremiumSubscription, QueryPremiumSubscriptionParams> {
    constructor() {
        super(premiumSubscriptionRepository);
    }

    /**
     * Find subscription by user ID and type
     */
    async findByUserIdAndType(userId: string, userType: UserScope): Promise<IPremiumSubscription | null> {
        return this.repository.findByUserIdAndType(userId, userType);
    }

    /**
     * Find active subscription by user ID and type
     */
    async findActiveByUserIdAndType(userId: string, userType: UserScope): Promise<IPremiumSubscription | null> {
        return this.repository.findActiveByUserIdAndType(userId, userType);
    }

    /**
     * Check if user has active subscription
     */
    async hasActiveSubscription(userId: string, userType: UserScope): Promise<boolean> {
        const subscription = await this.findActiveByUserIdAndType(userId, userType);
        return subscription !== null;
    }

    /**
     * Create checkout session for candidate subscription
     */
    async createCandidateCheckoutSession(
        candidateId: string,
        planType: PlanType,
        successUrl: string,
        cancelUrl: string
    ): Promise<{ sessionUrl: string; sessionId: string }> {
        // Get candidate details
        const candidate = await candidateService.getById(candidateId);
        if (!candidate) {
            throw new Error('Candidate not found');
        }

        const user = candidate.user as any;
        if (!user) {
            throw new Error('User details not found');
        }

        // Check if candidate already has a subscription
        const existingSubscription = await this.findByUserIdAndType(user._id.toString(), UserScope.CANDIDATE);
        
        let stripeCustomerId: string;
        
        if (existingSubscription) {
            stripeCustomerId = existingSubscription.stripeCustomerId;
        } else {
            // Create Stripe customer
            const customer = await stripeService.createCustomer(user.email, user.name);
            stripeCustomerId = customer.id;
        }

        // Get price ID for the plan
        const priceId = stripeService.getPriceId(planType);
        if (!priceId) {
            throw new Error(`Price ID not configured for plan: ${planType}`);
        }

        // Create checkout session
        const session = await stripeService.createCheckoutSession(
            stripeCustomerId,
            priceId,
            successUrl,
            cancelUrl,
            {
                candidateId,
                userId: user._id.toString(),
                userType: UserScope.CANDIDATE,
                planType
            }
        );

        if (!session.url) {
            throw new Error('Failed to create checkout session');
        }

        return {
            sessionUrl: session.url,
            sessionId: session.id
        };
    }

    /**
     * Create customer portal session
     */
    async createPortalSession(userId: string, userType: UserScope, returnUrl: string): Promise<string> {
        const subscription = await this.findByUserIdAndType(userId, userType);
        if (!subscription) {
            throw new Error('No subscription found');
        }

        const session = await stripeService.createPortalSession(subscription.stripeCustomerId, returnUrl);
        return session.url;
    }

    /**
     * Handle successful subscription creation from webhook
     */
    async handleSubscriptionCreated(stripeSubscription: Stripe.Subscription): Promise<IPremiumSubscription> {
        const customerId = stripeSubscription.customer as string;
        const metadata = stripeSubscription.metadata;

        if (!metadata.userId || !metadata.userType || !metadata.planType) {
            throw new Error('Missing required metadata in subscription');
        }

        // Create premium subscription record
        const premiumSubscription = await this.create({
            userId: metadata.userId,
            userType: metadata.userType as UserScope,
            stripeCustomerId: customerId,
            stripeSubscriptionId: stripeSubscription.id,
            status: stripeSubscription.status as SubscriptionStatus,
            currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
            currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
            cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
            planType: metadata.planType as PlanType,
            priceId: stripeSubscription.items.data[0].price.id
        } as IPremiumSubscription);

        // Update candidate premium status if it's a candidate subscription
        if (metadata.userType === UserScope.CANDIDATE && metadata.candidateId) {
            await candidateService.update(metadata.candidateId, {
                isPremium: true,
                subscriptionId: premiumSubscription._id
            } as Partial<ICandidate>);
        }

        return premiumSubscription;
    }

    /**
     * Handle subscription updates from webhook
     */
    async handleSubscriptionUpdated(stripeSubscription: Stripe.Subscription): Promise<IPremiumSubscription | null> {
        const subscription = await this.repository.findByStripeSubscriptionId(stripeSubscription.id);
        if (!subscription) {
            console.warn(`Subscription not found for Stripe ID: ${stripeSubscription.id}`);
            return null;
        }

        const updatedSubscription = await this.update(subscription._id.toString(), {
            status: stripeSubscription.status as SubscriptionStatus,
            currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
            currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
            cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
        } as Partial<IPremiumSubscription>);

        // Update candidate premium status
        if (subscription.userType === UserScope.CANDIDATE) {
            const candidate = await candidateService.getByUserId(subscription.userId.toString());
            if (candidate) {
                const isPremium = stripeSubscription.status === SubscriptionStatus.ACTIVE;
                await candidateService.update(candidate._id.toString(), {
                    isPremium
                } as Partial<ICandidate>);
            }
        }

        return updatedSubscription;
    }

    /**
     * Handle subscription deletion from webhook
     */
    async handleSubscriptionDeleted(stripeSubscription: Stripe.Subscription): Promise<void> {
        const subscription = await this.repository.findByStripeSubscriptionId(stripeSubscription.id);
        if (!subscription) {
            console.warn(`Subscription not found for Stripe ID: ${stripeSubscription.id}`);
            return;
        }

        // Update subscription status
        await this.update(subscription._id.toString(), {
            status: SubscriptionStatus.CANCELED
        } as Partial<IPremiumSubscription>);

        // Update candidate premium status
        if (subscription.userType === UserScope.CANDIDATE) {
            const candidate = await candidateService.getByUserId(subscription.userId.toString());
            if (candidate) {
                await candidateService.update(candidate._id.toString(), {
                    isPremium: false
                } as Partial<ICandidate>);
            }
        }
    }

    /**
     * Get subscription analytics
     */
    async getAnalytics() {
        return this.repository.getAnalytics();
    }
}

export const premiumSubscriptionService = new PremiumSubscriptionService();
