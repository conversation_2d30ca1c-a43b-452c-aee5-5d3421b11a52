import nodemailer from 'nodemailer';
import fs from 'fs';
import path from 'path';
import Handlebars from 'handlebars';
import { promisify } from 'util';
import { glob } from 'glob';

const readFile = promisify(fs.readFile);

export interface EmailTemplate {
  subject: string;
  html: string;
}

export interface EmailOptions {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject?: string;
  html?: string;
  text?: string;
  template?: string;
  context?: Record<string, any>;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private templatesDir: string;

  constructor() {
    // Initialize the transporter with environment variables
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: Number(process.env.EMAIL_PORT) || 465,
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    // Set the templates directory
    this.templatesDir = path.join(process.cwd(), 'src/templates/emails');

    // Register partials
    this.registerPartials();
  }

  /**
   * Register all Handlebars partials from the components directory
   */
  private async registerPartials() {
    try {
      const componentsDir = path.join(this.templatesDir, 'components');

      // Use glob to find all .hbs files in the components directory
      const partialFiles = await glob(`${componentsDir}/**/*.hbs`);

      // Register each file as a partial
      for (const file of partialFiles) {
        const content = await readFile(file, 'utf-8');
        const partialName = path.relative(this.templatesDir, file).replace(/\.hbs$/, '');
        Handlebars.registerPartial(partialName, content);
      }

      // Register the partial-block helper for layouts
      Handlebars.registerHelper('partial-block', function(this: any, options: Handlebars.HelperOptions) {
        return options.fn(this);
      });

      // Register the @partial-block helper for layouts (alternative syntax)
      Handlebars.registerHelper('@partial-block', function(this: any, options: Handlebars.HelperOptions) {
        return options.fn(this);
      });
    } catch (error) {
      console.error('Error registering email partials:', error);
    }
  }

  /**
   * Send an email
   *
   * @param options Email options
   * @returns Promise with the send info
   */
  async sendEmail(options: EmailOptions): Promise<nodemailer.SentMessageInfo> {
    const { to, cc, bcc, subject, html, text, template, context, attachments } = options;

    let emailHtml = html;
    let emailSubject = subject;

    // If a template is specified, compile it with Handlebars
    if (template) {
      const { html: compiledHtml, subject: compiledSubject } = await this.compileTemplate(template, context || {});
      emailHtml = compiledHtml;
      emailSubject = emailSubject || compiledSubject;
    }

    // Send the email
    return this.transporter.sendMail({
      from: process.env.EMAIL_FROM || `"${process.env.EMAIL_FROM_NAME || 'OnlyC2C'}" <${process.env.EMAIL_USER}>`,
      to,
      cc,
      bcc,
      subject: emailSubject,
      text: text || this.stripHtml(emailHtml || ''),
      html: emailHtml,
      attachments,
    });
  }

  /**
   * Compile a template with Handlebars
   *
   * @param templateName Name of the template file (without extension)
   * @param context Context data to inject into the template
   * @returns Compiled template with subject and HTML
   */
  private async compileTemplate(templateName: string, context: Record<string, any>): Promise<EmailTemplate> {
    try {
      // Read the template file
      const templatePath = path.join(this.templatesDir, `${templateName}.hbs`);
      const templateContent = await readFile(templatePath, 'utf-8');

      // Compile the template with Handlebars
      const template = Handlebars.compile(templateContent);
      const rendered = template(context);

      // Extract the subject from the template (assuming it's in the format <!-- Subject: Your Subject -->)
      const subjectMatch = rendered.match(/<!--\s*Subject:\s*(.*?)\s*-->/i);
      const subject = subjectMatch ? subjectMatch[1] : 'No Subject';

      // Remove the subject comment from the HTML
      const html = rendered.replace(/<!--\s*Subject:\s*(.*?)\s*-->/i, '');

      return { subject, html };
    } catch (error) {
      console.error(`Error compiling template ${templateName}:`, error);
      throw new Error(`Failed to compile email template: ${templateName}`);
    }
  }

  /**
   * Strip HTML tags from a string to create a plain text version
   *
   * @param html HTML string
   * @returns Plain text string
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '');
  }
}

export const emailService = new EmailService();
