import BaseService from "@/server/core/BaseService";
import { IWorkAuthorization } from "@/server/models";
import { QueryWorkAuthorizationParams, WorkAuthorizationRepository, workAuthorizationRepository } from "@/server/repositories";

export class WorkAuthorizationService extends BaseService<WorkAuthorizationRepository, IWorkAuthorization, QueryWorkAuthorizationParams> {
    constructor() {
        super(workAuthorizationRepository);
    }
}

export const workAuthorizationService = new WorkAuthorizationService();
