import BaseService from "@/server/core/BaseService";
import { deleteFile } from "@/server/lib/gcs";
import { IResume } from "@/server/models";
import { QueryResumeParams, resumeRepository, ResumeRepository } from "@/server/repositories";

export class ResumeService extends BaseService<ResumeRepository, IResume, QueryResumeParams> {
    constructor() {
        super(resumeRepository);
    }

    private async deleteResumeIfExists(id: string) {
        const resume = await this.getById(id);
        const key = resume.file.key;
        if (!key) return;

        try {
            await deleteFile(key);
        } catch (error) {
            // ignore: file not found error
            console.log(error);
        }
    }

    async update(id: string, data: IResume): Promise<IResume> {
        if (data.file) {
            await this.deleteResumeIfExists(id);
        }

        return super.update(id, data);
    }

    async delete(id: string): Promise<IResume> {
        await this.deleteResumeIfExists(id);
        return super.delete(id);
    }
}

export const resumeService = new ResumeService();