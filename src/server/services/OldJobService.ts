import { IOldJob } from "@/server/models";
import { OldJobRepository } from "@/server/repositories";

export class OldJobService {
    private readonly repository = new OldJobRepository();

    async getOldJobs(): Promise<IOldJob[]> {
        return this.repository.getOldJobs();
    }

    async getOldJobById(id: string): Promise<IOldJob> {
        return this.repository.getOldJobById(id);
    }
}

export const oldJobService = new OldJobService();