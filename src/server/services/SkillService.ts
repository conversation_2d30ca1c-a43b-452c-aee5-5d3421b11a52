import { QuerySkillsParams, skillRepository, SkillRepository } from "@/server/repositories";
import BaseService from "@/server/core/BaseService";
import { ISkill } from "@/server/models";

export class SkillService extends BaseService<SkillRepository, ISkill, QuerySkillsParams> {
    constructor() {
        super(skillRepository);
    }

    /**
     * Get all skills of given ids
     * 
     * @param ids List of mongo ids
     * @returns List of skills
     */
    async getAllOfIds(ids: string[]): Promise<ISkill[]> {
        return this.repository.getAllOfIds(ids);
    }

    async batchCreate(skills: ISkill[]): Promise<ISkill[]> {
        return this.repository.batchCreate(skills);
    }
}

export const skillService = new SkillService();