import axios from 'axios';
import { Account, IJob, ILocality } from '@/server/models';
import { Types } from 'mongoose';
import connectToDb from '@/server/lib/db';
import { ErrorNotFound, UnauthorizedError } from '@/server/core/Errors';
import { jobService } from '@/server/services';

interface LinkedInPostOptions {
  imageUrl?: string;
  imageId?: string;
  content: string;
}

export class LinkedInService {
  // LinkedIn API version to use
  private readonly LINKEDIN_API_VERSION = '202505';

  /**
   * Post a job to LinkedIn
   *
   * @param jobId The ID of the job to post
   * @param userId The ID of the user posting the job
   * @param options Optional parameters like image URL
   * @returns The LinkedIn post response
   */
  async postJobToLinkedIn(jobId: string, userId: string, options: LinkedInPostOptions): Promise<any> {
    await connectToDb();

    // Convert userId to ObjectId
    const userIdObj = Types.ObjectId.createFromHexString(userId);

    // Find LinkedIn account for this user
    const linkedInAccount = await Account.findOne({
      userId: userIdObj,
      provider: 'linkedin',
    });

    if (!linkedInAccount) {
      throw new UnauthorizedError('LinkedIn account not connected');
    }

    // Check if token is still valid
    const currentTime = Math.floor(Date.now() / 1000);
    const isExpired = linkedInAccount.expires_at &&
      linkedInAccount.expires_at < currentTime;

    if (isExpired) {
      throw new UnauthorizedError('LinkedIn token has expired. Please reconnect your LinkedIn account.');
    }

    // Check if the account has the required scope
    const scope = linkedInAccount.scope || '';
    if (!scope.includes('w_member_social')) {
      throw new UnauthorizedError('LinkedIn account does not have the required permissions. Please reconnect with the appropriate permissions.');
    }

    // Skip profile retrieval and directly use the providerAccountId
    let memberUrn: string;

    // Use the providerAccountId from the account record if available
    if (linkedInAccount.providerAccountId) {
      memberUrn = `urn:li:person:${linkedInAccount.providerAccountId}`;
    } else {
      // If no providerAccountId, we cannot proceed as we need a valid URN
      throw new UnauthorizedError('LinkedIn account does not have a valid provider account ID. Please reconnect your LinkedIn account.');
    }

    // Get the job details from the database
    const job = await jobService.getById(jobId);
    if (!job) {
      throw new ErrorNotFound('Job not found');
    }

    // Create the content
    const content = options.content || this.createJobPostContent(job);

    // If an image is provided, upload it to LinkedIn first
    let mediaId = null;
    if (options?.imageUrl) {
      mediaId = await this.uploadImageToLinkedIn(options.imageUrl, linkedInAccount.access_token, memberUrn);
    } else if (options?.imageId) {
      mediaId = options.imageId;
    }

    // Create the LinkedIn post
    const postResponse = await this.createLinkedInPost(
      linkedInAccount.access_token,
      content,
      memberUrn,
      mediaId
    );

    return postResponse;
  }

  /**
   * Create job post content for LinkedIn
   *
   * @param job The job details
   * @returns Formatted job post content
   */
  private createJobPostContent(job: IJob): string {
    const jobRole = job.role;
    const jobType = job.job_type;

    const locality = job.locality as ILocality | undefined;
    const location = locality ? `${locality.city}, ${locality.state}` : 'Remote';

    // Format the post content with a clear headline
    let content = `🔥 New Job Opening: ${jobRole}
Job Type: ${jobType}
Location: ${location}

APPLY HERE 👉 ${process.env.NEXTAUTH_URL}/jobs/${job.seo_link}
#jobs #hiring #${jobRole.replace(/\s+/g, '')} #${jobType.replace(/\s+/g, '')} #onlyc2c`;

    return content;
  }

  /**
   * Upload an image to LinkedIn
   *
   * @param imageUrl The URL of the image to upload
   * @param accessToken LinkedIn access token
   * @param ownerUrn The URN of the owner (person or organization)
   * @returns The LinkedIn image ID
   */
  private async uploadImageToLinkedIn(imageUrl: string, accessToken: string, ownerUrn: string): Promise<string> {
    console.log('Initializing LinkedIn image upload with owner:', ownerUrn);

    // 1. Initialize the image upload
    const initResponse = await axios.post(
      'https://api.linkedin.com/rest/images?action=initializeUpload',
      {
        initializeUploadRequest: {
          owner: ownerUrn
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'LinkedIn-Version': this.LINKEDIN_API_VERSION,
          'X-RestLi-Protocol-Version': '2.0.0',
          'Content-Type': 'application/json'
        }
      }
    );

    const { uploadUrl, image: imageId } = initResponse.data.value;

    // 2. Download the image from the provided URL
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const imageBuffer = Buffer.from(imageResponse.data, 'binary');

    // 3. Upload the image to LinkedIn
    await axios.put(
      uploadUrl,
      imageBuffer,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/octet-stream'
        }
      }
    );

    // Return the LinkedIn image ID
    return imageId;
  }

  /**
   * Create a LinkedIn post
   *
   * @param accessToken LinkedIn access token
   * @param content Post content
   * @param authorUrn The URN of the author (person or organization)
   * @param mediaId Optional media ID to attach
   * @returns LinkedIn post response
   */
  private async createLinkedInPost(
    accessToken: string,
    content: string,
    authorUrn: string,
    mediaId?: string | null
  ): Promise<any> {
    try {
      console.log('Creating LinkedIn post with author:', authorUrn);

      // Prepare the post payload
      const postPayload: any = {
        author: authorUrn,
        visibility: "PUBLIC",
        distribution: {
          feedDistribution: "MAIN_FEED",
          targetEntities: [],
          thirdPartyDistributionChannels: []
        },
        lifecycleState: "PUBLISHED",
        isReshareDisabledByAuthor: false
      };

      // Add the content
      postPayload.commentary = content;

      // Add media if provided
      if (mediaId) {
        // If we're using article content type, add the media as thumbnail
        if (postPayload.content?.article) {
          postPayload.content.article.thumbnail = mediaId;
        } else {
          // Otherwise use media content type
          postPayload.content = {
            media: {
              id: mediaId
            }
          };
        }
      }

      // Log the payload for debugging
      console.log('LinkedIn post payload:', JSON.stringify(postPayload, null, 2));

      // Create the post
      const response = await axios.post(
        'https://api.linkedin.com/rest/posts',
        postPayload,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'LinkedIn-Version': this.LINKEDIN_API_VERSION,
            'X-RestLi-Protocol-Version': '2.0.0',
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        postId: response.headers['x-restli-id'],
        data: response.data,
        authorUsed: postPayload.author // Include the author that was actually used
      };
    } catch (error) {
      console.error('Error creating LinkedIn post:', error);

      if (axios.isAxiosError(error) && error.response) {
        const status = error.response.status;
        const data = error.response.data;

        // Log the full error response for debugging
        console.error('LinkedIn API error response:', JSON.stringify(data, null, 2));

        // Handle specific error codes
        switch (status) {
          case 400:
            const detailedError = data?.message || data?.error_description || 'Invalid request format';
            throw new Error(`LinkedIn API validation error: ${detailedError}. Please check the post content and try again.`);
          case 401:
            throw new UnauthorizedError('LinkedIn authentication failed. Your token may have expired. Please reconnect your LinkedIn account.');
          case 403:
            throw new UnauthorizedError('LinkedIn permission denied. Your account may not have the required permissions.');
          case 422:
            const validationError = data?.message || data?.error_description || 'Invalid request format';
            throw new Error(`LinkedIn API validation error: ${validationError}. Please check the post content and try again.`);
          case 429:
            throw new Error('LinkedIn API rate limit exceeded. Please try again later.');
          default:
            const unknownError = data?.message || data?.error_description || 'Unknown error';
            throw new Error(`LinkedIn API error (${status}): ${unknownError}`);
        }
      }

      throw error;
    }
  }
}

export const linkedInService = new LinkedInService();
