import { externalJobRepository, ExternalJobRepository, QueryExternalJobParams } from "@/server/repositories/ExternalJobRepository";
import BaseService from "@/server/core/BaseService";
import { IExternalJob } from "@/server/models/ExternalJob";
import { sendNewExternalJobNotificationToAllCandidates } from "@/server/utils/emailUtils";

export class ExternalJobService extends BaseService<ExternalJobRepository, IExternalJob, QueryExternalJobParams> {
    constructor() {
        super(externalJobRepository);
    }

    async create(data: IExternalJob): Promise<IExternalJob> {
        // Create the external job
        const externalJob = await super.create(data);

        if (externalJob) {
            // Send notifications to all candidates asynchronously
            this.sendNotificationToAllCandidates(externalJob).catch(error => {
                console.error('Error sending external job notifications:', error);
            });
        }

        return externalJob;
    }

    /**
     * Send notification emails to all candidates about the new external job
     *
     * @param externalJob The created external job
     */
    private async sendNotificationToAllCandidates(externalJob: IExternalJob): Promise<void> {
        try {
            await sendNewExternalJobNotificationToAllCandidates(
                externalJob.role,
                externalJob.recruiter_email,
                externalJob.job_link,
                externalJob.job_description
            );
        } catch (error) {
            console.error('Failed to send external job notifications:', error);
            // Don't throw the error to avoid affecting the job creation
        }
    }
}

export const externalJobService = new ExternalJobService();
