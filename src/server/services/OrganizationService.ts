import { organizationRepository, OrganizationRepository, QueryOrganizationParams } from "@/server/repositories";
import BaseService from "@/server/core/BaseService";
import { IOrganization } from "@/server/models";

export class OrganizationService extends BaseService<OrganizationRepository, IOrganization, QueryOrganizationParams> {
    constructor() {
        super(organizationRepository);
    }
}

export const organizationService = new OrganizationService();