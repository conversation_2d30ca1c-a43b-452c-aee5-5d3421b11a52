import { jobRepository, JobRepository, QueryJobParams, SearchJobParams, SimilarJobsParams, SkillBasedRecommendationParams, CandidateRecommendationParams } from "@/server/repositories";
import BaseService from "@/server/core/BaseService";
import { IJob, IJobApplication, ICandidate, IUser, IOrganization, ILocality, IRecruiter, ISkill } from "@/server/models";
import { QueryResult } from "@/server/core/BaseRepository";
import { revalidatePath } from "next/cache";
import { sendJobClosedNotification, sendNewC2CJobNotificationToAllCandidates } from "@/server/utils/emailUtils";

export class JobService extends BaseService<JobRepository, IJob, QueryJobParams> {
    constructor() {
        super(jobRepository);
    }

    /**
     * Updates the no_of_applicants field of the job by incrementing it by 1
     *
     * @param id
     */
    async incrementApplicantsCount(id: string): Promise<any> {
        return this.repository.incrementApplicantsCount(id);
    }

    async search(params: SearchJobParams): Promise<QueryResult<IJob>> {
        return this.repository.search(params);
    }

    async getSimilarJobs(jobId: string, params: SimilarJobsParams): Promise<QueryResult<any>> {
        return this.repository.findSimilarJobs(jobId, params);
    }

    async getRecommendationsBySkills(params: SkillBasedRecommendationParams): Promise<QueryResult<any>> {
        return this.repository.recommendJobsBySkills(params);
    }

    async getRecommendationsForCandidate(candidateId: string, params: CandidateRecommendationParams): Promise<QueryResult<any>> {
        return this.repository.recommendJobsForCandidate(candidateId, params);
    }

    async create(data: IJob): Promise<IJob> {
        const job = await super.create(data);
        if (job) {
            revalidatePath('/');
            revalidatePath('/[slug]');

            // Send notifications to all candidates asynchronously
            this.sendNotificationToAllCandidates(job).catch((error: any) => {
                console.error('Error sending C2C job notifications:', error);
            });
        }
        return job;
    }

    async update(id: string, data: IJob): Promise<IJob> {
        const job = await super.update(id, data);
        if (job) {
            revalidatePath('/');
            revalidatePath('/[slug]', 'page');
        }
        return job;
    }

    /**
     * Closes a job by setting its active status to false
     *
     * @param id Job ID
     * @returns Updated job
     */
    async closeJob(id: string): Promise<IJob> {
        const job = await this.repository.closeJob(id);
        if (job) {
            revalidatePath('/');
            revalidatePath('/[slug]', 'page');

            // Send notifications to all applicants
            await this.sendJobClosedNotifications(job);
        }
        return job;
    }

    /**
     * Send job closed notifications to all applicants
     *
     * @param job The closed job
     */
    private async sendJobClosedNotifications(job: IJob): Promise<void> {
        try {
            // Import JobApplicationService here to avoid circular dependency
            const { jobApplicationService } = await import('./JobApplicationService');

            // Get all applications for this job
            const applications = await jobApplicationService.getApplicationsByJobId(job._id.toString());

            // Extract job and company information
            const jobTitle = job.title || job.role;
            const organization = job.organization as IOrganization;
            const companyName = organization.name;

            // Send notification to each applicant
            const notificationPromises = applications.map(async (application: IJobApplication) => {
                const candidate = application.applicant as ICandidate;
                const user = candidate.user as IUser;

                if (user.email && user.name) {
                    return sendJobClosedNotification(
                        user.name,
                        user.email,
                        jobTitle,
                        companyName
                    );
                }
            });

            // Wait for all notifications to be sent
            await Promise.all(notificationPromises.filter(Boolean));

            console.log(`Job closed notifications sent for job ${job.job_id} to ${applications.length} applicants`);
        } catch (error) {
            console.error('Error sending job closed notifications:', error);
            // Don't throw error to prevent job closing from failing
        }
    }

    /**
     * Closes all jobs older than the specified number of days
     *
     * @param days Number of days to look back (default: 3)
     * @returns Object with count of closed jobs and any errors
     */
    async closeJobsOlderThan(days: number = 3): Promise<{ closedCount: number; errors: string[] }> {
        const errors: string[] = [];
        let closedCount = 0;

        try {
            // Find all jobs older than specified days
            const oldJobs = await this.repository.findJobsOlderThan(days);

            console.log(`Found ${oldJobs.length} jobs older than ${days} days to close`);

            // Close each job individually to trigger notifications
            for (const job of oldJobs) {
                try {
                    await this.closeJob(job._id.toString());
                    closedCount++;
                    console.log(`Closed job ${job.job_id} (${job.title})`);
                } catch (error) {
                    const errorMessage = `Failed to close job ${job.job_id}: ${error instanceof Error ? error.message : String(error)}`;
                    console.error(errorMessage);
                    errors.push(errorMessage);
                }
            }

            console.log(`Successfully closed ${closedCount} out of ${oldJobs.length} old jobs`);

            return { closedCount, errors };
        } catch (error) {
            const errorMessage = `Error finding old jobs: ${error instanceof Error ? error.message : String(error)}`;
            console.error(errorMessage);
            errors.push(errorMessage);
            return { closedCount, errors };
        }
    }

    /**
     * Send notification emails to all candidates about the new C2C job
     *
     * @param job The created job
     */
    private async sendNotificationToAllCandidates(job: IJob): Promise<void> {
        try {
            // Extract job information
            const locality = job.locality as ILocality;
            const organization = job.organization as IOrganization;
            const recruiter = job.posted_by as IRecruiter;
            const recruiterUser = recruiter.user as IUser;
            const skills = job.skills as ISkill[];

            const jobTitle = job.title || job.role;
            const companyName = organization.name;
            const location = locality ? `${locality.city}, ${locality.state}` : 'Remote';
            const jobType = job.job_type;
            const jobId = job.job_id;
            const jobUrl = job.seo_link;
            const jobDescription = job.description;
            const recruiterName = recruiterUser?.name || 'Recruiter';
            const skillNames = skills?.map(skill => skill.name).join(', ') || '';

            await sendNewC2CJobNotificationToAllCandidates(
                jobTitle,
                companyName,
                location,
                jobType,
                jobId,
                jobUrl,
                jobDescription,
                recruiterName,
                {
                    hourlyRate: job.hourly_rate || undefined,
                    skills: skillNames || undefined,
                }
            );
        } catch (error) {
            console.error('Failed to send C2C job notifications:', error);
            // Don't throw the error to avoid affecting the job creation
        }
    }
}

export const jobService = new JobService();