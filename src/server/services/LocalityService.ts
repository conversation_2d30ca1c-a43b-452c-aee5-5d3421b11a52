import BaseService from "@/server/core/BaseService";
import { ILocality } from "@/server/models";
import { localityRepository, LocalityRepository, QueryLocalityParams } from "@/server/repositories";

export class LocalityService extends BaseService<LocalityRepository, ILocality, QueryLocalityParams> {
    constructor() {
        super(localityRepository);
    }

    async batchCreate(localities: ILocality[]): Promise<ILocality[]> {
        return this.repository.batchCreate(localities);
    }
}

export const localityService = new LocalityService();