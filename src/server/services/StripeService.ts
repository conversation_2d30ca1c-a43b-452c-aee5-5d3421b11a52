import Stripe from 'stripe';
import { PlanType } from '@/server/enums';

export class StripeService {
    private stripe: Stripe;

    constructor() {
        const secretKey = process.env.STRIPE_SECRET_KEY;
        if (!secretKey) {
            throw new Error('STRIPE_SECRET_KEY is not set in environment variables');
        }
        
        this.stripe = new Stripe(secretKey, {
            apiVersion: '2024-12-18.basil',
        });
    }

    /**
     * Create a Stripe customer
     */
    async createCustomer(email: string, name?: string): Promise<Stripe.Customer> {
        return await this.stripe.customers.create({
            email,
            name: name || undefined,
        });
    }

    /**
     * Create a checkout session for subscription
     */
    async createCheckoutSession(
        customerId: string,
        priceId: string,
        successUrl: string,
        cancelUrl: string,
        metadata?: Record<string, string>
    ): Promise<Stripe.Checkout.Session> {
        return await this.stripe.checkout.sessions.create({
            customer: customerId,
            payment_method_types: ['card'],
            line_items: [
                {
                    price: priceId,
                    quantity: 1,
                },
            ],
            mode: 'subscription',
            success_url: successUrl,
            cancel_url: cancelUrl,
            metadata: metadata || {},
        });
    }

    /**
     * Create a customer portal session
     */
    async createPortalSession(
        customerId: string,
        returnUrl: string
    ): Promise<Stripe.BillingPortal.Session> {
        return await this.stripe.billingPortal.sessions.create({
            customer: customerId,
            return_url: returnUrl,
        });
    }

    /**
     * Get subscription by ID
     */
    async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
        return await this.stripe.subscriptions.retrieve(subscriptionId);
    }

    /**
     * Cancel subscription
     */
    async cancelSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
        return await this.stripe.subscriptions.update(subscriptionId, {
            cancel_at_period_end: true,
        });
    }

    /**
     * Reactivate subscription
     */
    async reactivateSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
        return await this.stripe.subscriptions.update(subscriptionId, {
            cancel_at_period_end: false,
        });
    }

    /**
     * Get price ID for plan type
     */
    getPriceId(planType: PlanType): string {
        switch (planType) {
            case PlanType.CANDIDATE_BASIC:
                return process.env.STRIPE_CANDIDATE_BASIC_PRICE_ID || '';
            case PlanType.CANDIDATE_PRO:
                return process.env.STRIPE_CANDIDATE_PRO_PRICE_ID || '';
            default:
                throw new Error(`Unsupported plan type: ${planType}`);
        }
    }

    /**
     * Construct webhook event
     */
    constructWebhookEvent(
        payload: string | Buffer,
        signature: string,
        secret: string
    ): Stripe.Event {
        return this.stripe.webhooks.constructEvent(payload, signature, secret);
    }

    /**
     * Get customer by ID
     */
    async getCustomer(customerId: string): Promise<Stripe.Customer> {
        const customer = await this.stripe.customers.retrieve(customerId);
        if (customer.deleted) {
            throw new Error('Customer has been deleted');
        }
        return customer as Stripe.Customer;
    }
}

export const stripeService = new StripeService();
