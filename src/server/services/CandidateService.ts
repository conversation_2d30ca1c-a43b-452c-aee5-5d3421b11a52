import BaseService from "@/server/core/BaseService";
import { ICandidate } from "@/server/models";
import { candidateRepository, CandidateRepository, QueryCandidateParams } from "@/server/repositories";

export class CandidateService extends BaseService<CandidateRepository, ICandidate, QueryCandidateParams> {
    constructor() {
        super(candidateRepository);
    }

    async getByUserId(userId: string): Promise<ICandidate | undefined> {
        return this.repository.getByUserId(userId);
    }

    async getAllCandidatesWithEmails(): Promise<ICandidate[]> {
        return this.repository.getAllCandidatesWithEmails();
    }
}

export const candidateService = new CandidateService();