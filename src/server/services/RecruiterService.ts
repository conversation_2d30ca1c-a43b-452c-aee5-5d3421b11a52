import BaseService from "@/server/core/BaseService";
import { QueryRecruiterParams, recruiterRepository, RecruiterRepository } from "@/server/repositories";
import { IRecruiter } from "@/server/models";

export class RecruiterService extends BaseService<RecruiterRepository, IRecruiter, QueryRecruiterParams> {
    constructor() {
        super(recruiterRepository);
    }

    async getByUserId(userId: string): Promise<IRecruiter | undefined> {
        return this.repository.getByUserId(userId);
    }
}

export const recruiterService = new RecruiterService();