import BaseService from "@/server/core/BaseService";
import { ISubscription } from "@/server/models";
import { QuerySubscriptionParams, SubscriptionRepository, subscriptionRepository } from "@/server/repositories";

export class SubscriptionService extends BaseService<SubscriptionRepository, ISubscription, QuerySubscriptionParams> {
    constructor() {
        super(subscriptionRepository);
    }

    /**
     * Find a subscription by email
     * 
     * @param email Email to search for
     * @returns Subscription if found, null otherwise
     */
    async findByEmail(email: string): Promise<ISubscription | null> {
        return this.repository.findByEmail(email);
    }

    /**
     * Create or update a subscription
     * 
     * @param email Email to subscribe
     * @param marketing Whether to subscribe to marketing emails
     * @returns Created or updated subscription
     */
    async createOrUpdate(email: string, marketing: boolean): Promise<ISubscription> {
        const existing = await this.findByEmail(email);
        
        if (existing) {
            return this.update(existing._id.toString(), { 
                ...existing, 
                marketing 
            } as ISubscription);
        }
        
        return this.create({ 
            email, 
            marketing 
        } as ISubscription);
    }
}

export const subscriptionService = new SubscriptionService();
