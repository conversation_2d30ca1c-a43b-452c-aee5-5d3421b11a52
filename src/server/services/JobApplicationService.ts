import BaseService from "@/server/core/BaseService";
import { ErrorNotFound } from "@/server/core/Errors";
import { JobApplicationStatus } from "@/server/enums/job_application_status";
import { ICandidate, IJob, IJobApplication, IOrganization, I<PERSON><PERSON>ruiter, IUser } from "@/server/models";
import { jobApplicationRepository, JobApplicationRepository, QueryJobApplicationParams } from "@/server/repositories";
import { jobService } from "./JobService";
import {
    sendJobApplicationConfirmationEmail,
    sendJobApplicationNotificationToRecruiter,
    sendApplicationViewedNotification,
    sendApplicationRejectedNotification,
    sendApplicationShortlistedNotification,
    sendApplicationHiredNotification
} from "@/server/utils/emailUtils";

export class JobApplicationService extends BaseService<JobApplicationRepository, IJobApplication, QueryJobApplicationParams> {
    constructor() {
        super(jobApplicationRepository);
    }

    /**
     * Create a new job application and send confirmation emails
     *
     * @param data Job application data
     * @returns Created job application
     */
    async create(data: IJobApplication): Promise<IJobApplication> {
        // Create the job application
        const jobApplication = await this.repository.create(data);
        await jobService.incrementApplicantsCount(data.job.toString());

        // Get the populated job application with all related data
        const populatedApplication = await this.getById(jobApplication._id.toString());

        // Send confirmation emails
        await this.sendApplicationCreatedEmails(populatedApplication);

        return populatedApplication;
    }

    /**
     * Update the status of a job application and send appropriate notifications
     *
     * @param id Job application ID
     * @param status New status
     * @returns Updated job application
     */
    async updateStatus(id: string, status: JobApplicationStatus): Promise<IJobApplication> {
        // Get the current application to check if status is actually changing
        const currentApplication = await this.getById(id);
        if (!currentApplication) {
            throw new ErrorNotFound(`Job application(${id}) not found`);
        }

        // If status is not changing, return the current application
        if (currentApplication.status === status) {
            return currentApplication;
        }

        // Update the status
        const updatedApplication = await this.repository.update(id, { status });

        // Send appropriate notifications based on the new status
        switch (status) {
            case JobApplicationStatus.READ:
                await this.sendApplicationViewedEmail(updatedApplication);
                break;

            case JobApplicationStatus.SHORTLISTED:
                await this.sendApplicationShortlistedEmail(updatedApplication);
                break;

            case JobApplicationStatus.REJECTED:
                await this.sendApplicationRejectedEmail(updatedApplication);
                break;

            case JobApplicationStatus.HIRED:
                await this.sendApplicationHiredEmail(updatedApplication);
                break;
        }

        return updatedApplication;
    }

    /**
     * Get all job applications for a specific job
     *
     * @param jobId Job ID
     * @returns List of job applications
     */
    async getApplicationsByJobId(jobId: string): Promise<IJobApplication[]> {
        return this.repository.getApplicationsByJobId(jobId);
    }

    /**
     * Send confirmation emails when a job application is created
     *
     * @param application Job application
     */
    private async sendApplicationCreatedEmails(application: IJobApplication): Promise<void> {
        try {
            const { job, candidateName, candidateEmail, jobTitle, companyName } = this.extractEmailData(application);
            const recruiter = application.recruiter as IRecruiter;
            const recruiterUser = recruiter.user as IUser;
            const candidateApplicationUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/my-applications/applications?job_id=${job._id.toString()}`;

            // Send confirmation email to the candidate
            await sendJobApplicationConfirmationEmail(
                candidateName,
                candidateEmail,
                jobTitle,
                companyName,
                candidateApplicationUrl,
                {
                    location: job.locality ? `${(job.locality as any).city}, ${(job.locality as any).state}` : undefined,
                    jobType: job.job_type
                }
            );

            const jobId = (application.job as IJob)._id.toString();
            const applicantId = (application.applicant as ICandidate)._id.toString();
            const applicationUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/dashboard/job/${jobId}?applicant_id=${applicantId}`;
            // Send notification email to the recruiter
            await sendJobApplicationNotificationToRecruiter(
                recruiterUser.name || 'Recruiter',
                recruiterUser.email,
                candidateName,
                jobTitle,
                application.created_at || new Date(),
                applicationUrl,
            );
        } catch (error) {
            console.error('Error sending job application emails:', error);
        }
    }

    /**
     * Extract common data from job application for email notifications
     *
     * @param application Job application
     * @returns Object containing common data for email notifications
     */
    private extractEmailData(application: IJobApplication): {
        job: IJob;
        applicant: ICandidate;
        applicantUser: IUser;
        organization: IOrganization;
        applicationId: string;
        jobTitle: string;
        candidateName: string;
        candidateEmail: string;
        companyName: string;
    } {
        const job = application.job as IJob;
        const applicant = application.applicant as ICandidate;
        const applicantUser = applicant.user as IUser;
        const organization = application.organization as IOrganization;

        return {
            job,
            applicant,
            applicantUser,
            organization,
            applicationId: application._id.toString(),
            jobTitle: job.title || job.role,
            candidateName: applicantUser.name || 'Candidate',
            candidateEmail: applicantUser.email,
            companyName: organization.name
        };
    }

    /**
     * Send notification email when a job application is viewed
     *
     * @param application Job application
     */
    private async sendApplicationViewedEmail(application: IJobApplication): Promise<void> {
        try {
            const { candidateName, candidateEmail, jobTitle, companyName, applicationId } = this.extractEmailData(application);
            const jobId = (application.job as IJob)._id.toString();
            const candidateApplicationUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/my-applications/applications?job_id=${jobId}`;

            await sendApplicationViewedNotification(
                candidateName,
                candidateEmail,
                jobTitle,
                companyName,
                applicationId,
                candidateApplicationUrl,
            );
        } catch (error) {
            console.error('Error sending application viewed email:', error);
        }
    }

    /**
     * Send notification email when a job application is shortlisted
     *
     * @param application Job application
     */
    private async sendApplicationShortlistedEmail(application: IJobApplication): Promise<void> {
        try {
            const { candidateName, candidateEmail, jobTitle, companyName, applicationId } = this.extractEmailData(application);
            const jobId = (application.job as IJob)._id.toString();
            const candidateApplicationUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/my-applications/applications?job_id=${jobId}`;

            await sendApplicationShortlistedNotification(
                candidateName,
                candidateEmail,
                jobTitle,
                companyName,
                applicationId,
                candidateApplicationUrl,
            );
        } catch (error) {
            console.error('Error sending application shortlisted email:', error);
        }
    }

    /**
     * Send notification email when a job application is rejected
     *
     * @param application Job application
     */
    private async sendApplicationRejectedEmail(application: IJobApplication): Promise<void> {
        try {
            const { candidateName, candidateEmail, jobTitle, companyName, applicant } = this.extractEmailData(application);

            // Get candidate skills as comma-separated string
            const skills = applicant.skills ?
                (applicant.skills as any[]).map(skill => skill.name || '').filter(Boolean).join(',') :
                '';

            // For simplicity, we'll just say there are 5 recommended jobs
            // In a real implementation, you would query for matching jobs based on skills
            const recommendedJobsCount = 5;

            await sendApplicationRejectedNotification(
                candidateName,
                candidateEmail,
                jobTitle,
                companyName,
                skills,
                recommendedJobsCount
            );
        } catch (error) {
            console.error('Error sending application rejected email:', error);
        }
    }

    /**
     * Send notification email when a candidate is hired
     *
     * @param application Job application
     */
    private async sendApplicationHiredEmail(application: IJobApplication): Promise<void> {
        try {
            const { candidateName, candidateEmail, jobTitle, companyName, applicationId } = this.extractEmailData(application);
            const jobId = (application.job as IJob)._id.toString();
            const candidateApplicationUrl = `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/my-applications/applications?job_id=${jobId}`;

            await sendApplicationHiredNotification(
                candidateName,
                candidateEmail,
                jobTitle,
                companyName,
                applicationId,
                candidateApplicationUrl,
            );
        } catch (error) {
            console.error('Error sending application hired email:', error);
        }
    }
}

export const jobApplicationService = new JobApplicationService();