import { NextRequest, NextResponse } from "next/server";
import { frontendMiddleware, backendMiddleware } from "@/middleguard";

/**
 * Middleware configuration
 * Defines which routes the middleware should run on
 */
export const config = {
    matcher: [
        '/api/v1/:path*',
        '/dashboard/:path*',
        '/my-applications/:path*',
    ],
};

/**
 * Main middleware function
 * Routes requests to the appropriate middleware based on the URL
 */
export default async function middleware(request: NextRequest) {
    const url = request.nextUrl.pathname;

    // Route to frontend middleware for dashboard routes
    if (url.startsWith('/dashboard') || url.startsWith('/my-applications')) {
        return frontendMiddleware(request);
    }

    // Route to backend middleware for API routes
    if (url.startsWith('/api/v1')) {
        return backendMiddleware(request);
    }

    // Allow all other requests to pass through
    return NextResponse.next();
}