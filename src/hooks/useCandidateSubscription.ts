'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';

interface SubscriptionData {
    hasSubscription: boolean;
    isActive: boolean;
    subscription?: {
        id: string;
        status: string;
        planType: string;
        currentPeriodStart: string;
        currentPeriodEnd: string;
        cancelAtPeriodEnd: boolean;
        createdAt: string;
        updatedAt: string;
    };
}

export function useCandidateSubscription() {
    const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchSubscriptionStatus = async () => {
        try {
            setLoading(true);
            const response = await axios.get(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/subscriptions/premium/candidates/status`
            );
            setSubscriptionData(response.data.data);
            setError(null);
        } catch (error: any) {
            console.error('Error fetching subscription status:', error);
            setError(error.response?.data?.message || 'Failed to fetch subscription status');
        } finally {
            setLoading(false);
        }
    };

    const createCheckoutSession = async (planType: string, successUrl: string, cancelUrl: string) => {
        try {
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/subscriptions/premium/candidates/create-checkout-session`,
                {
                    planType,
                    successUrl,
                    cancelUrl
                }
            );
            
            if (response.data.data.sessionUrl) {
                window.location.href = response.data.data.sessionUrl;
            }
            
            return response.data.data;
        } catch (error: any) {
            console.error('Error creating checkout session:', error);
            throw new Error(error.response?.data?.message || 'Failed to create checkout session');
        }
    };

    const createPortalSession = async (returnUrl: string) => {
        try {
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/subscriptions/premium/candidates/create-portal-session`,
                {
                    returnUrl
                }
            );
            
            if (response.data.data.portalUrl) {
                window.location.href = response.data.data.portalUrl;
            }
            
            return response.data.data;
        } catch (error: any) {
            console.error('Error creating portal session:', error);
            throw new Error(error.response?.data?.message || 'Failed to create portal session');
        }
    };

    const cancelSubscription = async (reason?: string) => {
        try {
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_ONLYC2C_BASE_URL}/api/v1/subscriptions/premium/candidates/cancel`,
                {
                    reason
                }
            );
            
            // Refresh subscription data after cancellation
            await fetchSubscriptionStatus();
            
            return response.data.data;
        } catch (error: any) {
            console.error('Error cancelling subscription:', error);
            throw new Error(error.response?.data?.message || 'Failed to cancel subscription');
        }
    };

    useEffect(() => {
        fetchSubscriptionStatus();
    }, []);

    return {
        subscriptionData,
        loading,
        error,
        refetch: fetchSubscriptionStatus,
        createCheckoutSession,
        createPortalSession,
        cancelSubscription,
        isPremium: subscriptionData?.isActive || false,
        hasSubscription: subscriptionData?.hasSubscription || false
    };
}
