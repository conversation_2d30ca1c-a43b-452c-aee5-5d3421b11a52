<!-- Subject: 🚀 New C2C Opportunity: {{jobTitle}} at {{companyName}} - Apply Now! 💼 -->
{{#> components/layout title="New C2C Job Opportunity - OnlyC2C" logoImage="onlyc2c_for_candidates.png" year=year}}
<p class="greeting">Hello {{candidateName}}! 👋</p>
<p class="main-message">Fresh C2C Opportunity Alert! 🎯</p>
<p class="sub-message">
  We've just posted an exciting new contract-to-contract opportunity that matches your profile!
</p>

<div style="background-color: #f8f9fa; border-left: 4px solid #007bff; padding: 20px; margin: 20px 0; border-radius: 4px;">
  <h3 style="margin: 0 0 10px 0; color: #007bff;">{{jobTitle}}</h3>
  <p style="margin: 0 0 10px 0; color: #6c757d;">
    <strong>Company:</strong> {{companyName}}
  </p>
  <p style="margin: 0 0 10px 0; color: #6c757d;">
    <strong>Location:</strong> {{location}}
  </p>
  <p style="margin: 0 0 10px 0; color: #6c757d;">
    <strong>Job Type:</strong> {{jobType}}
  </p>
  {{#if hourlyRate}}
  <p style="margin: 0 0 10px 0; color: #6c757d;">
    <strong>Rate:</strong> {{hourlyRate}}
  </p>
  {{/if}}
  {{#if skills}}
  <p style="margin: 0 0 10px 0; color: #6c757d;">
    <strong>Skills:</strong> {{skills}}
  </p>
  {{/if}}
  <p style="margin: 0; color: #495057; font-size: 14px; line-height: 1.5;">
    {{jobDescription}}
  </p>
</div>

<p class="message-text">
  This is a verified opportunity posted by a trusted recruiter on our platform. 
  Don't miss out on this chance to advance your career! 🚀
</p>

<p class="message-text">
  <strong>Ready to apply?</strong> Click the link below to view the full job details and submit your application:
</p>

<a href="{{jobUrl}}" class="button" style="color: #fff;">🔗 View Job & Apply Now</a>

<p class="message-text" style="margin-top: 30px; font-size: 14px; color: #6c757d;">
  <em>Job ID: {{jobId}} | Posted by {{recruiterName}} at {{companyName}}</em>
</p>

<p class="message-text" style="font-size: 14px; color: #6c757d;">
  Keep exploring more opportunities on our platform and stay ahead in your career journey! 💪
</p>
{{/components/layout}}
