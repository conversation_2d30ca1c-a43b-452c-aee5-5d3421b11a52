@import url('https://fonts.googleapis.com/css2?family=Geist+Sans:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'Geist Sans', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
  }

  .container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 2px solid #f5f7ff;
  }

  .header {
    background-color: #f5f7ff;
    padding: 20px 0;
    text-align: center;
  }

  .logo {
    max-width: 250px;
  }

  .content {
    padding: 30px 40px;
    background-color: #fff;
    text-align: center;
  }

  .main-message {
    font-size: 28px;
    font-weight: bold;
    color: #0077B6;
    margin-bottom: 20px;
  }

  .sub-message {
    font-size: 16px;
    margin-bottom: 20px;
    color: #4a0072;
    font-weight: 500;
  }

  .greeting {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
  }

  .message-text {
    font-size: 14px;
    color: #333;
    margin: 15px auto;
    max-width: 450px;
    line-height: 1.5;
  }

  .button {
    display: inline-block;
    background-color: #1976d2;
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    margin: 25px 0;
    transition: background-color 0.3s ease;
  }

  .button:hover {
    background-color: #1565c0;
  }

  .footer {
    text-align: center;
    padding: 20px;
    font-size: 12px;
    color: #666;
    background-color: #f5f7ff;
  }

  .social-icons {
    margin-bottom: 15px;
  }

  .social-icons a {
    display: inline-block;
    margin: 0 5px;
    text-decoration: none;
  }

  .social-icons img {
    width: 20px;
    height: 20px;
  }

  .footer-text {
    margin: 8px 0;
    font-size: 12px;
    color: #666;
  }

  .footer-links {
    margin-top: 10px;
  }

  .footer-links a {
    color: #1976d2;
    text-decoration: none;
    margin: 0 8px;
    font-size: 12px;
  }

  .footer-separator {
    color: #666;
    margin: 0 2px;
  }

  li {
    text-align: start
  }