#!/bin/bash

# This script is used to set up the initial deployment environment on the Linode server
# It should be run once when setting up a new server

# Update package lists
apt-get update

# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Install PM2 globally
npm install -g pm2

# Create project directory
mkdir -p ~/onlyc2c-candidate-frontend

# Set up PM2 to start on boot
pm2 startup
pm2 save

echo "Server setup completed successfully!"
echo "Now you can deploy the application using GitHub Actions or the deploy.sh script."
