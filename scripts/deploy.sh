#!/bin/bash

# This script is used to deploy the application on the Linode server
# It should be placed in the root directory of the project on the server

# Navigate to the project directory
cd ~/onlyc2c-candidate-frontend

# Pull the latest changes from the repository
git pull

# Install dependencies
npm ci

# Build the application
npm run build

# Restart the PM2 process
pm2 restart onlyc2c-candidate || pm2 start npm --name "onlyc2c-candidate" -- run start

echo "Deployment completed successfully!"
