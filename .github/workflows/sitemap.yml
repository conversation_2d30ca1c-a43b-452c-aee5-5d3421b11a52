name: Sitemap Submission

on:
  # Run daily at 2:00 AM CST (8:00 AM UTC)
  schedule:
    - cron: '0 8 * * *'

  # Allow manual triggering
  workflow_dispatch:

jobs:
  submit-sitemap:
    runs-on: ubuntu-latest

    steps:
      - name: Submit Sitemap to Search Engines
        id: submit-sitemap
        uses: fjogeleit/http-request-action@v1
        with:
          url: 'https://onlyc2c.com/api/cron/submit-sitemap'
          method: 'POST'
          customHeaders: "{\"x-c2c-api-key\": \"${{ secrets.X_CRON_API_KEY }}\"}"

      - name: Check Submission Result
        run: |
          echo "Status code: ${{ steps.submit-sitemap.outputs.status }}"
          echo "Response: ${{ steps.submit-sitemap.outputs.response }}"

          # Check if the submission was successful
          if [[ "${{ steps.submit-sitemap.outputs.status }}" != "200" ]]; then
            echo "::error::Sitemap submission failed with status code ${{ steps.submit-sitemap.outputs.status }}"
            exit 1
          fi

          # Parse the response to check for success field
          SUCCESS=$(echo '${{ steps.submit-sitemap.outputs.response }}' | jq -r '.success')
          if [[ "$SUCCESS" != "true" ]]; then
            echo "::error::Sitemap submission reported failure in response"
            echo "Message: $(echo '${{ steps.submit-sitemap.outputs.response }}' | jq -r '.message')"
            exit 1
          fi

          echo "Sitemap submission completed successfully!"
