name: Close Old Jobs

on:
  # Run daily at 3:30 PM CST (9:30 PM UTC)
  schedule:
    - cron: '30 21 * * *'

  # Allow manual triggering
  workflow_dispatch:

jobs:
  close-jobs:
    runs-on: ubuntu-latest

    steps:
      - name: Close Old Jobs
        id: close-jobs
        uses: fjogeleit/http-request-action@v1
        with:
          url: 'https://onlyc2c.com/api/cron/close-jobs'
          method: 'POST'
          customHeaders: "{\"x-c2c-api-key\": \"${{ secrets.X_CRON_API_KEY }}\", \"Content-Type\": \"application/json\"}"
          data: '{"days": 3}'

      - name: Check Close Jobs Result
        run: |
          echo "Status code: ${{ steps.close-jobs.outputs.status }}"
          echo "Response: ${{ steps.close-jobs.outputs.response }}"

          # Check if the request was successful
          if [[ "${{ steps.close-jobs.outputs.status }}" != "200" ]]; then
            echo "::error::Close jobs request failed with status code ${{ steps.close-jobs.outputs.status }}"
            exit 1
          fi

          # Parse the response to check for success field
          SUCCESS=$(echo '${{ steps.close-jobs.outputs.response }}' | jq -r '.success')
          if [[ "$SUCCESS" != "true" ]]; then
            echo "::error::Close jobs process reported failure in response"
            echo "Message: $(echo '${{ steps.close-jobs.outputs.response }}' | jq -r '.message')"
            exit 1
          fi

          # Display results
          CLOSED_COUNT=$(echo '${{ steps.close-jobs.outputs.response }}' | jq -r '.data.closedCount')
          DAYS_THRESHOLD=$(echo '${{ steps.close-jobs.outputs.response }}' | jq -r '.data.daysThreshold')
          HAS_ERRORS=$(echo '${{ steps.close-jobs.outputs.response }}' | jq -r '.data.hasErrors')
          
          echo "Jobs closed: $CLOSED_COUNT"
          echo "Days threshold: $DAYS_THRESHOLD"
          echo "Has errors: $HAS_ERRORS"

          # Show errors if any
          if [[ "$HAS_ERRORS" == "true" ]]; then
            echo "::warning::Some jobs failed to close:"
            echo '${{ steps.close-jobs.outputs.response }}' | jq -r '.data.errors[]'
          fi

          echo "Close jobs process completed successfully!"
