name: Deployment

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Set up SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.LINODE_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.LINODE_HOST }} >> ~/.ssh/known_hosts

      - name: Start SSH agent and add key
        run: |
          eval "$(ssh-agent -s)"
          ssh-add ~/.ssh/id_rsa
          
      - name: Test SSH connection
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no root@************** "echo Connected successfully!"

      - name: Deploy to Linode
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.LINODE_USER }}@${{ secrets.LINODE_HOST }} "
            echo 'Connected successfully!' &&
            cd ~/onlyc2c-candidate-frontend &&
            git fetch origin main &&
            git reset --hard origin/main &&     # Discard all local changes
            git clean -fd &&                    # Remove untracked files and directories
            npm ci &&
            NODE_OPTIONS='--max_old_space_size=4096' npx next build &&
            pm2 start 'npx next start' || pm2 restart 'npx next start'
          "
