{"name": "onlyc2c_candicate_app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate-sitemaps": "ts-node -r tsconfig-paths/register src/scripts/generate-sitemaps.ts", "submit-sitemaps": "ts-node -r tsconfig-paths/register src/scripts/submit-sitemaps.ts", "generate-api-key": "ts-node -r tsconfig-paths/register src/scripts/generate-api-key.ts", "fix-seo-link": "ts-node -r tsconfig-paths/register src/scripts/fix-seo-links.ts", "test-close-jobs": "ts-node -r tsconfig-paths/register src/scripts/test-close-jobs.ts", "test-job-recommendations": "ts-node -r tsconfig-paths/register src/scripts/test-job-recommendations.ts"}, "dependencies": {"-": "^0.0.1", "@ai-sdk/google": "^1.1.25", "@auth/core": "^0.37.4", "@auth/mongodb-adapter": "^3.8.0", "@google-cloud/storage": "^7.15.2", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "ai": "^4.1.61", "axios": "^1.8.4", "bootstrap": "^5.3.3", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "framer-motion": "^12.4.7", "glob": "^11.0.1", "googleapis": "^148.0.0", "handlebars": "^4.7.8", "heic-convert": "^2.1.0", "joi": "^17.13.3", "jose": "^6.0.8", "lodash": "^4.17.21", "lucide-react": "^0.263.1", "mdb-ui-kit": "^8.2.0", "mongodb": "^6.14.1", "mongoose": "^8.10.1", "next": "15.1.7", "next-auth": "^5.0.0-beta.25", "nodemailer": "^6.10.0", "p-queue": "^8.1.0", "path": "^0.12.7", "react": "^18.0.0", "react-dom": "^18.2.0", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-select": "^5.10.0", "react-toastify": "^11.0.5", "save-dev": "^0.0.1-security"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/heic-convert": "^2.1.0", "@types/node": "^22.13.4", "@types/node-jose": "^1.1.13", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.8", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}}