# OnlyC2C Documentation

Welcome to the OnlyC2C documentation. This repository contains comprehensive documentation for the OnlyC2C candidate frontend application.

## Documentation Structure

- **[Getting Started](./getting-started/)**: Setup guides and initial configuration
  - [Installation](./getting-started/installation.md): How to install and set up the project
  - [Environment Setup](./getting-started/environment-setup.md): Environment variables and configuration
  - [Development Workflow](./getting-started/development-workflow.md): Development process and guidelines
  - [NPM Scripts](./getting-started/npm-scripts.md): Documentation for all npm scripts

- **[Deployment](./deployment/)**: Deployment guides and server setup
  - [Linode Setup](./deployment/linode-setup.md): Linode server setup instructions
  - [GitHub Actions](./deployment/github-actions.md): GitHub Actions workflows
  - [Monitoring](./deployment/monitoring.md): Production monitoring and maintenance

- **[Features](./features/)**: Documentation for key features
  - [Authentication](./features/authentication/): Authentication system
  - [Email System](./features/email-system/): Email service and templates
  - [Job Applications](./features/job-applications/): Job application workflow
  - [SEO](./features/seo/): SEO features including sitemaps

- **[Security](./security/)**: Security documentation
  - [API Keys](./security/api-keys.md): API key management

- **[Troubleshooting](./troubleshooting/)**: Troubleshooting guides
  - [Common Issues](./troubleshooting/common-issues.md): Common issues and solutions
  - [Debugging](./troubleshooting/debugging.md): Debugging techniques

## Documentation Guidelines

When contributing to this documentation, please follow these guidelines:

1. **Structure**: Use consistent header levels (# for title, ## for sections, ### for subsections)
2. **Code Blocks**: Always specify language for syntax highlighting (e.g., ```typescript)
3. **Links**: Use relative links between documentation files
4. **Images**: Store in an assets subfolder within docs
5. **Tables**: Use for structured data like API parameters or environment variables
6. **Callouts**: Use blockquotes for important notes, warnings, or tips
7. **Versioning**: Include "Last updated" date at the top of each document

## About OnlyC2C

OnlyC2C is a platform dedicated to connecting candidates with Contract-to-Contract (C2C) job opportunities. The platform streamlines the job search and application process for contractors while providing recruiters with tools to find qualified candidates.
