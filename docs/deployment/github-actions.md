# GitHub Actions Workflows

> _Last updated: May 2025_

This document explains the GitHub Actions workflows used for deployment and maintenance of the OnlyC2C application.

## Table of Contents

1. [Overview](#overview)
2. [Deployment Workflow](#deployment-workflow)
3. [Sitemap Submission Workflow](#sitemap-submission-workflow)
4. [Required Secrets](#required-secrets)
5. [Manual Triggering](#manual-triggering)
6. [Troubleshooting](#troubleshooting)

## Overview

OnlyC2C uses GitHub Actions for two main purposes:

1. **Automated Deployment**: Deploying the application to the Linode server when changes are pushed to the main branch
2. **Sitemap Submission**: Submitting the sitemap to search engines on a daily schedule

## Deployment Workflow

The deployment workflow is defined in `.github/workflows/deploy.yml` and is triggered whenever changes are pushed to the main branch.

```yaml
name: Deployment

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Set up SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.LINODE_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.LINODE_HOST }} >> ~/.ssh/known_hosts

      - name: Start SSH agent and add key
        run: |
          eval "$(ssh-agent -s)"
          ssh-add ~/.ssh/id_rsa
          
      - name: Test SSH connection
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.LINODE_USER }}@${{ secrets.LINODE_HOST }} "echo Connected successfully!"

      - name: Deploy to Linode
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.LINODE_USER }}@${{ secrets.LINODE_HOST }} "
            echo 'Connected successfully!' &&
            cd ~/onlyc2c-candidate-frontend &&
            git fetch origin main &&
            git reset --hard origin/main &&     # Discard all local changes
            git clean -fd &&                    # Remove untracked files and directories
            npm ci &&
            NODE_OPTIONS='--max_old_space_size=4096' npx next build &&
            pm2 start 'npx next start' || pm2 restart 'npx next start'
          "
```

### Deployment Process

The workflow performs the following steps:

1. Sets up the SSH key from GitHub secrets
2. Starts the SSH agent and adds the key
3. Tests the SSH connection to the Linode server
4. Connects to the Linode server and:
   - Pulls the latest changes from the main branch
   - Installs dependencies
   - Builds the application
   - Starts or restarts the PM2 process

## Sitemap Submission Workflow

The sitemap submission workflow is defined in `.github/workflows/sitemap.yml` and runs on a daily schedule.

```yaml
name: Sitemap Submission

on:
  # Run daily at 2:00 AM CST (8:00 AM UTC)
  schedule:
    - cron: '0 8 * * *'

  # Allow manual triggering
  workflow_dispatch:

jobs:
  submit-sitemap:
    runs-on: ubuntu-latest

    steps:
      - name: Submit Sitemap to Search Engines
        id: submit-sitemap
        uses: fjogeleit/http-request-action@v1
        with:
          url: 'https://onlyc2c.com/api/cron/submit-sitemap'
          method: 'POST'
          customHeaders: "{\"x-c2c-api-key\": \"${{ secrets.X_CRON_API_KEY }}\"}"

      - name: Check Submission Result
        run: |
          echo "Status code: ${{ steps.submit-sitemap.outputs.status }}"
          echo "Response: ${{ steps.submit-sitemap.outputs.response }}"

          # Check if the submission was successful
          if [[ "${{ steps.submit-sitemap.outputs.status }}" != "200" ]]; then
            echo "::error::Sitemap submission failed with status code ${{ steps.submit-sitemap.outputs.status }}"
            exit 1
          fi

          # Parse the response to check for success field
          SUCCESS=$(echo '${{ steps.submit-sitemap.outputs.response }}' | jq -r '.success')
          if [[ "$SUCCESS" != "true" ]]; then
            echo "::error::Sitemap submission reported failure in response"
            echo "Message: $(echo '${{ steps.submit-sitemap.outputs.response }}' | jq -r '.message')"
            exit 1
          fi
```

### Submission Process

The workflow performs the following steps:

1. Makes a POST request to the sitemap submission API endpoint
2. Checks the response status code and content
3. Reports success or failure based on the response

## Required Secrets

### Deployment Secrets

The deployment workflow requires the following secrets to be set in the GitHub repository settings:

- `LINODE_SSH_KEY`: The private SSH key for connecting to the Linode server
- `LINODE_HOST`: The IP address or hostname of the Linode server
- `LINODE_USER`: The username for SSH access to the Linode server (usually "root")

### Sitemap Submission Secrets

The sitemap submission workflow requires the following secret:

- `X_CRON_API_KEY`: The API key for authenticating with cron job endpoints including sitemap submission

## Manual Triggering

### Manual Deployment

While the deployment workflow runs automatically when changes are pushed to the main branch, you can also manually deploy the application:

1. SSH into the Linode server
2. Navigate to the project directory
3. Run the deployment script:

```bash
cd ~/onlyc2c-candidate-frontend
./scripts/deploy.sh
```

### Manual Sitemap Submission

To manually trigger the sitemap submission workflow:

1. Go to the "Actions" tab in your GitHub repository
2. Select the "Sitemap Submission" workflow
3. Click "Run workflow"
4. Select the branch (usually "main")
5. Click "Run workflow"

## Troubleshooting

### Deployment Issues

If the deployment workflow fails:

1. Check the GitHub Actions logs for error messages
2. Verify that all required secrets are correctly set
3. Test SSH connection manually:

    ```bash
    ssh -i ~/.ssh/id_ed25519 root@your_linode_ip
    ```

4. Check if the repository is accessible from the Linode server:

    ```bash
    ssh root@your_linode_ip "cd ~/onlyc2c-candidate-frontend && git fetch"
    ```

5. Check for disk space issues on the Linode server:

    ```bash
    ssh root@your_linode_ip "df -h"
    ```

### Sitemap Submission Issues

If the sitemap submission workflow fails:

1. Check the GitHub Actions logs for error messages
2. Verify that the `X_CRON_API_KEY` secret is correctly set
3. Test the API endpoint manually:

    ```bash
    curl -X POST https://onlyc2c.com/api/cron/submit-sitemap \
      -H "x-c2c-api-key: your_api_key_here"
    ```

4. Check the server logs for API errors:

    ```bash
    ssh root@your_linode_ip "pm2 logs onlyc2c-candidate"
    ```

## Related Documentation

- [Linode Server Setup](./linode-setup.md)
- [Sitemap Generation and Management](../features/seo/sitemaps.md)
- [API Key Management](../security/api-keys.md)
