# Monitoring and Maintenance

> _Last updated: May 2025_

This document provides guidance on monitoring and maintaining the OnlyC2C application in production.

## Table of Contents

1. [System Monitoring](#system-monitoring)
2. [Application Monitoring](#application-monitoring)
3. [Log Management](#log-management)
4. [Backup Strategy](#backup-strategy)
5. [Regular Maintenance](#regular-maintenance)
6. [Incident Response](#incident-response)

## System Monitoring

### Server Resources

Monitor server resources to ensure optimal performance:

```bash
# Check system load and CPU usage
htop

# Check disk usage
df -h

# Check memory usage
free -m

# Check running processes
ps aux | grep node
```

### Setting Up Basic Monitoring

Install monitoring tools:

```bash
# Install htop for interactive process monitoring
apt-get install -y htop

# Install sysstat for system statistics
apt-get install -y sysstat
```

### Automated Monitoring

Consider setting up automated monitoring with tools like:

- **Uptime Robot**: For basic uptime monitoring
- **New Relic**: For more comprehensive application monitoring
- **Datadog**: For infrastructure and application monitoring

## Application Monitoring

### PM2 Process Management

PM2 provides basic monitoring for the Node.js application:

```bash
# Check status of all processes
pm2 list

# Monitor logs and metrics in real-time
pm2 monit

# Check specific application logs
pm2 logs onlyc2c-candidate

# Get detailed information about a process
pm2 show onlyc2c-candidate
```

### Health Checks

Implement and monitor application health endpoints:

- `/api/health`: Basic health check endpoint
- `/api/health/detailed`: Detailed health check with database connectivity

Example cURL command for health check:

```bash
curl -v https://onlyc2c.com/api/health
```

### Performance Monitoring

Monitor key performance metrics:

- Response times
- Error rates
- Database query performance
- Memory usage

## Log Management

### Application Logs

Access application logs through PM2:

```bash
# View all logs
pm2 logs

# View specific application logs
pm2 logs onlyc2c-candidate

# View logs with timestamp
pm2 logs --timestamp

# View a specific number of lines
pm2 logs --lines 200
```

### Nginx Logs

Access Nginx logs for HTTP requests:

```bash
# Access logs (successful requests)
tail -f /var/log/nginx/access.log

# Error logs (failed requests)
tail -f /var/log/nginx/error.log
```

### Log Rotation

Configure log rotation to prevent logs from filling up disk space:

```bash
# PM2 log rotation
nano /etc/logrotate.d/pm2
```

Add the following configuration:

```config
/root/.pm2/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0640 root root
}
```

## Backup Strategy

### Database Backups

Set up regular MongoDB backups:

```bash
# For MongoDB Atlas
# Automated backups are included in the service

# For self-hosted MongoDB
mongodump --uri="********************************:port/database" --out=/path/to/backup/directory
```

### Application Backups

Back up the application code and configuration:

```bash
# Create a backup of the application directory
tar -czf /root/backups/onlyc2c-backup-$(date +\%Y\%m\%d).tar.gz /root/onlyc2c-candidate-frontend
```

### Automated Backup Script

Create a backup script at `/root/scripts/backup.sh`:

```bash
#!/bin/bash

# Create backup directory if it doesn't exist
mkdir -p /root/backups

# Backup application code
tar -czf /root/backups/onlyc2c-app-$(date +\%Y\%m\%d).tar.gz /root/onlyc2c-candidate-frontend

# Cleanup old backups (keep last 7 days)
find /root/backups -name "onlyc2c-app-*.tar.gz" -type f -mtime +7 -delete

echo "Backup completed: $(date)"
```

Make it executable and add to crontab:

```bash
chmod +x /root/scripts/backup.sh

# Add to crontab to run daily at 2 AM
crontab -e
```

Add the following line:

```cron
0 2 * * * /root/scripts/backup.sh >> /root/backups/backup.log 2>&1
```

## Regular Maintenance

### System Updates

Keep the server updated:

```bash
# Update package lists
apt update

# Install security updates
apt upgrade -y

# Reboot if necessary (schedule during low-traffic periods)
reboot
```

### Node.js Updates

Update Node.js when new LTS versions are released:

```bash
# Install new Node.js version
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt-get install -y nodejs

# Verify version
node --version
```

### SSL Certificate Renewal

Let's Encrypt certificates auto-renew, but verify the renewal process:

```bash
# Test certificate renewal
certbot renew --dry-run

# Check certificate expiration
certbot certificates
```

## Incident Response

### Common Issues and Solutions

#### Application Crashes

If the application crashes:

```bash
# Check logs for errors
pm2 logs onlyc2c-candidate

# Restart the application
pm2 restart onlyc2c-candidate

# If needed, rebuild and restart
cd /root/onlyc2c-candidate-frontend
git pull
npm ci
npm run build
pm2 restart onlyc2c-candidate
```

#### High CPU/Memory Usage

If the server shows high resource usage:

```bash
# Identify resource-intensive processes
htop

# Check application memory usage
pm2 monit

# Restart if necessary
pm2 restart onlyc2c-candidate
```

#### Database Connection Issues

If the application can't connect to the database:

```bash
# Check MongoDB connection
mongo ********************************:port/database --eval "db.stats()"

# Verify environment variables
grep MONGO_URI /root/onlyc2c-candidate-frontend/.env.production
```

### Emergency Contacts

Maintain a list of emergency contacts:

- **Primary Developer**: [Name] - [Email] - [Phone]
- **Database Administrator**: [Name] - [Email] - [Phone]
- **Server Administrator**: [Name] - [Email] - [Phone]

### Incident Documentation

Document all incidents with:

1. Date and time
2. Description of the issue
3. Steps taken to resolve
4. Root cause analysis
5. Preventive measures for the future
