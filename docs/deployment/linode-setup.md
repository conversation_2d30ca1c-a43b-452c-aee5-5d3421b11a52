# Linode Server Setup Guide

> _Last updated: May 2025_

This document provides comprehensive instructions for setting up a Linode server to host the OnlyC2C Next.js application.

## Table of Contents

1. [Initial Server Setup](#initial-server-setup)
2. [SSH Key Configuration](#ssh-key-configuration)
3. [Software Installation](#software-installation)
4. [Application Setup](#application-setup)
5. [Nginx Configuration](#nginx-configuration)
6. [Environment Configuration](#environment-configuration)
7. [Troubleshooting](#troubleshooting)

## Initial Server Setup

### Creating a Linode Instance

1. Log in to your Linode account
2. Click "Create Linode"
3. Choose a distribution (Ubuntu 22.04 LTS recommended)
4. Select a region close to your target audience
5. Choose a plan (at least 4GB RAM recommended for Next.js builds)
6. Set a strong root password
7. Click "Create"

### Initial Security Setup

Once your Linode is running, SSH into it as root:

```bash
ssh root@your_linode_ip
```

Update the system:

```bash
apt update && apt upgrade -y
```

## SSH Key Configuration

### Generate SSH Key Pair on the Linode Server

Create an SSH key pair on your Linode server that will be used for both GitHub authentication and SSH connections:

```bash
ssh-keygen -t ed25519 -C "<EMAIL>"
```

This creates:

- Private key: `~/.ssh/id_ed25519`
- Public key: `~/.ssh/id_ed25519.pub`

### Set Up Authorized Keys on Linode

Add your public key to the authorized_keys file to enable passwordless SSH login:

```bash
cat ~/.ssh/id_ed25519.pub >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

### Configure SSH for GitHub Authentication

1. Create an SSH config file for GitHub:

    ```bash
    nano ~/.ssh/config
    ```

2. Add the following configuration:

    ```bash
    Host github.com
    Hostname github.com
    PreferredAuthentications publickey
    IdentityFile ~/.ssh/id_ed25519
    ```

3. Add your public key to your GitHub account:
   - Copy the content of your public key:

     ```bash
     cat ~/.ssh/id_ed25519.pub
     ```

   - Go to GitHub → Settings → SSH and GPG keys → New SSH key
   - Paste your public key and save

4. Test your GitHub connection:

    ```bash
    ssh -T **************
    ```

## Software Installation

### Install Node.js and npm

```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -

# Install Node.js and npm
apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### Install PM2 Process Manager

```bash
# Install PM2 globally
npm install -g pm2

# Set up PM2 to start on boot
pm2 startup
```

### Install Git

```bash
apt-get install -y git

# Configure Git
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### Install Nginx

```bash
# Install Nginx
apt-get install -y nginx

# Start Nginx and enable it to start on boot
systemctl start nginx
systemctl enable nginx

# Check status
systemctl status nginx
```

## Application Setup

### Clone the Repository

```bash
# Navigate to home directory
cd ~

# Clone the repository
<NAME_EMAIL>:Judyjose008/onlyc2c-candidate-frontend.git

# Navigate to project directory
cd onlyc2c-candidate-frontend
```

### Install Dependencies and Build

```bash
# Install dependencies
npm ci

# Build the application
NODE_OPTIONS='--max_old_space_size=4096' npm run build
```

### Configure PM2

Start the application with PM2:

```bash
# Start the application
pm2 start 'npx next start' --name "onlyc2c-candidate"

# Save the PM2 configuration
pm2 save
```

## Nginx Configuration

### Create Nginx Configuration

Create a new Nginx site configuration:

```bash
nano /etc/nginx/sites-available/onlyc2c
```

Add the following configuration:

```nginx
server {
    server_name onlyc2c.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/onlyc2c.com/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/onlyc2c.com/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = onlyc2c.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name onlyc2c.com;
    return 404; # managed by Certbot
}
```

Enable the site:

```bash
# Create symbolic link to enable the Nginx site configuration
ln -s /etc/nginx/sites-available/onlyc2c /etc/nginx/sites-enabled/

# Test Nginx configuration for syntax errors before applying changes
nginx -t

# Restart Nginx server to apply the new configuration
systemctl restart nginx
```

### Set Up SSL with Let's Encrypt

Install Certbot:

```bash
apt-get install -y certbot python3-certbot-nginx
```

Obtain and configure SSL certificate:

```bash
certbot --nginx -d onlyc2c.com -d www.onlyc2c.com
```

Follow the prompts to complete the SSL setup.

## Environment Configuration

Create a production environment file:

```bash
nano .env.production
```

Add the necessary environment variables:

```env
ENV=PRODUCTION

# Database
MONGO_URI=your_mongodb_connection_string

# Authentication
NEXTAUTH_URL=https://onlyc2c.com
AUTH_TRUST_HOST=true
NEXTAUTH_SECRET=your_nextauth_secret

# JWT Keys
PRIVATE_JWK=your_private_jwk
PUBLIC_JWK=your_public_jwk

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=OnlyC2C

# Email Queue Configuration
EMAIL_QUEUE_CONCURRENCY=5
EMAIL_QUEUE_INTERVAL=1000
EMAIL_QUEUE_RETRY_COUNT=3

# API Keys
X_CRON_API_KEY=your_cron_api_key

# Base URL
NEXT_PUBLIC_ONLYC2C_BASE_URL=https://onlyc2c.com
```

## Troubleshooting

### Common Issues and Solutions

#### PM2 Issues

If PM2 is not starting the application correctly:

```bash
# Check PM2 logs
pm2 logs onlyc2c-candidate

# Restart the application
pm2 restart onlyc2c-candidate

# Delete and recreate the PM2 process
pm2 delete onlyc2c-candidate
pm2 start 'npx next start' --name "onlyc2c-candidate"
```

#### Nginx Issues

If Nginx is not serving the application:

```bash
# Check Nginx error logs
tail -f /var/log/nginx/error.log

# Check Nginx configuration
nginx -t

# Restart Nginx
systemctl restart nginx
```

#### Deployment Issues

If GitHub Actions deployment fails:

1. Check the GitHub Actions logs for error messages
2. Verify that all required secrets are correctly set
3. Test SSH connection manually:

```bash
ssh -i ~/.ssh/id_ed25519 root@your_linode_ip
```

#### Memory Issues During Build

If the build process runs out of memory:

```bash
# Increase Node.js memory limit
NODE_OPTIONS='--max_old_space_size=4096' npm run build

# Consider upgrading your Linode plan if memory issues persist
```

### Monitoring

Set up basic monitoring:

```bash
# Install monitoring tools
apt-get install -y htop

# Monitor system resources
htop

# Monitor disk usage
df -h

# Monitor memory usage
free -m

# Monitor running processes
top
```

### Log Rotation

Configure log rotation to prevent logs from filling up your disk:

```bash
nano /etc/logrotate.d/pm2
```

Add:

```bash
/root/.pm2/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0640 root root
}
```

## Related Documentation

- [GitHub Actions Deployment](./github-actions.md)
- [Monitoring and Maintenance](./monitoring.md)
- [Environment Setup](../getting-started/environment-setup.md)
