# Environment Setup

> _Last updated: May 2025_

This document provides a comprehensive guide to setting up the environment variables required for the OnlyC2C application.

## Table of Contents

1. [Overview](#overview)
2. [Environment Files](#environment-files)
3. [Required Environment Variables](#required-environment-variables)
4. [Optional Environment Variables](#optional-environment-variables)
5. [Environment-Specific Configuration](#environment-specific-configuration)
6. [Sensitive Information Handling](#sensitive-information-handling)

## Overview

The OnlyC2C application uses environment variables for configuration. These variables are loaded from `.env` files and are used to configure various aspects of the application, including:

- Database connection
- Authentication
- Email service
- API keys
- External service integrations

## Environment Files

The application uses different environment files for different environments:

- `.env.development`: Used for local development
- `.env.production`: Used for production deployment
- `.env.test`: Used for testing (if applicable)

These files should be created in the root directory of the project.

## Required Environment Variables

### Core Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `ENV` | Current environment | `DEVELOPMENT`, `PRODUCTION`, `STAGING` |
| `NEXT_PUBLIC_ONLYC2C_BASE_URL` | Base URL of the application | `http://localhost:3000` (dev), `https://onlyc2c.com` (prod) |

### Database Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `MONGO_URI` | MongoDB connection string | `mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority` |

### Authentication

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXTAUTH_URL` | URL for NextAuth.js | `http://localhost:3000` (dev), `https://staging.onlyc2c.com` (stage), `https://onlyc2c.com` (prod) |
| `NEXTAUTH_SECRET` | Secret for NextAuth.js | Random string (e.g., `QJPZgGIEbGpa7kJKG84iXsoNq7ojxOCluoPCS1wZ7SI=`) |
| `AUTH_TRUST_HOST` | Trust host header (for production) | `true` |
| `PRIVATE_JWK` | Private JWK for JWT signing | JSON string containing RSA private key |
| `PUBLIC_JWK` | Public JWK for JWT verification | JSON string containing RSA public key |

### Email Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `EMAIL_HOST` | SMTP host | `smtp.gmail.com` |
| `EMAIL_PORT` | SMTP port | `465` |
| `EMAIL_SECURE` | Use secure connection | `true` |
| `EMAIL_USER` | SMTP username | `<EMAIL>` |
| `EMAIL_PASSWORD` | SMTP password or app password | `your_app_password` |
| `EMAIL_FROM` | From email address | `<EMAIL>` |
| `EMAIL_FROM_NAME` | From name | `OnlyC2C` |

### OAuth Providers

| Variable | Description | Example |
|----------|-------------|---------|
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | `your_google_client_id.apps.googleusercontent.com` |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | `your_google_client_secret` |
| `LINKEDIN_CLIENT_ID` | LinkedIn OAuth client ID | `your_linkedin_client_id` |
| `LINKEDIN_CLIENT_SECRET` | LinkedIn OAuth client secret | `your_linkedin_client_secret` |

### API Keys

| Variable | Description | Example |
|----------|-------------|---------|
| `X_CRON_API_KEY` | API key for cron job endpoints (including sitemap submission) | Generated using `npm run generate-api-key` |

## Optional Environment Variables

### Email Queue Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `EMAIL_QUEUE_CONCURRENCY` | Number of concurrent email sends | `5` | `10` |
| `EMAIL_QUEUE_INTERVAL` | Interval between email sends (ms) | `1000` | `2000` |
| `EMAIL_QUEUE_RETRY_COUNT` | Number of retries for failed emails | `3` | `5` |

### Google Cloud Services

| Variable | Description | Example |
|----------|-------------|---------|
| `GOOGLE_SERVICE_ACCOUNT_KEY` | Path to Google service account key file | `./.creds/onlyc2c-gcs-creds.json` |
| `GOOGLE_CLOUD_PROJECT` | Google Cloud project ID | `your_project_id` |
| `GCS_BUCKET_NAME` | Google Cloud Storage bucket name | `onlyc2c_prod` |
| `GOOGLE_GENAI_API_KEY` | Google Generative AI API key | `your_genai_api_key` |

## Environment-Specific Configuration

### Development Environment

For local development, create a `.env.development` file with:

```env
ENV=DEVELOPMENT

# Database
MONGO_URI=your_development_mongodb_uri

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Base URL
NEXT_PUBLIC_ONLYC2C_BASE_URL=http://localhost:3000

# Other required variables...
```

### Production Environment

For production deployment, create a `.env.production` file with:

```env
ENV=PRODUCTION

# Database
MONGO_URI=your_production_mongodb_uri

# Authentication
NEXTAUTH_URL=https://onlyc2c.com
AUTH_TRUST_HOST=true
NEXTAUTH_SECRET=your_nextauth_secret

# Base URL
NEXT_PUBLIC_ONLYC2C_BASE_URL=https://onlyc2c.com

# Other required variables...
```

### Staging Environment

> For staging deployment, the environment variables are set on the Vercel dashboard.

## Sensitive Information Handling

### Local Development

- Never commit `.env` files to version control
- Use `.env.example` to document required variables without values
- Keep sensitive credentials secure

### Production Deployment

- Set environment variables securely on the server
- Use secrets management for GitHub Actions workflows
- Regularly rotate sensitive credentials

### JWT Keys Generation

To generate JWT keys for authentication:

1. Use a tool like [JWK Generator](https://mkjwk.org/) to generate RSA key pairs
2. Set the private key in `PRIVATE_JWK` and public key in `PUBLIC_JWK`
3. Ensure the keys are properly JSON-encoded strings

### API Key Generation

To generate API keys for protected endpoints:

```bash
npm run generate-api-key
```

This will output a secure random API key that you can use for the `X_CRON_API_KEY` environment variable.
