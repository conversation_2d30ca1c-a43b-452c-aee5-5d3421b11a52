# Installation Guide

> _Last updated: May 2025_

This document provides step-by-step instructions for installing and setting up the OnlyC2C application for local development.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Clone the Repository](#clone-the-repository)
3. [Install Dependencies](#install-dependencies)
4. [Environment Setup](#environment-setup)
5. [Database Setup](#database-setup)
6. [Running the Application](#running-the-application)
7. [Verification](#verification)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version 18.x or higher
  - Download from [nodejs.org](https://nodejs.org/)
  - Verify with `node --version`

- **npm**: Version 9.x or higher (comes with Node.js)
  - Verify with `npm --version`

- **Git**: Latest version
  - Download from [git-scm.com](https://git-scm.com/)
  - Verify with `git --version`

- **MongoDB**: Access to a MongoDB database
  - You can use [MongoDB Atlas](https://www.mongodb.com/cloud/atlas) for a free cloud-hosted database
  - Or install MongoDB locally from [mongodb.com](https://www.mongodb.com/try/download/community)

## Clone the Repository

1. Open a terminal or command prompt
2. Clone the repository:

    ```bash
    git clone https://github.com/Judyjose008/onlyc2c-candidate-frontend.git
    ```

3. Navigate to the project directory:

    ```bash
    cd onlyc2c-candidate-frontend
    ```

## Install Dependencies

Install the project dependencies using npm:

```bash
npm ci
```

> **Note**: We use `npm ci` instead of `npm install` to ensure exact versions from package-lock.json are installed.

## Environment Setup

1. Create a `.env.development` file in the root directory:

    ```bash
    cp .env.example .env.development
    ```

2. Open `.env.development` in a text editor and fill in the required environment variables.

For a complete list of environment variables and their descriptions, see the [Environment Setup](./environment-setup.md) documentation. At minimum, you'll need to configure:

- Database connection (`MONGO_URI`)
- Authentication settings (`NEXTAUTH_URL`, `NEXTAUTH_SECRET`)
- OAuth provider credentials (if using social login)
- Email service settings (if testing email functionality)

## Database Setup

1. Ensure you have access to a MongoDB database
2. Update the `MONGO_URI` in your `.env.development` file with your MongoDB connection string
3. The application will automatically create the necessary collections on first run

## Running the Application

Start the development server:

```bash
npm run dev
```

This will start the Next.js development server on port 3000. You can access the application at [http://localhost:3000](http://localhost:3000).

## Verification

To verify that the installation was successful:

1. Open your browser and navigate to [http://localhost:3000](http://localhost:3000)
2. You should see the OnlyC2C homepage
3. Test the authentication by clicking on the "Sign In" button
4. Verify that you can browse job listings

## Troubleshooting

### Common Issues

#### "Module not found" errors

If you encounter module not found errors, try:

```bash
npm ci
```

#### MongoDB connection issues

If you have trouble connecting to MongoDB:

1. Verify that your MongoDB connection string is correct
2. Ensure that your IP address is whitelisted in MongoDB Atlas (if using Atlas)
3. Check that the database user has the correct permissions

#### NextAuth.js configuration issues

If you encounter authentication issues:

1. Verify that your OAuth provider credentials are correct
2. Ensure that the redirect URIs are properly configured in your OAuth provider settings
3. Check that `NEXTAUTH_URL` is set correctly

#### Port already in use

If port 3000 is already in use:

```bash
# Kill the process using port 3000
npx kill-port 3000

# Or start the application on a different port
PORT=3001 npm run dev
```

### Getting Help

If you encounter issues not covered here:

1. Check the error logs in the console
2. Search for similar issues in the project repository
3. Consult the [Troubleshooting Guide](../troubleshooting/common-issues.md) for more detailed solutions
