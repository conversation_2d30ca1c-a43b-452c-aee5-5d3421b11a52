# NPM Scripts Documentation

> _Last updated: May 2025_

This document provides a comprehensive guide to all npm scripts available in the OnlyC2C project.

## Table of Contents

- [Core Development Scripts](#core-development-scripts)
- [Sitemap Management Scripts](#sitemap-management-scripts)
- [Utility Scripts](#utility-scripts)
- [Troubleshooting](#troubleshooting)

## Core Development Scripts

These scripts are used for day-to-day development and deployment tasks.

### `npm run dev`

Starts the Next.js development server with hot reloading.

```bash
npm run dev
```

This will start the development server on port 3000 (by default). You can access the application at `http://localhost:3000`.

### `npm run build`

Builds the application for production deployment.

```bash
npm run build
```

This creates an optimized production build in the `.next` directory. For servers with limited memory, you may need to increase the Node.js memory limit:

```bash
NODE_OPTIONS='--max_old_space_size=4096' npm run build
```

### `npm run start`

Starts the Next.js production server using the built application.

```bash
npm run start
```

This command should only be used after running `npm run build`. In production environments, it's recommended to use PM2 to manage the process:

```bash
pm2 start 'npx next start' --name "onlyc2c-candidate"
```

### `npm run lint`

Runs ESLint to check for code quality issues.

```bash
npm run lint
```

## Sitemap Management Scripts

These scripts are used to generate and submit sitemaps for search engine optimization.

### `npm run generate-sitemaps`

Generates all sitemaps and saves them to the `public/sitemaps` directory.

```bash
npm run generate-sitemaps
```

This script:

1. Generates separate sitemaps for static pages, jobs, locations, skills, and job types
2. Creates a sitemap index file that references all individual sitemaps
3. Saves all files to the `public/sitemaps` directory
4. Prints URLs for verification

Implementation details can be found in `src/scripts/generate-sitemaps.ts` and `src/utils/server/sitemapUtils.ts`.

### `npm run generate-sitemaps -- --submit`

Generates all sitemaps and submits them to Google Search Console.

```bash
npm run generate-sitemaps -- --submit
```

This requires proper setup of the Google Search Console API credentials.

### `npm run submit-sitemaps`

Submits existing sitemaps to Google Search Console without regenerating them.

```bash
npm run submit-sitemaps
```

This script is used by the daily GitHub Actions workflow to automatically submit sitemaps to search engines. The workflow runs at 2:00 AM CST (8:00 AM UTC) every day.

Implementation details can be found in `src/scripts/submit-sitemaps.ts`.

## Utility Scripts

These scripts provide utility functions for various tasks.

### `npm run generate-api-key`

Generates a secure API key for use with protected endpoints.

```bash
npm run generate-api-key
```

This script generates a random 32-byte key using the `crypto` module and outputs it to the console. The generated key can be used for API authentication, such as the sitemap submission endpoint.

Implementation details can be found in `src/scripts/generate-api-key.ts` and `src/utils/server/apiKeyUtils.ts`.

### `npm run fix-seo-link`

Updates SEO-friendly links for all job listings in the database.

```bash
npm run fix-seo-link
```

This script:

1. Connects to the MongoDB database
2. Retrieves all job listings
3. Generates SEO-friendly URLs based on job details (role, organization, location)
4. Updates the `seo_link` field in the database

Implementation details can be found in `src/scripts/fix-seo-links.ts` and `src/utils/server/generateJobMetadata.ts`.

## Troubleshooting

### Common Issues

#### "Error: Cannot find module 'ts-node'"

If you encounter this error, you need to install the required dependencies:

```bash
npm install ts-node tsconfig-paths --save-dev
```

#### "Error: ENOENT: no such file or directory, open 'public/sitemaps/...'"

The `public/sitemaps` directory doesn't exist. The script should create it automatically, but if it fails, you can create it manually:

```bash
mkdir -p public/sitemaps
```

#### "Error submitting sitemap to Google: Google API key file not found"

For sitemap submission to work, you need to:

1. Create a service account in Google Cloud Console
2. Grant the service account access to your Search Console property
3. Download the service account key file
4. Save the key file as specified in your environment configuration

#### Memory Issues During Build

If you encounter memory issues during build, increase the Node.js memory limit:

```bash
NODE_OPTIONS='--max_old_space_size=4096' npm run build
```

### Getting Help

If you encounter issues not covered here, check:

1. The error logs for specific error messages
2. The implementation files for each script
3. The related documentation in the `docs` directory
