# Development Workflow

> _Last updated: May 2025_

This document outlines the development workflow for the OnlyC2C application.

## Table of Contents

1. [Development Environment](#development-environment)
2. [Git Workflow](#git-workflow)
3. [Code Standards](#code-standards)
4. [Testing](#testing)
5. [Pull Requests](#pull-requests)
6. [Deployment](#deployment)

## Development Environment

### Prerequisites

Before starting development, ensure you have:

- Node.js (v18.x or higher)
- npm (v9.x or higher)
- Git
- A code editor (VS Code recommended)
- MongoDB access (local or Atlas)

### Setup

1. Clone the repository:

   ```bash
   git clone https://github.com/Judyjose008/onlyc2c-candidate-frontend.git
   cd onlyc2c-candidate-frontend
   ```

2. Install dependencies:

   ```bash
   npm ci
   ```

3. Set up environment variables:

   ```bash
   cp .env.example .env.development
   ```

   Edit `.env.development` with your local configuration.

4. Start the development server:

   ```bash
   npm run dev
   ```

5. Access the application at [http://localhost:3000](http://localhost:3000)

## Git Workflow

We follow a feature branch workflow:

1. **Main Branch**: The `main` branch contains production-ready code
2. **Stage Branch**: The `stage` branch contains features staged for release
3. **Feature Branches**: Create branches for new features or bug fixes
4. **Pull Requests**: Submit PRs to merge changes into `stage`

### Branch Naming Convention

Use descriptive branch names with prefixes:

- `feature/`: For new features
- `bugfix/`: For bug fixes
- `hotfix/`: For urgent production fixes
- `docs/`: For documentation updates
- `refactor/`: For code refactoring

Example: `feature/email-templates` or `bugfix/login-error`

### Commit Messages

Write clear, descriptive commit messages:

- Use the imperative mood ("Add feature" not "Added feature")
- Keep the first line under 50 characters
- Provide more details in the body if necessary
- Reference issue numbers when applicable

Example:

```commit-message
Add job application email notifications

- Implement email templates for application status changes
- Add email queue for reliable delivery
- Update JobApplicationService to send emails on status change

Fixes #123
```

## Code Standards

### TypeScript

- Use TypeScript for all new code
- Define proper interfaces for data structures
- Use type annotations for function parameters and return types
- Avoid using `any` type when possible

### React

- Use functional components with hooks
- Keep components small and focused
- Use proper prop types
- Follow the container/presentational pattern when appropriate

### Styling

- Use Tailwind CSS for styling
- Follow the existing design system
- Ensure responsive design for all screen sizes

### File Structure

- Group related files by feature
- Use consistent naming conventions
- Keep files focused on a single responsibility

## Testing

### Running Tests

Run tests with:

```bash
npm run test
```

### Writing Tests

- Write tests for new features and bug fixes
- Focus on testing behavior, not implementation details
- Use descriptive test names that explain the expected behavior

## Pull Requests

### Creating a PR

1. Push your branch to GitHub:

   ```bash
   git push origin feature/your-feature
   ```

2. Create a PR on GitHub with:
   - A clear title
   - A description of the changes
   - Any related issues
   - Screenshots if applicable

### PR Review Process

1. At least one approval is required before merging
2. Address all review comments
3. Ensure all checks pass (linting, tests, etc.)
4. Use squash and merge to keep the history clean

## Deployment

### Environments

- **Development**: Local development environment
- **Staging**: Staged features environment [staging.onlyc2c.com](https://staging.onlyc2c.com)
- **Production**: Live environment at [onlyc2c.com](https://onlyc2c.com)

### Deployment Process

1. Changes merged to `main` are automatically deployed to production via GitHub Actions
2. Changes merged to `stage` are automatically deployed to staging via Vercel
3. The deployment workflow:
   - Pulls the latest code
   - Installs dependencies
   - Builds the application
   - Restarts the service

### Monitoring Deployments

Monitor deployments in:

- GitHub Actions tab in the repository
- Vercel dashboard for staging environment
- Server logs on the Linode server

### Rollback Process

If a deployment causes issues:

1. Identify the last stable commit
2. Manually trigger a deployment of that commit:

   ```bash
   git checkout <commit-hash>
   ./scripts/deploy.sh
   ```

## Additional Resources

- [Installation Guide](./installation.md)
- [Environment Setup](./environment-setup.md)
- [NPM Scripts](./npm-scripts.md)
- [Deployment Guide](../deployment/linode-setup.md)
