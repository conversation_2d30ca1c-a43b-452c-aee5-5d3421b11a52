# Sitemap Generation and Management

> _Last updated: May 2025_

This document explains how to generate and manage sitemaps for the OnlyC2C website.

## Overview

The sitemap system consists of:

1. **Separate sitemap files** for different content types:
   - Static pages (`static.xml`)
   - Job listings (`jobs.xml`)
   - Location-based pages (`locations.xml`)
   - Skill-based pages (`skills.xml`)
   - Job type pages (`job-types.xml`)

2. **A sitemap index file** (`sitemap.xml`) that references all individual sitemaps

3. **API routes** to serve the sitemaps dynamically

4. **Scripts** to generate and submit sitemaps

## Sitemap URLs

The following sitemap URLs are available in production:

- Main sitemap index: `/sitemap.xml`
- Static pages sitemap: `/api/sitemap/static.xml`
- Job listings sitemap: `/api/sitemap/jobs.xml`
- Location-based pages sitemap: `/api/sitemap/locations.xml`
- Skill-based pages sitemap: `/api/sitemap/skills.xml`
- Job type pages sitemap: `/api/sitemap/job-types.xml`

## Implementation Details

### Core Components

The sitemap system is implemented in:

- `src/utils/server/sitemapUtils.ts`: Core sitemap generation utilities
- `src/app/api/sitemap/`: API routes for serving sitemaps
- `src/app/sitemap.xml/route.ts`: Root sitemap.xml route
- `src/scripts/generate-sitemaps.ts`: Script for generating sitemaps
- `src/scripts/submit-sitemaps.ts`: Script for submitting sitemaps

### URL Structure

Each sitemap contains URLs with the following structure:

1. **Static Pages**:
   - Homepage: `https://onlyc2c.com/`
   - About page: `https://onlyc2c.com/about`
   - Contact page: `https://onlyc2c.com/contact`
   - And other static pages

2. **Jobs**:
   - SEO-friendly URLs: `https://onlyc2c.com/c2c-{job-type}-{role}-in-{city}-{state}-for-{organization}-jobid-{id}`
   - Example: `https://onlyc2c.com/c2c-fulltime-software-engineer-in-new-york-ny-for-acme-corp-jobid-12345`

3. **Locations**:
   - Format: `https://onlyc2c.com/c2c-job-in-{location}`
   - Example: `https://onlyc2c.com/c2c-job-in-new-york`

4. **Skills**:
   - Format: `https://onlyc2c.com/{skill}-c2c-jobs`
   - Example: `https://onlyc2c.com/react-c2c-jobs`

5. **Job Types**:
   - Format: `https://onlyc2c.com/{job-type}-c2c-jobs`
   - Example: `https://onlyc2c.com/fulltime-c2c-jobs`

## Generating Sitemaps Locally

To generate sitemaps locally for testing and verification:

```bash
# Install required dependencies
npm install

# Generate sitemaps
npm run generate-sitemaps
```

This will:

1. Generate all sitemap files
2. Save them to the `public/sitemaps` directory
3. Print the URLs for verification

You can then access the generated sitemaps at:

- `/sitemaps/sitemap.xml`
- `/sitemaps/static-sitemap.xml`
- `/sitemaps/jobs-sitemap.xml`
- `/sitemaps/locations-sitemap.xml`
- `/sitemaps/skills-sitemap.xml`
- `/sitemaps/job-types-sitemap.xml`

## Submitting Sitemaps to Google

### Manual Submission

To manually submit sitemaps to Google:

```bash
# Generate and submit sitemaps
npm run generate-sitemaps -- --submit

# Or just submit existing sitemaps
npm run submit-sitemaps
```

### API Submission

You can also submit sitemaps via the API:

```bash
curl -X POST https://onlyc2c.com/api/cron/submit-sitemap \
  -H "x-c2c-api-key: your_api_key_here"
```

This endpoint is protected by an API key that must be provided in the `x-c2c-api-key` header.

### Automatic Submission

The project includes a GitHub Actions workflow that automatically submits sitemaps to Google Search Console daily at 2:00 AM CST (8:00 AM UTC).

The workflow configuration is located at `.github/workflows/sitemap.yml`.

## Google Search Console Setup

To enable automatic submission to Google Search Console:

1. Create a service account in Google Cloud Console
2. Grant the service account access to your Search Console property
3. Download the service account key file
4. Set the path to the key file in the environment variable `GOOGLE_SERVICE_ACCOUNT_KEY`

## Troubleshooting

If you encounter issues with sitemap generation or submission:

1. Check that the required services are accessible:
   - MongoDB connection for fetching jobs, skills, and localities
   - Google Search Console API for submission

2. Verify that the Google Search Console API key is correctly set up:
   - The key file exists at the specified path
   - The service account has proper permissions

3. Check the server logs for error messages:
   - Look for database connection issues
   - Check for API authentication errors

4. Try generating sitemaps locally to verify the content:
   - Run `npm run generate-sitemaps`
   - Inspect the generated files in `public/sitemaps/`

5. Ensure the environment variables are correctly set:
   - `NEXT_PUBLIC_ONLYC2C_BASE_URL`: The base URL of the website
   - `GOOGLE_SERVICE_ACCOUNT_KEY`: Path to the Google service account key file
   - `X_CRON_API_KEY`: API key for cron job endpoints including sitemap submission
