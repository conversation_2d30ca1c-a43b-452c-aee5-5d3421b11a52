# Premium Subscription System

This document outlines the implementation of the premium subscription system for OnlyC2C candidates.

## Overview

The subscription system allows candidates to subscribe to premium features for $1.99/month using Stripe for payment processing. The system is designed to be scalable for future recruiter subscriptions.

## Architecture

### Database Models

#### PremiumSubscription Model
- `userId`: Reference to User
- `userType`: CANDIDATE or RECRUITER (for future use)
- `stripeCustomerId`: Stripe customer ID
- `stripeSubscriptionId`: Stripe subscription ID
- `status`: Subscription status (active, canceled, etc.)
- `currentPeriodStart/End`: Billing period dates
- `cancelAtPeriodEnd`: Whether subscription cancels at period end
- `planType`: Type of plan (candidate_basic, etc.)
- `priceId`: Stripe price ID

#### Updated Candidate Model
- `isPremium`: Boolean flag for premium status
- `subscriptionId`: Reference to PremiumSubscription

### API Endpoints

#### Candidate Subscription Management
- `POST /api/v1/subscriptions/premium/candidates/create-checkout-session`
- `POST /api/v1/subscriptions/premium/candidates/create-portal-session`
- `GET /api/v1/subscriptions/premium/candidates/status`
- `POST /api/v1/subscriptions/premium/candidates/cancel`

#### Webhook
- `POST /api/v1/subscriptions/premium/webhook` (Stripe webhooks)

#### Marketing Subscriptions (Existing)
- `POST /api/v1/subscriptions/marketing`
- `PATCH /api/v1/subscriptions/marketing/[id]`

### Middleware Protection

The system includes premium route protection in `src/middleguard/backend.ts`:

```typescript
export const premiumCandidateApiRoutes = [
    { path: /^\/api\/v1\/subscriptions\/premium\/candidates\//, methods: ['GET', 'POST', 'PATCH'] },
    // Future premium features can be added here
];
```

Routes matching these patterns require:
1. Valid authentication
2. Active premium subscription

### Frontend Components

#### Candidate Components
- `CandidateSubscriptionStatus`: Shows current subscription status
- `CandidatePricingCard`: Displays pricing and handles subscription
- Pages: pricing, success, cancel, manage

#### Admin Components
- Admin dashboard for subscription monitoring
- Protected by admin access key

### Services

#### StripeService
Handles all Stripe operations:
- Customer creation
- Checkout session creation
- Portal session creation
- Subscription management
- Webhook event construction

#### PremiumSubscriptionService
Manages subscription business logic:
- Subscription CRUD operations
- Webhook event handling
- Subscription status checking
- Analytics

## Environment Variables

### Development
```env
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_CANDIDATE_BASIC_PRICE_ID=price_...
NEXT_PUBLIC_ADMIN_ACCESS_KEY=admin123
```

### Production
```env
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_CANDIDATE_BASIC_PRICE_ID=price_...
NEXT_PUBLIC_ADMIN_ACCESS_KEY=secure_key
```

## Premium Features

Current premium features for candidates ($1.99/month):
- Get to know jobs first - instant notifications
- Profile promotion to recruiters automatically
- Exclusive verified job listings daily
- Priority application processing
- Enhanced profile visibility
- Direct recruiter connections
- 14-day free trial included

## Stripe Integration

### Webhook Events Handled
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`

### Security
- Webhook signature verification
- Admin access protection
- JWT-based API authentication
- Subscription status validation

## Testing

Run the subscription system test:
```bash
npm run test-subscription
```

This tests:
- Service initialization
- Database connectivity
- Model validation
- Analytics functionality
- Subscription checking

## Admin Dashboard

Access the admin dashboard at `/admin` with the admin access key. Features:
- Subscription analytics
- System status monitoring
- User subscription management (future)

## Future Enhancements

The system is designed to support:
- Recruiter premium subscriptions
- Multiple plan tiers
- Advanced analytics
- Automated billing management
- Usage-based pricing

## Security Considerations

1. **Admin Access**: Currently uses environment variable key - should be enhanced with proper admin authentication
2. **Webhook Security**: Stripe signature verification implemented
3. **API Protection**: Premium routes protected by middleware
4. **Data Validation**: Joi validators for all inputs

## Deployment Notes

1. Set up Stripe webhook endpoint: `/api/v1/subscriptions/premium/webhook`
2. Configure environment variables
3. Create Stripe products and prices
4. Test webhook delivery
5. Monitor subscription analytics

## Support

For subscription-related issues:
- Check admin dashboard for system status
- Review Stripe webhook logs
- Monitor application logs for errors
- Verify environment variable configuration
