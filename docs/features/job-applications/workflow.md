# Job Application Workflow

> _Last updated: May 2025_

This document describes the job application workflow in the OnlyC2C application, including the status lifecycle, notifications, and implementation details.

## Table of Contents

1. [Overview](#overview)
2. [Application Status Lifecycle](#application-status-lifecycle)
3. [Email Notifications](#email-notifications)
4. [Implementation Details](#implementation-details)
5. [API Endpoints](#api-endpoints)
6. [Frontend Integration](#frontend-integration)

## Overview

The job application workflow in OnlyC2C manages the lifecycle of a job application from submission to hiring. It includes:

- Status tracking for applications
- Email notifications for status changes
- API endpoints for managing applications
- Frontend components for displaying and updating applications

## Application Status Lifecycle

Job applications follow a defined status lifecycle:

1. **PENDING**: Initial state when an application is submitted
2. **READ**: Application has been viewed by the recruiter
3. **SHORTLISTED**: Candidate has been shortlisted for further consideration
4. **REJECTED**: Application has been rejected
5. **HIRED**: Candi<PERSON> has been hired for the position

The status flow is typically:

```flowchart
PENDING → READ → SHORTLISTED → HIRED
                 ↘
                   REJECTED
```

### Status Definitions

| Status | Description | Trigger |
|--------|-------------|---------|
| `PENDING` | Application submitted but not yet viewed | Automatic on submission |
| `READ` | Application has been viewed by recruiter | Automatic when resume is viewed |
| `SHORTLISTED` | Candidate selected for further consideration | Manual by recruiter |
| `REJECTED` | Application not selected | Manual by recruiter |
| `HIRED` | Candidate selected for the position | Manual by recruiter |

## Email Notifications

The system sends email notifications at each stage of the application process:

### On Application Submission

1. **To Candidate**: Confirmation that application was received
   - Template: `job-application-confirmation.hbs`
   - Contains: Job title, company name, application tracking link

2. **To Recruiter**: Notification of new application
   - Template: `job-application-notification-recruiter.hbs`
   - Contains: Candidate name, job title, application date, link to view application

### On Status Change

1. **READ**: Email to candidate when application is viewed
   - Template: `application-viewed-notification.hbs`
   - Trigger: Automatic when recruiter views resume

2. **SHORTLISTED**: Email to candidate when shortlisted
   - Template: `application-shortlisted-notification.hbs`
   - Trigger: Manual status update by recruiter

3. **REJECTED**: Email to candidate when rejected
   - Template: `application-rejected-notification.hbs`
   - Trigger: Manual status update by recruiter
   - Includes: Recommended alternative jobs

4. **HIRED**: Email to candidate when hired
   - Template: `application-hired-notification.hbs`
   - Trigger: Manual status update by recruiter

## Implementation Details

### Data Model

The job application is represented by the `JobApplication` model:

```typescript
export interface IJobApplication extends IBaseDocument {
    job: Reference<IJob>;
    resume: Reference<IResume>;
    applicant: Reference<ICandidate>;
    recruiter: Reference<IRecruiter>;
    organization?: Reference<IOrganization>;
    status: JobApplicationStatus;
}
```

### Status Enum

The application statuses are defined in an enum:

```typescript
export enum JobApplicationStatus {
    PENDING = 'PENDING',
    READ = 'READ',
    REJECTED = 'REJECTED',
    SHORTLISTED = 'SHORTLISTED',
    HIRED = 'HIRED',
}
```

### JobApplicationService

The `JobApplicationService` handles the core application logic:

```typescript
class JobApplicationService extends BaseService<IJobApplication, JobApplicationRepository, QueryJobApplicationParams> {
    // Create a new job application and send confirmation emails
    async create(data: IJobApplication): Promise<IJobApplication> {
        // Implementation...
    }

    // Update the status of a job application and send appropriate notifications
    async updateStatus(id: string, status: JobApplicationStatus): Promise<IJobApplication> {
        // Implementation...
    }

    // Private methods for sending various notification emails
    private async sendApplicationCreatedEmails(application: IJobApplication): Promise<void> {
        // Implementation...
    }

    private async sendApplicationViewedEmail(application: IJobApplication): Promise<void> {
        // Implementation...
    }

    // Other notification methods...
}
```

### Email Notifications Utilities

Email notifications are sent using the email utilities:

```typescript
// Send job application confirmation to candidate
await sendJobApplicationConfirmationEmail(
    candidateName,
    candidateEmail,
    jobTitle,
    companyName,
    candidateApplicationUrl
);

// Send notification to recruiter
await sendJobApplicationNotificationToRecruiter(
    recruiterName,
    recruiterEmail,
    candidateName,
    jobTitle,
    applicationDate,
    applicationUrl
);
```

## API Endpoints

### Create Job Application

```text
POST /api/v1/job-applications
```

Request body:

```json
{
  "job": "job_id",
  "resume": "resume_id",
  "applicant": "candidate_id"
}
```

### Update Application Status

```text
PATCH /api/v1/job-applications/:id/status
```

Request body:

```json
{
  "status": "READ" // or "SHORTLISTED", "REJECTED", "HIRED"
}
```

### Get Applications by Job ID

```text
GET /api/v1/job-applications?job=job_id
```

### Get Applications by Applicant ID

```text
GET /api/v1/job-applications?applicant=applicant_id
```

## Frontend Integration

### Recruiter Dashboard

The recruiter dashboard includes a `JobApplicationTable` component that:

1. Displays all applications for a job
2. Allows viewing applicant resumes
3. Automatically updates status to READ when resume is viewed
4. Provides UI for updating application status

```javascript
// Handle resume view
const handleResumeOnClick = async (application) => {
    window.open(application.resume.file.url, '_blank');
    if (application.status !== 'READ') {
        try {
            await updateApplicationStatus(application._id, 'READ');
            fetchApplications(pagination.page);
            toast.success('Application marked as read!');
        } catch (error) {
            console.error('Failed to update application status:', error);
            toast.error('Failed to update status');
        }
    }
};
```

### Candidate Dashboard

The candidate dashboard includes:

1. A list of all applications submitted by the candidate
2. Current status of each application
3. Links to view job details
4. Timestamps for application submission and status changes

## Best Practices

1. **Status Updates**: Only allow status to move forward in the workflow, not backward
2. **Email Notifications**: Send notifications for all status changes
3. **Error Handling**: Implement proper error handling for failed status updates
4. **Validation**: Validate status changes to ensure they follow the allowed workflow
5. **Tracking**: Track all status changes with timestamps for auditing purposes
