# Email Templates

> _Last updated: May 2025_

This document provides an overview of the email template system used in the OnlyC2C application.

## Table of Contents

1. [Overview](#overview)
2. [Template Structure](#template-structure)
3. [Available Templates](#available-templates)
4. [Components](#components)
5. [Creating New Templates](#creating-new-templates)
6. [Styling](#styling)
7. [Best Practices](#best-practices)

## Overview

OnlyC2C uses Handlebars as its templating engine for emails. The templates are located in the `src/templates/emails` directory and are organized as follows:

- Individual email templates (`.hbs` files)
- Reusable components in the `components` subdirectory
- Shared styles in `components/styles.hbs`

## Template Structure

Each email template follows this structure:

```handlebars
<!-- Subject: Your Email Subject Line -->
{{#> components/layout title="Page Title" logoImage="logo_filename.png" year=year}}
  <p class="greeting">Hello, {{name}}</p>
  <p class="main-message">Your Main Message</p>
  <p class="sub-message">Your secondary message with more details.</p>

  <p class="message-text">
    Additional content and details about the email.
  </p>

  <a href="{{actionUrl}}" class="button" style="color: #fff;">⚡ Call to Action</a>
{{/components/layout}}
```

Key elements:

- The first line contains the email subject in an HTML comment
- The template uses the layout component with parameters
- Content is placed inside the layout block
- Variables are inserted using `{{variableName}}` syntax

## Available Templates

### User Onboarding

| Template | Purpose | Required Variables |
|----------|---------|-------------------|
| `welcome-candidate.hbs` | Welcome email for new candidates | `name`, `supportUrl`, `year` |
| `welcome-recruiter.hbs` | Welcome email for new recruiters | `name`, `supportUrl`, `year` |

### Job Applications

| Template | Purpose | Required Variables |
|----------|---------|-------------------|
| `job-application-confirmation.hbs` | Confirms job application submission to candidate | `jobTitle`, `companyName`, `applicationUrl`, `year` |
| `job-application-notification-recruiter.hbs` | Notifies recruiter of new application | `candidateName`, `jobTitle`, `applicationDate`, `applicationUrl`, `year` |
| `application-viewed-notification.hbs` | Notifies candidate when application is viewed | `candidateName`, `jobTitle`, `companyName`, `applicationUrl`, `year` |
| `application-shortlisted-notification.hbs` | Notifies candidate when shortlisted | `candidateName`, `jobTitle`, `companyName`, `applicationUrl`, `year` |
| `job-closed-notification.hbs` | Notifies applicants when a job is closed | `candidateName`, `jobTitle`, `companyName`, `jobSearchUrl`, `year` |
| `application-rejected-notification.hbs` | Notifies candidate when rejected (with alternatives) | `candidateName`, `jobTitle`, `companyName`, `recommendedJobsCount`, `moreJobsUrl`, `year` |
| `application-hired-notification.hbs` | Notifies candidate when hired | `candidateName`, `jobTitle`, `companyName`, `applicationUrl`, `year` |
| `new-external-job-notification.hbs` | Notifies candidates about new external job opportunities | `candidateName`, `role`, `recruiterEmail`, `jobLink`, `jobDescription`, `year` |
| `new-c2c-job-notification.hbs` | Notifies candidates about new C2C job opportunities | `candidateName`, `jobTitle`, `companyName`, `location`, `jobType`, `jobId`, `jobUrl`, `jobDescription`, `recruiterName`, `hourlyRate`, `skills`, `year` |

## Components

The template system uses reusable components to maintain consistency:

### Layout (`components/layout.hbs`)

The main layout component that wraps all emails:

```handlebars
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{title}}</title>
  <style>
    {{> components/styles}}
  </style>
</head>
<body>
  <div class="container">
    {{> components/header logoImage=logoImage}}
    <div class="content">
      {{> @partial-block this}}
    </div>
    {{> components/footer year=year}}
  </div>
</body>
</html>
```

### Header (`components/header.hbs`)

Contains the email header with logo:

```handlebars
<div class="header">
  <a href="https://onlyc2c.com" target="_blank">
    <img src="https://onlyc2c.com/assets/images/{{logoImage}}" alt="OnlyC2C Logo" class="logo">
  </a>
</div>
```

### Footer (`components/footer.hbs`)

Contains the email footer with social links and company information:

```handlebars
<div class="footer">
  <div class="social-icons">
    <a href="https://www.linkedin.com/company/onlyc2c" target="_blank">
      <img src="https://onlyc2c.com/assets/images/linkedin_logo.png" alt="LinkedIn">
    </a>
    <a href="https://www.reddit.com/r/only_c2c_jobs/" target="_blank">
      <img src="https://onlyc2c.com/assets/images/reddit_logo.webp" alt="Reddit">
    </a>
  </div>
  <p class="footer-text">&copy; {{year}} OnlyC2C LLC</p>
  <p class="footer-text">5858 Blackshire Path, STE B-2<br>Invergrove Heights, MN 55076</p>
  <div class="footer-links">
    <a href="https://onlyc2c.com/privacy">Privacy Policy</a> |
    <a href="https://onlyc2c.com/terms">Terms</a> |
    <a href="https://onlyc2c.com/blog">Blog</a> |
    <a href="https://onlyc2c.com/unsubscribe">Unsubscribe</a>
  </div>
</div>
```

### Styles (`components/styles.hbs`)

Contains shared CSS styles for all emails:

```css
@import url('https://fonts.googleapis.com/css2?family=Geist+Sans:wght@300;400;500;600;700&display=swap');

body {
  font-family: 'Geist Sans', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 2px solid #f5f7ff;
}

/* Additional styles... */
```

## Creating New Templates

To create a new email template:

1. Create a new `.hbs` file in the `src/templates/emails` directory
2. Start with the subject line in an HTML comment: `<!-- Subject: Your Subject -->`
3. Use the layout component: `{{#> components/layout title="Title" logoImage="logo.png" year=year}}`
4. Add your content inside the layout block
5. Close the layout block: `{{/components/layout}}`
6. Use existing CSS classes for consistent styling

Example:

```handlebars
<!-- Subject: Your Custom Email Subject -->
{{#> components/layout title="Custom Email - OnlyC2C" logoImage="onlyc2c_for_candidates.png" year=year}}
<p class="greeting">Hello, {{name}}</p>
<p class="main-message">Your Main Message</p>
<p class="sub-message">Your detailed message goes here.</p>

<a href="{{actionUrl}}" class="button" style="color: #fff;">⚡ Your Call to Action</a>
{{/components/layout}}
```

## Styling

The email templates use a consistent style with these key elements:

- **Font**: Geist Sans (with fallbacks)
- **Colors**:
  - Primary: #0077B6 (blue)
  - Background: #f5f5f5 (light gray)
  - Content: #ffffff (white)
  - Text: #333333 (dark gray)
  - Accent: #4a0072 (purple)
- **Components**:
  - Rounded corners (10px border-radius)
  - Subtle shadows
  - Clear call-to-action buttons
  - Consistent spacing

### CSS Classes

| Class | Purpose |
|-------|---------|
| `container` | Main email container |
| `header` | Email header with logo |
| `content` | Main content area |
| `footer` | Email footer with links |
| `greeting` | Personal greeting |
| `main-message` | Primary headline |
| `sub-message` | Secondary headline |
| `message-text` | Body text |
| `button` | Call-to-action button |

## Best Practices

When working with email templates:

1. **Test Thoroughly**: Email clients render HTML/CSS differently. Test in multiple clients.
2. **Keep It Simple**: Use basic HTML and inline CSS for maximum compatibility.
3. **Be Concise**: Keep emails short and focused on a single action.
4. **Use Variables**: Don't hardcode values that might change.
5. **Maintain Consistency**: Use the existing components and styles.
6. **Include Plain Text**: Always provide a plain text alternative (handled automatically).
7. **Optimize Images**: Use small, optimized images with proper alt text.
8. **Follow Branding**: Adhere to OnlyC2C's brand guidelines for colors and tone.

### Email-Specific Considerations

- Email clients don't support all CSS properties
- Tables are often used for layout in emails
- CSS should be inline or in the `<head>` section
- Images should have absolute URLs
- Max width should be 600px for best compatibility
