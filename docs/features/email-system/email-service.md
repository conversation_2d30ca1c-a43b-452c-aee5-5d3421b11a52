# Email Service

> _Last updated: May 2025_

This document provides an overview of the email service implementation in the OnlyC2C application.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Configuration](#configuration)
4. [Email Queue](#email-queue)
5. [Usage](#usage)
6. [Testing](#testing)

## Overview

The OnlyC2C email system provides a robust solution for sending transactional emails to users. It includes:

- A templating system using Handlebars
- A queuing mechanism for reliable delivery
- Retry logic for failed emails
- Support for HTML and plain text emails
- Reusable components for consistent styling

## Architecture

The email system consists of several key components:

### EmailService

Located at `src/services/EmailService.ts`, this service handles:

- SMTP transport configuration
- Template compilation
- Email sending
- HTML to plain text conversion

### Email Queue

Located at `src/utils/server/emailQueue.ts`, the queue system:

- Manages concurrent email sending
- Implements retry logic with exponential backoff
- Tracks email statistics (queued, sent, failed)
- Prevents overwhelming the SMTP server

### Email Utilities

Located at `src/utils/server/emailUtils.ts`, these utilities provide:

- High-level functions for sending specific types of emails
- Context preparation for templates
- Default settings for all emails

## Configuration

The email service is configured using environment variables:

```env
# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=OnlyC2C

# Email Queue Configuration
EMAIL_QUEUE_CONCURRENCY=5
EMAIL_QUEUE_INTERVAL=1000
EMAIL_QUEUE_RETRY_COUNT=3
```

### Configuration Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `EMAIL_HOST` | SMTP server hostname | Required |
| `EMAIL_PORT` | SMTP server port | 465 |
| `EMAIL_SECURE` | Use SSL/TLS | true |
| `EMAIL_USER` | SMTP username | Required |
| `EMAIL_PASSWORD` | SMTP password or app password | Required |
| `EMAIL_FROM` | Default sender email | `EMAIL_USER` |
| `EMAIL_FROM_NAME` | Default sender name | "OnlyC2C" |
| `EMAIL_QUEUE_CONCURRENCY` | Maximum concurrent emails | 5 |
| `EMAIL_QUEUE_INTERVAL` | Milliseconds between sends | 1000 |
| `EMAIL_QUEUE_RETRY_COUNT` | Maximum retry attempts | 3 |

## Email Queue Benefits

The email queue system provides several benefits:

1. **Rate Limiting**: Prevents overwhelming the SMTP server by limiting concurrent sends
2. **Reliability**: Automatically retries failed emails with exponential backoff
3. **Monitoring**: Tracks statistics about email sending success/failure
4. **Asynchronous Processing**: Doesn't block the main application flow

### Queue Implementation

The queue is implemented using the `p-queue` library and provides:

```typescript
// Add an email to the queue
await queueEmail({
  to: '<EMAIL>',
  template: 'welcome-candidate',
  context: { name: 'John Doe' }
});

// Get queue statistics
const stats = emailQueueService.getStats();

// Pause/resume the queue
emailQueueService.pause();
emailQueueService.resume();
```

## Usage

### Basic Usage

To send an email using a template:

```typescript
import { sendEmail } from '@/utils/server/emailUtils';

await sendEmail({
  to: '<EMAIL>',
  template: 'welcome-candidate',
  context: {
    name: 'John Doe',
    supportUrl: 'https://onlyc2c.com',
    year: new Date().getFullYear()
  }
});
```

### Specialized Email Functions

The system provides specialized functions for common email types:

```typescript
// Send welcome email
await sendWelcomeEmail(
  'John Doe',
  '<EMAIL>',
  'candidate'
);

// Send job application confirmation
await sendJobApplicationConfirmationEmail(
  'John Doe',
  '<EMAIL>',
  'Software Engineer',
  'Acme Inc',
  'https://onlyc2c.com/my-applications'
);
```

### Job Application Status Notifications

The `JobApplicationService` automatically sends appropriate emails when an application's status changes:

```typescript
// This will trigger the appropriate email notification
await jobApplicationService.updateStatus(applicationId, JobApplicationStatus.VIEWED);
```

Status change emails include:

- Application submitted (to both candidate and recruiter)
- Application viewed
- Application shortlisted
- Application rejected (with suggested alternatives)
- Application accepted (hired)

## Testing

### Test API Endpoint

The application includes a test API endpoint for sending test emails:

```text
POST /api/v1/email/test
```

Request body:

```json
{
  "type": "welcome-candidate",
  "email": "<EMAIL>"
}
```

Supported email types:

- `welcome-candidate`
- `welcome-recruiter`
- `job-application`
- `application-notification-recruiter`
- `application-viewed`
- `application-rejected`
- `application-shortlisted`
- `application-hired`

### Manual Testing

You can also test the email service directly in development:

```typescript
import { emailService } from '@/services/EmailService';

// Test direct sending
await emailService.sendEmail({
  to: '<EMAIL>',
  subject: 'Test Email',
  html: '<p>This is a test email</p>'
});

// Test template rendering
await emailService.sendEmail({
  to: '<EMAIL>',
  template: 'welcome-candidate',
  context: { name: 'Test User', supportUrl: 'http://localhost:3000' }
});
```
