# LinkedIn Custom Callback URL

This document explains how to configure and use custom callback URLs for LinkedIn authentication in the OnlyC2C application.

## Overview

The LinkedIn authentication system now supports customizable callback URLs, allowing you to:

1. **Set a custom redirect URI** for LinkedIn OAuth flow
2. **Return users to their original page** after authentication
3. **Handle authentication from different parts of the application**

## Configuration

### Environment Variables

You can set a custom LinkedIn redirect URI using the `LINKEDIN_REDIRECT_URI` environment variable:

```bash
# Optional: Custom LinkedIn redirect URI
LINKEDIN_REDIRECT_URI=https://yourdomain.com/api/v1/linkedin/callback
```

If not set, it defaults to: `${NEXTAUTH_URL}/api/v1/linkedin/callback`

### Development Environment

```bash
# .env.development
LINKEDIN_REDIRECT_URI=http://localhost:3000/api/v1/linkedin/callback
```

### Production Environment

```bash
# .env.production
LINKEDIN_REDIRECT_URI=https://onlyc2c.com/api/v1/linkedin/callback
```

## Usage

### Basic Usage

The LinkedIn integration component automatically uses the current page URL as the return URL:

```javascript
import { connectLinkedIn } from '@/utils/linkedinOps';

// This will return the user to the current page after authentication
const response = await connectLinkedIn();
```

### Custom Return URL

You can specify a custom return URL:

```javascript
import { connectLinkedIn } from '@/utils/linkedinOps';

// Return to a specific page after authentication
const response = await connectLinkedIn('/dashboard/profile');

// Return to an external URL
const response = await connectLinkedIn('https://example.com/success');
```

### API Endpoint Usage

You can also call the API endpoint directly with a custom return URL:

```javascript
const response = await fetch('/api/v1/linkedin/auth?returnUrl=/custom/page');
```

## How It Works

### Authentication Flow

1. **User initiates LinkedIn connection** from any page
2. **Current page URL is captured** and passed as `returnUrl` parameter
3. **LinkedIn OAuth flow begins** with the custom callback URL
4. **User authenticates with LinkedIn**
5. **LinkedIn redirects to the callback URL** with authorization code
6. **Callback handler processes the authentication** and extracts the return URL from state
7. **User is redirected back** to their original page (or custom URL) with success/error status

### State Parameter

The authentication state includes:

```javascript
{
  r: "random_string",     // Security random string
  u: "user_id",          // User ID
  returnUrl: "/original/page"  // Optional return URL
}
```

### URL Handling

The callback handler supports both relative and absolute URLs:

- **Relative URLs**: `/dashboard/settings` → `https://onlyc2c.com/dashboard/settings`
- **Absolute URLs**: `https://example.com/page` → Used as-is

## Error Handling

All error scenarios redirect to the appropriate return URL with error parameters:

- `error=linkedin_auth_failed` - LinkedIn OAuth error
- `error=invalid_state` - Invalid state parameter
- `error=user_not_found` - User ID not found in state
- `error=token_exchange_failed` - Failed to exchange code for token
- `error=profile_fetch_failed` - Failed to fetch LinkedIn profile
- `error=unexpected_error` - Unexpected server error

## Success Handling

Successful authentication redirects with:

- `success=linkedin_connected` - LinkedIn account successfully connected

## LinkedIn App Configuration

Make sure your LinkedIn app is configured with the correct redirect URI:

1. Go to [LinkedIn Developer Console](https://www.linkedin.com/developers/)
2. Select your app
3. Go to "Auth" tab
4. Add your redirect URI to "Authorized redirect URLs for your app"
   - Development: `http://localhost:3000/api/v1/linkedin/callback`
   - Production: `https://onlyc2c.com/api/v1/linkedin/callback`
   - Custom: Your custom `LINKEDIN_REDIRECT_URI` value

## Examples

### Settings Page Integration

```javascript
// In settings page component
const handleLinkedInConnect = async () => {
  try {
    // Will return to current settings page after auth
    const response = await connectLinkedIn();
    // Handle popup or redirect
  } catch (error) {
    console.error('LinkedIn connection failed:', error);
  }
};
```

### Dashboard Integration

```javascript
// In dashboard component
const handleLinkedInConnect = async () => {
  try {
    // Return to dashboard after auth
    const response = await connectLinkedIn('/dashboard');
    // Handle popup or redirect
  } catch (error) {
    console.error('LinkedIn connection failed:', error);
  }
};
```

### Custom Integration

```javascript
// Custom integration with specific return URL
const handleLinkedInConnect = async () => {
  try {
    // Return to a specific page with custom parameters
    const returnUrl = '/dashboard/integrations?tab=linkedin&status=connected';
    const response = await connectLinkedIn(returnUrl);
    // Handle popup or redirect
  } catch (error) {
    console.error('LinkedIn connection failed:', error);
  }
};
```

## Security Considerations

- The state parameter is base64 encoded and includes a random component for security
- Return URLs are validated to prevent open redirect vulnerabilities
- Only authenticated users can initiate LinkedIn connections
- All errors are logged server-side for monitoring

## Troubleshooting

### Common Issues

1. **Invalid redirect URI**: Ensure your LinkedIn app has the correct redirect URI configured
2. **State parameter errors**: Check that the authentication flow isn't being interrupted
3. **Return URL not working**: Verify the URL format and accessibility

### Debug Information

Check server logs for detailed error information during the authentication flow.
