# NextAuth Integration

> _Last updated: May 2025_

This document describes the NextAuth.js integration in the OnlyC2C application, including configuration, custom adapters, and authentication flow.

## Table of Contents

1. [Overview](#overview)
2. [Configuration](#configuration)
3. [MongoDB Adapter](#mongodb-adapter)
4. [Authentication Flow](#authentication-flow)
5. [Custom JWT Implementation](#custom-jwt-implementation)
6. [Middleware Protection](#middleware-protection)
7. [Session Management](#session-management)
8. [Type Extensions](#type-extensions)

## Overview

OnlyC2C uses NextAuth.js (v5) for authentication, with the following key features:

- OAuth providers (Google and LinkedIn)
- Custom MongoDB adapter
- Extended user profiles (Candidate and Recruiter)
- Custom JWT implementation for API authentication
- Protected routes via middleware

## Configuration

The NextAuth configuration is defined in `src/utils/server/authOptions.ts`:

```typescript
export const authOptions: NextAuthConfig = {
    secret: process.env.NEXTAUTH_SECRET,
    adapter,
    providers: [
        GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            allowDangerousEmailAccountLinking: true,
        }),
        LinkedIn({
            clientId: process.env.LINKEDIN_CLIENT_ID,
            clientSecret: process.env.LINKEDIN_CLIENT_SECRET,
            allowDangerousEmailAccountLinking: true,
        })
    ],
    callbacks: {
        async session({ session, user }) {
            if (session.user) {
                session.user.id = user.id;

                // Include candidate profile if it exists
                if (user.candidateProfile) {
                    session.user.candidateProfile = user.candidateProfile;
                }

                // Include recruiter profile if it exists
                if (user.recruiterProfile) {
                    session.user.recruiterProfile = user.recruiterProfile;
                }
            }

            return session;
        },
    },
    events: {
        async signOut() {
            const c = await cookies();
            c.delete('x-c2c-token');

            if (typeof window !== 'undefined') {
                document.cookie = 'x-c2c-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            }
        },
    },
};
```

### Environment Variables

The following environment variables are required for NextAuth:

```env
# Authentication
NEXTAUTH_URL=https://onlyc2c.com  # or http://localhost:3000 for development
NEXTAUTH_SECRET=your_secret_key
AUTH_TRUST_HOST=true  # for production

# OAuth Providers
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
```

## MongoDB Adapter

A custom MongoDB adapter is implemented in `src/lib/auth/mongodb-adapter.ts` to:

1. Store authentication data in MongoDB
2. Extend the user object with candidate and recruiter profiles
3. Handle account linking and session management

Key features of the adapter:

```typescript
export function MongoDBAdapter(): Adapter {
  return {
    async createUser(userData) {
      // Create a new user in the database
    },
    
    async getUser(id) {
      // Get a user by ID
    },
    
    async getUserByEmail(email) {
      // Get a user by email
    },
    
    async getUserByAccount({ provider, providerAccountId }) {
      // Get a user by OAuth provider account
    },
    
    async updateUser(userData) {
      // Update a user's data
    },
    
    async deleteUser(userId) {
      // Delete a user and related data
    },
    
    async linkAccount(accountData) {
      // Link an OAuth account to a user
    },
    
    async unlinkAccount({ provider, providerAccountId }) {
      // Unlink an OAuth account from a user
    },
    
    async createSession(sessionData) {
      // Create a new session
    },
    
    async getSessionAndUser(sessionToken) {
      // Get a session and its associated user
      // This is extended to include candidate and recruiter profiles
    },
    
    async updateSession(sessionData) {
      // Update a session
    },
    
    async deleteSession(sessionToken) {
      // Delete a session
    },
    
    async createVerificationToken(verificationTokenData) {
      // Create a verification token
    },
    
    async useVerificationToken({ identifier, token }) {
      // Use a verification token
    },
  };
}
```

## Authentication Flow

The authentication flow in OnlyC2C works as follows:

1. **User Sign-In**:
   - User clicks on Google or LinkedIn sign-in button
   - NextAuth handles the OAuth flow
   - User is redirected back to the application

2. **Profile Creation**:
   - On first sign-in, a basic user record is created
   - User is redirected to profile setup based on role
   - Candidate or Recruiter profile is created

3. **JWT Generation**:
   - After profile creation, a custom JWT is generated
   - JWT contains user ID, profile ID, and role
   - JWT is stored in cookies for subsequent requests

4. **Protected Routes**:
   - Frontend routes (`/dashboard/*`, `/my-applications/*`) are protected by middleware
   - API routes (`/api/v1/*`) are protected by middleware
   - Unauthenticated users are redirected to login

## Custom JWT Implementation

OnlyC2C uses a custom JWT implementation for API authentication:

```typescript
export async function generateJwtToken(profile: ICandidate | IRecruiter, scope: string): Promise<string> {
    if (!PRIVATE_JWK) {
        throw new Error('PRIVATE_JWK is not set in environment variables');
    }

    const alg = 'RS256';
    const jwk = JSON.parse(PRIVATE_JWK as string);
    const privateKey = await importJWK(jwk, alg);

    const user = profile.user as IUser;
    const payload: IUserPayload = {
        sub: user._id.toString(),
        name: user.name || '',
        email: user.email,
        profile_id: profile._id.toString(),
        scope: scope,
    }

    const jwt = await new SignJWT({ ...payload })
        .setProtectedHeader({ alg })
        .setIssuedAt()
        .setExpirationTime('7d')
        .sign(privateKey);

    return jwt;
}
```

The JWT is verified using:

```typescript
export async function verifyJwtToken(token: string): Promise<IUserPayload | undefined> {
    try {
        if (!PUBLIC_JWK) {
            throw new Error('PUBLIC_JWK is not set in environment variables');
        }

        const jwk = JSON.parse(PUBLIC_JWK as string);
        const { payload } = await jwtVerify<IUserPayload>(token, jwk);
        return payload;
    } catch (error) {
        console.log(error);
    }
}
```

## Middleware Protection

### Frontend Middleware

The frontend middleware protects dashboard and application routes:

```typescript
export async function frontendMiddleware(request: NextRequest) {
    const url = request.nextUrl.pathname;
    
    // Check for authentication token
    const token = await getTokenFromCookieOrRequest(request);
    if (!token) {
        // Redirect to login with callback URL
        return redirectToLogin(request);
    }

    try {
        // Verify token
        const payload = await verifyJwtToken(token);
        if (!payload) throw new Error('Invalid token');

        // Token is valid, allow access
        return NextResponse.next();
    } catch (error) {
        // Redirect to login with callback URL on invalid token
        return redirectToLogin(request);
    }
}
```

### Backend Middleware

The backend middleware protects API routes:

```typescript
export async function backendMiddleware(request: NextRequest) {
    const url = request.nextUrl.pathname;
    const method = request.method;

    // Check if route is public
    if (publicApiRoutes.some(p => p.path.test(url) && p.methods.includes(method))) {
        return NextResponse.next({ request });
    }

    // Check for authentication token
    const token = await getTokenFromCookieOrRequest(request);
    if (!token) {
        return BaseResponse.unauthorized();
    }

    try {
        // Verify token and set user headers
        const payload = await verifyJwtToken(token);
        if (!payload) throw new Error('Invalid token');

        request.headers.set(USER_ID_HEADER, payload.sub);
        request.headers.set(USER_NAME_HEADER, payload.name);
        request.headers.set(USER_PROFILE_ID_HEADER, payload.profile_id);
        request.headers.set(USER_EMAIL_HEADER, payload.email);
        request.headers.set(USER_SCOPE_HEADER, payload.scope);
    } catch (error) {
        return BaseResponse.unauthorized();
    }

    return NextResponse.next({ request });
}
```

## Session Management

NextAuth sessions are extended to include candidate and recruiter profiles:

```typescript
async session({ session, user }) {
    if (session.user) {
        session.user.id = user.id;

        // Include candidate profile if it exists
        if (user.candidateProfile) {
            session.user.candidateProfile = user.candidateProfile;
        }

        // Include recruiter profile if it exists
        if (user.recruiterProfile) {
            session.user.recruiterProfile = user.recruiterProfile;
        }
    }

    return session;
}
```

This allows the application to access profile information directly from the session.

## Type Extensions

The NextAuth types are extended in `src/types/next-auth.d.ts`:

```typescript
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      candidateProfile?: {
        id: string;
        phone?: string;
        role?: string;
        skills?: any[];
      } | null;
      recruiterProfile?: {
        id: string;
        phone?: string;
        organization?: any;
      } | null;
    } & DefaultSession["user"];
  }

  interface User extends DefaultUser {
    candidateProfile?: {
      id: string;
      phone?: string;
      role?: string;
      skills?: any[];
    } | null;
    recruiterProfile?: {
      id: string;
      phone?: string;
      organization?: any;
    } | null;
  }
}

// Extend AdapterUser to include our custom fields
declare module "@auth/core/adapters" {
  interface AdapterUser {
    candidateProfile?: {
      id: string;
      phone?: string;
      role?: string;
      skills?: any[];
    } | null;
    recruiterProfile?: {
      id: string;
      phone?: string;
      organization?: any;
    } | null;
  }
}
```

These type extensions ensure proper TypeScript support throughout the application.
