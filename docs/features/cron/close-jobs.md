# Close Jobs API

> _Last updated: June 2025_

This document describes the Close Jobs API endpoint that automatically closes job postings older than a specified number of days.

## Overview

The Close Jobs API (`/api/cron/close-jobs`) is designed to be called by cron jobs or automated systems to clean up old job postings. It finds all active jobs older than a specified threshold (default: 3 days) and closes them by setting their `active` status to `false`.

## Endpoint Details

- **URL**: `/api/cron/close-jobs`
- **Method**: `POST`
- **Authentication**: API Key required
- **Environment**: Production only

## Authentication

The endpoint requires an API key to be provided in the `x-c2c-api-key` header. The API key must match the `X_CRON_API_KEY` environment variable.

```bash
curl -X POST https://onlyc2c.com/api/cron/close-jobs \
  -H "x-c2c-api-key: your_cron_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{"days": 3}'
```

## Request Body

The request body is optional and can contain:

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| `days` | number | No | 3 | Number of days to look back for old jobs |

### Example Request Body

```json
{
  "days": 5
}
```

## Response Format

### Success Response

```json
{
  "success": true,
  "message": "Job closure process completed",
  "data": {
    "closedCount": 15,
    "daysThreshold": 3,
    "errors": [],
    "hasErrors": false
  }
}
```

### Response with Errors

```json
{
  "success": true,
  "message": "Job closure process completed",
  "data": {
    "closedCount": 12,
    "daysThreshold": 3,
    "errors": [
      "Failed to close job JOB123: Job not found",
      "Failed to close job JOB456: Database connection error"
    ],
    "hasErrors": true
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "Error: Invalid API key"
}
```

## Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Whether the overall operation was successful |
| `message` | string | Human-readable message about the operation |
| `data.closedCount` | number | Number of jobs successfully closed |
| `data.daysThreshold` | number | The days threshold used for the operation |
| `data.errors` | string[] | Array of error messages for individual job closures |
| `data.hasErrors` | boolean | Whether any errors occurred during job closure |

## Status Codes

| Code | Description |
|------|-------------|
| 200 | Success (even if some individual job closures failed) |
| 401 | Unauthorized (missing or invalid API key, or non-production environment) |
| 500 | Internal server error |

## Environment Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `ENV` | Must be set to `PRODUCTION` | `PRODUCTION` |
| `X_CRON_API_KEY` | API key for cron job authentication | Generated using `npm run generate-api-key` |

### Setting Up the API Key

1. Generate a new API key:
   ```bash
   npm run generate-api-key
   ```

2. Add the generated key to your production environment variables:
   ```env
   X_CRON_API_KEY=your_generated_key_here
   ```

## Job Closure Process

When a job is closed, the following actions occur:

1. **Status Update**: The job's `active` field is set to `false`
2. **Cache Invalidation**: Relevant pages are revalidated
3. **Notifications**: Email notifications are sent to all applicants informing them that the job has been closed
4. **Logging**: The closure is logged for monitoring purposes

## Cron Job Setup

### GitHub Actions Example

```yaml
name: Close Old Jobs
on:
  schedule:
    - cron: '0 2 * * *'  # Run daily at 2 AM UTC

jobs:
  close-jobs:
    runs-on: ubuntu-latest
    steps:
      - name: Close old jobs
        run: |
          curl -X POST https://onlyc2c.com/api/cron/close-jobs \
            -H "x-c2c-api-key: ${{ secrets.X_CRON_API_KEY }}" \
            -H "Content-Type: application/json" \
            -d '{"days": 3}'
```

### Server Cron Example

```bash
# Add to crontab (crontab -e)
# Run daily at 2 AM
0 2 * * * curl -X POST https://onlyc2c.com/api/cron/close-jobs \
  -H "x-c2c-api-key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"days": 3}' >> /var/log/close-jobs.log 2>&1
```

## Testing

### Development Testing

Use the test script to verify the API functionality:

```bash
npm run test-close-jobs
```

This script tests:
- API key validation
- Environment restrictions
- Request/response format

### Manual Testing

For manual testing in production:

```bash
curl -X POST https://onlyc2c.com/api/cron/close-jobs \
  -H "x-c2c-api-key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"days": 7}' \
  -v
```

## Monitoring and Logging

The API provides detailed logging for monitoring:

- **Start**: Logs when the job closure process begins
- **Progress**: Logs each job that is closed
- **Errors**: Logs any errors encountered during closure
- **Summary**: Logs the final count of closed jobs

Monitor the application logs to track the API's performance and identify any issues.

## Security Considerations

1. **API Key Protection**: Keep the `X_CRON_API_KEY` secure and rotate it regularly
2. **Environment Restriction**: The API only works in production to prevent accidental job closures
3. **Rate Limiting**: Consider implementing rate limiting if called frequently
4. **Monitoring**: Monitor API usage for unusual patterns

## Related Documentation

- [API Keys Management](../../security/api-keys.md)
- [Environment Setup](../../getting-started/environment-setup.md)
- [Job Management](../jobs/job-management.md)
