# Sitemap Submission API

> _Last updated: June 2025_

This document explains the API endpoint for submitting the OnlyC2C sitemap to search engines.

## Overview

The sitemap submission API provides a way to submit the OnlyC2C sitemap to search engines. This API is used by:

1. The GitHub Actions workflow that runs daily (see [GitHub Actions documentation](../../deployment/github-actions.md#sitemap-submission-workflow))
2. Manual submissions through the npm script `submit-sitemaps`

## API Endpoint

The sitemap submission API endpoint is:

```url
https://onlyc2c.com/api/cron/submit-sitemap
```

This endpoint:

1. Verifies the API key
2. Checks that the environment is production
3. Submits the sitemap to Google Search Console
4. Returns a success or failure response

## Authentication

The endpoint is protected by an API key that must be provided in the `x-c2c-api-key` header. For details on API key management, see the [API Keys documentation](../../security/api-keys.md).

## Implementation Details

The API endpoint is implemented in `src/app/api/cron/submit-sitemap/route.ts` and uses utilities from:

- `src/utils/server/sitemapUtils.ts`: Utilities for generating and submitting sitemaps
- `src/utils/server/apiKeyUtils.ts`: Utilities for API key verification

## Manual Submission

To manually submit sitemaps without using GitHub Actions:

```bash
# Generate and submit sitemaps
npm run generate-sitemaps -- --submit

# Or just submit existing sitemaps
npm run submit-sitemaps
```

For more details on these scripts, see the [NPM Scripts documentation](../../getting-started/npm-scripts.md#sitemap-management-scripts).

## Google Search Console Setup

For the sitemap submission to work, you need to set up Google Search Console:

1. Create a service account in Google Cloud Console
2. Grant the service account access to your Search Console property
3. Download the service account key file
4. Set the path to the key file in the environment variable `GOOGLE_SERVICE_ACCOUNT_KEY`

## Related Files

- `src/app/api/cron/submit-sitemap/route.ts`: The API endpoint for sitemap submission
- `src/utils/server/sitemapUtils.ts`: Utilities for generating and submitting sitemaps
- `src/scripts/submit-sitemaps.ts`: Script for manual sitemap submission
