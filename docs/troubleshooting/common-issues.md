# Common Issues and Solutions

> _Last updated: May 2025_

This document provides solutions for common issues you might encounter when working with the OnlyC2C application.

## Table of Contents

1. [Development Environment Issues](#development-environment-issues)
2. [Build Issues](#build-issues)
3. [Authentication Issues](#authentication-issues)
4. [Database Issues](#database-issues)
5. [Email Issues](#email-issues)
6. [Deployment Issues](#deployment-issues)

## Development Environment Issues

### "Module not found" errors

**Problem**: When starting the development server, you see errors about modules not being found.

**Solution**:

1. Ensure all dependencies are installed:

   ```bash
   npm ci
   ```

2. Check for TypeScript path aliases in `tsconfig.json` and ensure they're correctly configured.

3. Clear the Next.js cache:

   ```bash
   rm -rf .next
   ```

### Port already in use

**Problem**: When starting the development server, you get an error that port 3000 is already in use.

**Solution**:

1. Find and kill the process using port 3000:

   ```bash
   # On macOS/Linux
   lsof -i :3000
   kill -9 <PID>
   
   # On Windows
   netstat -ano | findstr :3000
   taskkill /PID <PID> /F
   ```

2. Alternatively, use a different port:

   ```bash
   PORT=3001 npm run dev
   ```

### Hot reloading not working

**Problem**: Changes to files are not automatically reflected in the browser.

**Solution**:

1. Check if the file is being watched by Next.js (some directories might be excluded).
2. Try restarting the development server.
3. Check if your editor is properly saving files.
4. Ensure you don't have conflicting extensions or tools that might interfere with file watching.

## Build Issues

### Out of memory during build

**Problem**: The build process fails with an out of memory error.

**Solution**:

1. Increase Node.js memory limit:

   ```bash
   NODE_OPTIONS='--max_old_space_size=4096' npm run build
   ```

2. Close other memory-intensive applications during build.

3. If on a server with limited resources, consider upgrading the server or using a build machine with more memory.

### TypeScript errors during build

**Problem**: The build fails due to TypeScript type errors.

**Solution**:

1. Fix the type errors in your code.
2. If you're confident the code works despite the type errors, you can temporarily bypass them:

   ```bash
   # In package.json, modify the build script
   "build": "next build || true"
   ```

   Note: This is not recommended for production builds.

### Missing environment variables

**Problem**: Build fails due to missing environment variables.

**Solution**:

1. Ensure all required environment variables are set in your `.env.production` file.
2. Check that the environment file is being loaded correctly.
3. For CI/CD builds, ensure environment variables are set in the build environment.

## Authentication Issues

### OAuth provider errors

**Problem**: Authentication with Google or LinkedIn fails.

**Solution**:

1. Verify that your OAuth credentials are correct in the environment variables:
   - `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`
   - `LINKEDIN_CLIENT_ID` and `LINKEDIN_CLIENT_SECRET`

2. Ensure the redirect URIs are correctly configured in the OAuth provider's developer console:
   - For development: `http://localhost:3000/api/auth/callback/google` or `http://localhost:3000/api/auth/callback/linkedin`
   - For production: `https://onlyc2c.com/api/auth/callback/google` or `https://onlyc2c.com/api/auth/callback/linkedin`

3. Check that `NEXTAUTH_URL` is set correctly in your environment variables.

### JWT errors

**Problem**: JWT validation fails or tokens are not being generated correctly.

**Solution**:

1. Ensure `NEXTAUTH_SECRET` is set and is the same across restarts.
2. Verify that `PRIVATE_JWK` and `PUBLIC_JWK` are correctly formatted and valid.
3. Check for clock skew between servers if using multiple servers.
4. Clear browser cookies and try again.

## Database Issues

### Connection errors

**Problem**: The application fails to connect to MongoDB.

**Solution**:

1. Verify that the MongoDB connection string is correct in your environment variables.
2. Ensure your IP address is whitelisted in MongoDB Atlas (if using Atlas).
3. Check that the database user has the correct permissions.
4. Test the connection manually:

   ```bash
   mongo "mongodb+srv://username:<EMAIL>/database"
   ```

### Slow queries

**Problem**: Database operations are taking too long.

**Solution**:

1. Check if indexes are properly set up for frequently queried fields.
2. Review your queries for optimization opportunities.
3. Consider adding database query logging to identify slow queries.
4. For MongoDB Atlas, check if you need to upgrade your cluster tier.

## Email Issues

### Emails not sending

**Problem**: The application is not sending emails.

**Solution**:

1. Verify email configuration in environment variables:
   - `EMAIL_HOST`, `EMAIL_PORT`, `EMAIL_USER`, `EMAIL_PASSWORD`
   - `EMAIL_SECURE`, `EMAIL_FROM`, `EMAIL_FROM_NAME`

2. Check if the email service is accessible from your server.

3. For Gmail, ensure you're using an app password if 2FA is enabled.

4. Check the application logs for email-related errors.

5. Test the email service directly:

   ```bash
   # Using the test API
   curl -X POST http://localhost:3000/api/v1/email/test \
     -H "Content-Type: application/json" \
     -d '{"type":"welcome-candidate","email":"<EMAIL>"}'
   ```

### Email templates not rendering correctly

**Problem**: Emails are sending but the templates look broken.

**Solution**:

1. Ensure all template files exist in the correct location (`src/templates/emails/`).
2. Check that all required variables are being passed to the template.
3. Verify that Handlebars partials are registered correctly.
4. Test the template with different email clients.

## Deployment Issues

### GitHub Actions deployment failure

**Problem**: The GitHub Actions deployment workflow fails.

**Solution**:

1. Check the GitHub Actions logs for specific error messages.
2. Verify that all required secrets are correctly set in the repository settings:
   - `LINODE_SSH_KEY`
   - `LINODE_HOST`
   - `LINODE_USER`

3. Test SSH connection manually:

   ```bash
   ssh -i ~/.ssh/id_ed25519 root@your_linode_ip
   ```

4. Ensure the repository is accessible from the Linode server:

   ```bash
   ssh root@your_linode_ip "cd ~/onlyc2c-candidate-frontend && git fetch"
   ```

### PM2 process not starting

**Problem**: The PM2 process fails to start after deployment.

**Solution**:

1. Check PM2 logs for errors:

   ```bash
   pm2 logs onlyc2c-candidate
   ```

2. Verify that the build was successful:

   ```bash
   ls -la ~/onlyc2c-candidate-frontend/.next
   ```

3. Try starting the process manually:

   ```bash
   cd ~/onlyc2c-candidate-frontend
   pm2 start 'npx next start' --name onlyc2c-candidate
   ```

4. Check for environment variable issues:

   ```bash
   grep -v '^#' ~/onlyc2c-candidate-frontend/.env.production | grep -v '^$'
   ```

### Nginx configuration issues

**Problem**: The application is running but not accessible via the domain.

**Solution**:

1. Check Nginx configuration:

   ```bash
   nginx -t
   ```

2. Verify that the Nginx site is enabled:

   ```bash
   ls -la /etc/nginx/sites-enabled/
   ```

3. Check Nginx logs for errors:

   ```bash
   tail -f /var/log/nginx/error.log
   ```

4. Ensure the domain is pointing to the correct IP address:

   ```bash
   dig onlyc2c.com
   ```

5. Restart Nginx:

   ```bash
   systemctl restart nginx
   ```

### SSL certificate issues

**Problem**: SSL certificate errors or HTTPS not working.

**Solution**:

1. Check certificate status:

   ```bash
   certbot certificates
   ```

2. Try renewing the certificate:

   ```bash
   certbot renew --dry-run
   ```

3. If needed, obtain a new certificate:

   ```bash
   certbot --nginx -d onlyc2c.com -d www.onlyc2c.com
   ```

4. Verify Nginx is configured to use the certificate:

   ```bash
   grep -A 10 "ssl_certificate" /etc/nginx/sites-available/onlyc2c
   ```
