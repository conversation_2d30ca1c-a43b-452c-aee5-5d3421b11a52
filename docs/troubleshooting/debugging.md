# Debugging Guide

> _Last updated: May 2025_

This document provides techniques and tools for debugging the OnlyC2C application.

## Table of Contents

1. [Development Debugging](#development-debugging)
2. [Production Debugging](#production-debugging)
3. [API Debugging](#api-debugging)
4. [Database Debugging](#database-debugging)
5. [Email Debugging](#email-debugging)
6. [Performance Debugging](#performance-debugging)

## Development Debugging

### Console Logging

Use console logging for basic debugging:

```typescript
// Basic logging
console.log('Value:', someValue);

// Structured logging
console.log({
  action: 'userLogin',
  userId: user.id,
  timestamp: new Date().toISOString()
});

// Error logging
console.error('Error in authentication:', error);
```

### Browser DevTools

Use browser developer tools for frontend debugging:

1. **Elements Panel**: Inspect and modify the DOM
2. **Console Panel**: View logs and execute JavaScript
3. **Network Panel**: Monitor network requests
4. **Application Panel**: Inspect cookies, local storage, and session storage
5. **React DevTools**: Debug React components and state

### Next.js Debugging

Enable verbose build output:

```bash
# Show more detailed build information
NEXT_DEBUG_BUILD=true npm run build
```

Enable React strict mode for development:

```javascript
// next.config.js
module.exports = {
  reactStrictMode: true,
  // other config...
};
```

### TypeScript Debugging

Use TypeScript's type checking to catch errors early:

```bash
# Run TypeScript type checking
npx tsc --noEmit
```

Add explicit type annotations to clarify intent:

```typescript
function processUser(user: IUser): void {
  // Implementation
}
```

## Production Debugging

### Server Logs

Access server logs to diagnose issues:

```bash
# PM2 logs
pm2 logs onlyc2c-candidate

# Nginx access logs
tail -f /var/log/nginx/access.log

# Nginx error logs
tail -f /var/log/nginx/error.log
```

### Remote Debugging

Enable remote debugging for Node.js:

```bash
# Start the application with the inspector
NODE_OPTIONS='--inspect=0.0.0.0:9229' npm start
```

Then connect using Chrome DevTools or VS Code.

### Error Monitoring

Consider implementing error monitoring with services like:

- Sentry
- LogRocket
- New Relic

Example Sentry integration:

```typescript
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.ENV,
  tracesSampleRate: 1.0,
});
```

## API Debugging

### API Request Logging

Add middleware to log API requests:

```typescript
// src/middleware.ts
export function middleware(request: NextRequest) {
  console.log(`API Request: ${request.method} ${request.nextUrl.pathname}`);
  return NextResponse.next();
}
```

### Testing API Endpoints

Use tools like Postman, Insomnia, or curl to test API endpoints:

```bash
# GET request
curl -X GET https://onlyc2c.com/api/v1/jobs

# POST request with JSON body
curl -X POST https://onlyc2c.com/api/v1/jobs/search \
  -H "Content-Type: application/json" \
  -d '{"query":"software engineer"}'

# Request with authentication
curl -X GET https://onlyc2c.com/api/v1/applications \
  -H "Authorization: Bearer your_token_here"
```

### API Response Analysis

Analyze API responses for debugging:

```bash
# Pretty-print JSON response
curl -s https://onlyc2c.com/api/v1/jobs | jq

# Check HTTP status code
curl -I https://onlyc2c.com/api/v1/jobs
```

## Database Debugging

### MongoDB Shell

Connect to MongoDB for direct database operations:

```bash
# Connect to MongoDB
mongo "mongodb+srv://username:<EMAIL>/database"

# List collections
show collections

# Query documents
db.jobs.find({ active: true }).limit(5)

# Count documents
db.jobs.countDocuments({ job_type: "REMOTE" })
```

### Mongoose Debugging

Enable Mongoose debug mode to see queries:

```typescript
// Add to your database connection file
mongoose.set('debug', process.env.ENV !== 'PRODUCTION');
```

### Database Indexing

Check and optimize indexes:

```bash
# Show indexes on a collection
db.jobs.getIndexes()

# Create an index for frequently queried fields
db.jobs.createIndex({ job_type: 1, active: 1 })

# Analyze query performance
db.jobs.find({ job_type: "REMOTE", active: true }).explain("executionStats")
```

## Email Debugging

### Email Service Testing

Test the email service directly:

```typescript
import { emailService } from '@/services/EmailService';

// Test direct sending
await emailService.sendEmail({
  to: '<EMAIL>',
  subject: 'Test Email',
  html: '<p>This is a test email</p>'
});
```

### Email Template Testing

Test email templates with the test API:

```bash
curl -X POST http://localhost:3000/api/v1/email/test \
  -H "Content-Type: application/json" \
  -d '{"type":"welcome-candidate","email":"<EMAIL>"}'
```

### Email Queue Monitoring

Monitor the email queue:

```typescript
import { emailQueueService } from '@/utils/server/emailQueue';

// Get queue statistics
const stats = emailQueueService.getStats();
console.log('Email Queue Stats:', stats);
```

## Performance Debugging

### React Profiler

Use React Profiler to identify performance bottlenecks:

1. Open Chrome DevTools
2. Go to the Profiler tab in React DevTools
3. Record a session while performing the slow operation
4. Analyze component render times

### Network Performance

Analyze network performance:

1. Open Chrome DevTools
2. Go to the Network tab
3. Enable "Disable cache" and set throttling to simulate different network conditions
4. Reload the page and analyze request timings

### Memory Leaks

Identify memory leaks:

1. Open Chrome DevTools
2. Go to the Memory tab
3. Take a heap snapshot
4. Perform the suspected leaking operation
5. Take another heap snapshot
6. Compare snapshots to identify retained objects

### Server-Side Performance

Profile server-side performance:

```bash
# Install Node.js profiling tools
npm install -g clinic

# Run the application with profiling
clinic doctor -- node server.js
```

### Database Query Performance

Analyze slow database queries:

```bash
# Enable MongoDB profiling
db.setProfilingLevel(2, { slowms: 100 })

# Check profiling results
db.system.profile.find().sort({ ts: -1 }).limit(10)
```

## Debugging Tools

### Recommended VS Code Extensions

- **Debugger for Chrome**: Debug JavaScript code in Chrome
- **ESLint**: Lint JavaScript and TypeScript code
- **MongoDB for VS Code**: Connect to MongoDB and run queries
- **REST Client**: Send HTTP requests and view responses
- **Error Lens**: Show errors inline in the editor

### Command-Line Tools

- **curl**: Test HTTP endpoints
- **jq**: Process JSON data
- **htop**: Monitor system resources
- **lsof**: Check open files and ports
- **netstat**: Network statistics

### Browser Extensions

- **React Developer Tools**: Debug React components
- **Redux DevTools**: Debug Redux state (if using Redux)
- **Lighthouse**: Audit performance and best practices
- **Axe**: Test accessibility
