# API Key Management

> _Last updated: May 2025_

This document explains the API key utilities and management practices used in the OnlyC2C application.

## Overview

OnlyC2C uses API keys to secure certain endpoints, particularly those that perform administrative actions like sitemap submission. The API key utilities are implemented in `src/utils/server/apiKeyUtils.ts`.

## API Key Utilities

### Generation

The `generateApiKey()` function creates a secure, random API key using Node.js's crypto module:

```typescript
import crypto from 'crypto';

export function generateApiKey(): string {
    return crypto.randomBytes(32).toString('base64url');
}
```

This generates a cryptographically secure random string of 32 bytes, encoded in base64url format. Example output:

```text
8KNgrqOH0durIHD_kw4u-bundJJ-xGz0v-OPw_SEK7E
```

### Verification

The `verifyApiKey()` function securely compares a provided API key with the expected key:

```typescript
export async function verifyApiKey(provided: string, expected: string): Promise<boolean> {
    if (provided.length !== expected.length) {
        return false;
    }

    return crypto.timingSafeEqual(
        Buffer.from(provided),
        Buffer.from(expected)
    );
}
```

This function:

1. Checks if the lengths match (quick rejection for obvious mismatches)
2. Uses `crypto.timingSafeEqual()` to perform a constant-time comparison, which prevents timing attacks

## Generating New API Keys

To generate a new API key, use the built-in npm script:

```bash
npm run generate-api-key
```

This runs `src/scripts/generate-api-key.ts`, which outputs a new API key to the console:

```text
API_KEY: wx2p8EUY0AobE6KRlFyUuviQr_9A3FiYBDbW2lnOmbs
```

## Current Usage

### Cron Job API Protection

The primary use of API keys is to protect cron job endpoints, including sitemap submission:

```typescript
// src/app/api/cron/submit-sitemap/route.ts
import { verifyApiKey } from '@/utils/server/apiKeyUtils';

export async function POST(request: NextRequest) {
    const providedApiKey = request.headers.get('x-c2c-api-key');
    const expectedKey = process.env.X_CRON_API_KEY;

    if (!await verifyApiKey(providedApiKey, expectedKey)) {
        return BaseResponse.unauthorized('Invalid API key');
    }
    // Process sitemap submission...
}
```

### Environment Setup

The API key must be set in the environment variables:

```env
X_CRON_API_KEY=your_generated_key_here
```

For GitHub Actions workflows, the API key should be stored as a repository secret named `X_CRON_API_KEY`.

### Making API Requests

When calling protected endpoints, include the API key in the request headers:

```typescript
const response = await fetch('/api/cron/submit-sitemap', {
    method: 'POST',
    headers: {
        'x-c2c-api-key': process.env.X_CRON_API_KEY
    }
});
```

Or using curl:

```bash
curl -X POST https://onlyc2c.com/api/cron/submit-sitemap \
  -H "x-c2c-api-key: your_api_key_here"
```

## Security Best Practices

### API Key Storage

- **Never commit API keys to version control**
- Store keys in environment variables
- For production, use secure environment variable management
- For GitHub Actions, use repository secrets

### API Key Transmission

- Always use HTTPS for API requests
- Transmit API keys in headers, not in URLs
- Use custom headers (e.g., `x-c2c-api-key`) rather than standard Authorization headers

### API Key Rotation

- Periodically generate new API keys
- Update all services and configurations when rotating keys
- Consider implementing a grace period where both old and new keys work

### Access Control

- Limit API key access to only necessary personnel
- Use different API keys for different environments (development, staging, production)
- Consider implementing API key scopes for more granular access control

## Troubleshooting

### Invalid API Key Errors

If you receive "Invalid API key" errors:

1. Verify that the API key is correctly set in your environment variables
2. Check that the API key is being correctly included in the request headers
3. Ensure there are no whitespace or encoding issues with the API key
4. Generate a new API key if necessary

### Security Concerns

If you suspect an API key has been compromised:

1. Generate a new API key immediately
2. Update all services and configurations with the new key
3. Monitor for unauthorized access
4. Review application logs for suspicious activity
